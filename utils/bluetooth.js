import store from '@/store'
import * as tsc from '@/utils/print/tsc';
import * as cpcl from '@/utils/print/cpcl';
var config = require('../common/config.js');
let viewCertificateUrl = config.BASE_PATH; //公共路径
let openBluetoothAdapter = function () {
    return new Promise((resolve, reject) => {
        uni.openBluetoothAdapter({
            success(res) {
                console.log("打开 蓝牙模块");
                resolve(res)
            },
            fail(res) {
                console.log("打开 蓝牙模块错误" + res);
                reject(res)
            }
        })
    })
};

let getAdapterState = function() {
    return new Promise((resolve, reject) => {
        uni.getBluetoothAdapterState({
            success: (res) => {
               store.commit("setBluetoothAvailable", res.available);
               store.commit("setBluetoothDiscovering", res.discovering);
               resolve();
            },
            fail: (res) => {
                store.commit("setBluetoothAvailable", false);
                store.commit("setBluetoothDiscovering", false);
                reject();
            }
        })
    })
}

let searchDevice = function() {
    return new Promise((resolve, reject) => {
        console.log("开始搜寻附近的蓝牙外围设备");
        uni.startBluetoothDevicesDiscovery({
            allowDuplicatesKey: true,
            success(res) {
                console.log(res);
                resolve(res)
            },
            fail(res) {
                console.error(res);
                reject(res)
            }
        })



    })
};

let connectDevices = function (device) {
    return new Promise((resolve, reject) => {
        console.log("连接蓝牙---------------" + device.deviceId)
        uni.createBLEConnection({
            deviceId: device.deviceId,
            success(res) {
                if (res.errMsg === "createBLEConnection:ok") {
                    console.log("连接蓝牙-[" + device.name + "]--成功");
                    store.commit("setBleDevice", device);
                    uni.showToast({
                        title: '连接成功',
                        icon: "none",
                        mask: true,
                        duration: 3000
                    });
                    setTimeout(() => {
                        getBleServices(device.deviceId).then(res => {
                            Object.assign(device, res);
                            store.commit("setBleDevice", device);
                            resolve(device);
                        }).catch(error => {
                            disconnetDevices();
                            uni.showToast({
                                title: '设备连接失败',
                                icon: "none",
                                mask: true,
                                duration: 1500
                            });
                            reject(error)
                        });
                    }, 3000)
                } else {
                    console.log(res);
                    disconnetDevices();
                    uni.showToast({
                        title: '设备连接失败',
                        icon: "none",
                        mask: true,
                        duration: 1500
                    });
                    reject(res)
                }
                //连接成功 关闭搜索
                stopFindBule();
            },
            fail(res) {
                console.log("连接蓝牙-[" + device.name + "]--失败")
                store.commit("setBleDevice", {});
                reject(res)
            }
        })
    })
};

let disconnetDevices = function() {
	return new Promise((resolve, reject) => {
	    let bleDevice = store.state.bleDevice;
	    if (!bleDevice || !bleDevice.deviceId) {
	        resolve();
        }
        uni.closeBLEConnection({
            deviceId: bleDevice.deviceId,
            success(res) {
                console.log("关闭连接设备" + bleDevice.deviceId + "成功")
                store.commit("setBleDevice", {});
                resolve();
            },
            fail(error) {
                if (error.errCode === 10006) {
                    store.commit("setBleDevice", {});
                    resolve()
                }
                store.commit("setBleDevice", {});
                reject(error);
            }
        })
    })
}

let getBleServices = function (deviceId) {
    return new Promise((resolve, reject) => {
        console.log("获取蓝牙设备所有服务(service)。---------------")
        uni.getBLEDeviceServices({
            // 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
            deviceId: deviceId,
            success(res) {
                console.log(res);
                for (var s = 0; s < res.services.length; s++) {
                    let serviceId = res.services[s].uuid;
                    uni.getBLEDeviceCharacteristics({
                        // 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
                        deviceId: deviceId,
                        // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
                        serviceId: serviceId,
                        success(res) {
                            for (let c = 0; c < res.characteristics.length; c++) {
                                if (res.characteristics[c].properties.write === true) {
                                    let uuid = res.characteristics[c].uuid;
                                    console.log('找到可写入的特征值:deviceId = [' + deviceId + ']  serviceId = [' + serviceId + '] characteristics=[' +
                                        uuid);
                                    resolve({
                                        serviceId: serviceId,
                                        characteristicId: uuid
                                    });
                                    break;
                                }
                            }
                        },
                        fail(error) {
                            reject(error);
                        }
                    })
                }
            },
            fail(res) {
                console.log(res);
                reject(res)
            },
        })
    })
};

let stopFindBule = function () {
    return new Promise((resolve, reject) => {
        console.log("停止搜寻附近的蓝牙外围设备---------------");
        uni.stopBluetoothDevicesDiscovery({
            success(res) {
                resolve(res)
            }
        })
    })
};

let closeBluetoothAdapter = function () {
    return new Promise((resolve, reject) => {
        uni.closeBluetoothAdapter({
            success(res) {
                console.log(res)
            }
        })
    })
};

let senBlData = function (uint8Array) {
    return new Promise((resolve, reject) => {
        console.log("调用打印方法");
        let bleDevice = store.state.bleDevice;
        if (!bleDevice.deviceId) {
            console.log("未连接打印机");
            reject("未连接打印机");
        }
        var uint8Buf = Array.from(uint8Array);

        var sendloop = split_array(uint8Buf, 20);
        // console.log(sendloop.length)

        var i = 0;
        realWriteData(sendloop, i);
        uni.showToast({
            title: '打印数据已发送',
            icon: "none",
            mask: true,
            duration: 1500,
            success(){
                setTimeout(() => {
                    resolve()
                }, 1500)
            }
        });
    })

};
let realWriteData = function(sendloop, i) {
    let device = store.state.bleDevice;
    var data = sendloop[i]
    if(typeof(data) == "undefined"){
        return
    }
    console.log("第【" + i + "】次写数据"+data)
    var buffer = new ArrayBuffer(data.length)
    var dataView = new DataView(buffer)
    for (var j = 0; j < data.length; j++) {
        dataView.setUint8(j, data[j]);
    }
    uni.writeBLECharacteristicValue({
        deviceId: device.deviceId,
        serviceId: device.serviceId,
        characteristicId: device.characteristicId,
        value: buffer,
        success(res) {
			setTimeout(() => {
				realWriteData(sendloop, i + 1);
			}, 10);
        }, fail(error) {
            uni.showToast({
                title: '打印失败，请重试',
                icon: "none",
                mask: true,
                duration: 1500,
                success() {
                    setTimeout(() => {
                    }, 1500)
                }
            });
        }
    })
}

function split_array(datas,size){
    var result = {};
    var j = 0
    for (var i = 0; i < datas.length; i += size) {
        result[j] = datas.slice(i, i + size)
        j++
    }
    console.log(result)
    return result
}

//80*60样式
function senBleLabel() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = tsc.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)
            command.setSize(80, 60);
            command.setCls();
			command.setDirection(1);
            command.setText(16 * 8, 1.5 * 8, "TSS24.BF2", 2, 2, "食用农产品合格证");
            command.setText(35 * 8, 8 * 8, "TSS24.BF2", 1, 1, "编号：" + no.fullNumber);
            command.setText(3.5 * 8, 11 * 8, "TSS24.BF2", 1, 1, "食用农产品名称：" + printData.productName);
            command.setText(3.5 * 8, 15 * 8, "TSS24.BF2", 1, 1, "数量（重量）：" + printData.productNum + printData.productUnitName);
            command.setText(3.5 * 8, 19 * 8, "TSS24.BF2", 1, 1, "生产者：" + printData.entName);
            command.setText(3.5 * 8, 23 * 8, "TSS24.BF2", 1, 1, "联系方式：" + printData.entContactsPhone);
            command.setText(3.5 * 8, 27 * 8, "TSS24.BF2", 1,1 , "开具日期："+ printData.createDate.substring(0,10));
            command.setText(3.5 * 8, 31 * 8, "TSS24.BF2", 1, 1, "产地：" + printData.productDetail);

            command.setText(3.5 * 8, 37 * 8, "TSS24.BF2", 1, 1, "我承诺对产品质量安全以及合格证真实性负责");
            command.setText(3.5 * 8, 41 * 8, "TSS24.BF2", 1, 1, "·不使用禁限用农药兽药");
            command.setText(3.5 * 8, 45 * 8, "TSS24.BF2", 1, 1, "·不使用非法添加物");
            command.setText(3.5 * 8, 49 * 8, "TSS24.BF2", 1, 1, "·遵守农药安全间隔期、兽药休药期规定");
            /* command.setText(3.5 * 8, 53 * 8, "TSS24.BF2", 1, 1, "·销售的食用农产品符合农药兽药残留食品安全国家标准"); */


            command.setText(59 * 8, 27 * 8, "TSS24.BF2", 1, 1, "扫描二维码");
            command.setText(58 * 8, 30.5 * 8, "TSS24.BF2", 1, 1, "查看更多信息");

            command.setQR(59 * 8, 12.5 * 8, "L", 3, "A", viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
            command.setPagePrint(1);
        })


        senBlData(command.getData()).then(res => {
            resolve()
        });
    })

}

//白山市合格证(8*8cm)-D45BT
function senBle80LabelD45BT() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = tsc.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)
			let detail = printData.productDetail;
            command.setSize(80, 80);
            command.setCls();
			command.setDirection(1);
            //command.setText(16 * 8, 1.5 * 8, "TSS24.BF2", 2, 2, "食用农产品合格证");
			//编号
            command.setText(144, 80, "TSS24.BF2", 1, 1, "编号：" + no.fullNumber);
			//产品名称
            command.setText(240, 165, "TSS24.BF2", 1, 1, printData.productName);
			//数量、单位
            command.setText(240, 205, "TSS24.BF2", 1, 1, printData.productNum + printData.productUnitName);
			//主体名称
            command.setText(240, 255, "TSS24.BF2", 1, 1, printData.entName);
			//联系方式
            command.setText(240, 305, "TSS24.BF2", 1, 1, printData.entContactsPhone);			
			//产地
			if(detail.length>17){
				command.setText(240, 345, "TSS24.BF2", 1, 1, detail.substring(0,17));
				command.setText(240, 385, "TSS24.BF2", 1, 1, detail.substring(17,printData.productDetail.length));
			}else{
				command.setText(240, 345, "TSS24.BF2", 1, 1, detail);
			}
			//开具日期
            command.setText(240, 420, "TSS24.BF2", 1,1 , printData.createDate.substring(0,10));          
			
			//二维码
			command.setQR(484, 465, "L", 3, "A", viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
            command.setText(472, 575, "TSS24.BF2", 1, 1, "扫描二维码");
            command.setText(464, 605, "TSS24.BF2", 1, 1, "查看更多信息");

            
            command.setPagePrint(1);
        })


        senBlData(command.getData()).then(res => {
            resolve()
        });
    })

}
//长春市合格证(7*6cm)-D45BT
function senBle76LabelD45BT() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = tsc.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)
			let detail = printData.productDetail;
            command.setSize(70, 60);
            command.setCls();
			command.setDirection(1);
            //command.setText(16 * 8, 1.5 * 8, "TSS24.BF2", 2, 2, "食用农产品合格证");
			//编号
			command.setText(115, 60, "TSS24.BF2", 1, 1, "编号：" + no.fullNumber);
			//产品名称
			command.setText(200, 110, "TSS24.BF2", 1, 1, printData.productName);
			//数量、单位
			command.setText(200, 140, "TSS24.BF2", 1, 1, printData.productNum + printData.productUnitName);
			//主体名称
			let lineLength=13;
			let entName = printData.entName;
			if(entName.length>lineLength){
				command.setText(200, 170, "TSS24.BF2", 1, 1, entName.substring(0,lineLength));
				command.setText(200, 200, "TSS24.BF2", 1, 1, entName.substring(lineLength,printData.entName.length));
			}else{
				command.setText(200, 170, "TSS24.BF2", 1, 1, entName);
			}
			
			//联系方式
			command.setText(200, 225, "TSS24.BF2", 1, 1, printData.entContactsPhone);			
			
			//产地
			if(detail.length>lineLength){
				command.setText(200, 250, "TSS24.BF2", 1, 1, detail.substring(0,lineLength));
				command.setText(200, 280, "TSS24.BF2", 1, 1, detail.substring(lineLength,printData.productDetail.length));
			}else{
				command.setText(200, 250, "TSS24.BF2", 1, 1, detail);
			}
			//开具日期 
			command.setText(200	, 310, "TSS24.BF2", 1,1 , printData.createDate.substring(0,10));          
			
			//二维码
			command.setQR(424, 300, "L", 3, "A", viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
			command.setText(412, 410, "TSS24.BF2", 1, 1, "扫描二维码");
			command.setText(404, 435, "TSS24.BF2", 1, 1, "查看更多信息");
            
            command.setPagePrint(1);
        })


        senBlData(command.getData()).then(res => {
            resolve()
        });
    })

}
//通用版合格证(7*6cm)-D45BT
function senBle76v2LabelD45BT() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = tsc.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)
			let detail = printData.productDetail;
            command.setSize(70, 60);
            command.setCls();
			command.setDirection(1);
            //command.setText(16 * 8, 1.5 * 8, "TSS24.BF2", 2, 2, "食用农产品合格证");
			//编号
			command.setText(115, 55, "TSS24.BF2", 1, 1, "编号：" + no.fullNumber);
			//产品名称
			command.setText(200, 95, "TSS24.BF2", 1, 1, printData.productName);
			//数量、单位
			command.setText(200, 130, "TSS24.BF2", 1, 1, printData.productNum + printData.productUnitName);
			//主体名称
			let lineLength=13;
			let entName = printData.entName;
			if(entName.length>lineLength){
				command.setText(200, 165, "TSS24.BF2", 1, 1, entName.substring(0,lineLength));
				command.setText(200, 195, "TSS24.BF2", 1, 1, entName.substring(lineLength,printData.entName.length));
			}else{
				command.setText(200, 180, "TSS24.BF2", 1, 1, entName);
			}
			//联系方式
			command.setText(200, 230, "TSS24.BF2", 1, 1, printData.entContactsPhone);			
			
			//产地
			if(detail.length>lineLength){
				command.setText(200, 255, "TSS24.BF2", 1, 1, detail.substring(0,lineLength));
				command.setText(200, 285, "TSS24.BF2", 1, 1, detail.substring(lineLength,printData.productDetail.length));
			}else{
				command.setText(200, 265, "TSS24.BF2", 1, 1, detail);
			}
			//开具日期 
			command.setText(200	, 320, "TSS24.BF2", 1,1 , printData.createDate.substring(0,10));          
			
			//二维码
			command.setQR(424, 300, "L", 3, "A", viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
			command.setText(412, 410, "TSS24.BF2", 1, 1, "扫描二维码");
			command.setText(404, 440, "TSS24.BF2", 1, 1, "查看更多信息");
            
            command.setPagePrint(1);
        })


        senBlData(command.getData()).then(res => {
            resolve()
        });
    })

}
//通用版合格证(7*6cm)-M32B
function senBle76v2LabelM32B() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = tsc.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)
			let detail = printData.productDetail;
            command.setSize(70, 60);
            command.setCls();
			command.setDirection(1);
            //command.setText(16 * 8, 1.5 * 8, "TSS24.BF2", 2, 2, "食用农产品合格证");
			//编号
			command.setText(115, 45, "TSS24.BF2", 1, 1, "编号：" + no.fullNumber);
			//产品名称
			command.setText(230, 85, "TSS24.BF2", 1, 1, printData.productName);
			//数量、单位
			command.setText(230, 120, "TSS24.BF2", 1, 1, printData.productNum + printData.productUnitName);
			//主体名称
			let lineLength=13;
			let entName = printData.entName;
			if(entName.length>lineLength){
				command.setText(230, 155, "TSS24.BF2", 1, 1, entName.substring(0,lineLength));
				command.setText(230, 185, "TSS24.BF2", 1, 1, entName.substring(lineLength,printData.entName.length));
			}else{
				command.setText(230, 170, "TSS24.BF2", 1, 1, entName);
			}
			//联系方式
			command.setText(230, 220, "TSS24.BF2", 1, 1, printData.entContactsPhone);			
			
			//产地
			if(detail.length>lineLength){
				command.setText(230, 245, "TSS24.BF2", 1, 1, detail.substring(0,lineLength));
				command.setText(230, 275, "TSS24.BF2", 1, 1, detail.substring(lineLength,printData.productDetail.length));
			}else{
				command.setText(230, 255, "TSS24.BF2", 1, 1, detail);
			}
			//开具日期 
			command.setText(230	, 310, "TSS24.BF2", 1,1 , printData.createDate.substring(0,10));          
			
			//二维码
			command.setQR(424, 300, "L", 3, "A", viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
			//command.setText(412, 410, "TSS24.BF2", 1, 1, "扫描二维码");
			command.setText(404, 440, "TSS24.BF2", 1, 1, "查看更多信息");
            
            command.setPagePrint(1);
        })


        senBlData(command.getData()).then(res => {
            resolve()
        });
    })

}

//长春市合格证(7*6cm)-M32B
function senBle76LabelM32B() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = tsc.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)
			let detail = printData.productDetail;
            command.setSize(70, 60);
            command.setCls();
			command.setDirection(1);
			
			//编号
            command.setText(130, 50, "TSS24.BF2", 1, 1, "编号：" + no.fullNumber);
			//产品名称
            command.setText(230, 100, "TSS24.BF2", 1, 1, printData.productName);
			//数量、单位
            command.setText(230, 130, "TSS24.BF2", 1, 1, printData.productNum + printData.productUnitName);
			//主体名称
			let lineLength=13;
			let entName = printData.entName;
			if(entName.length>lineLength){
				command.setText(230, 160, "TSS24.BF2", 1, 1, entName.substring(0,lineLength));
				command.setText(230, 190, "TSS24.BF2", 1, 1, entName.substring(lineLength,printData.entName.length));
			}else{
				command.setText(230, 160, "TSS24.BF2", 1, 1, entName);
			}
			
			//联系方式
            command.setText(230, 215, "TSS24.BF2", 1, 1, printData.entContactsPhone);			
			
			//产地
			if(detail.length>13){
				command.setText(230, 245, "TSS24.BF2", 1, 1, detail.substring(0,13));
				command.setText(230, 275, "TSS24.BF2", 1, 1, detail.substring(13,printData.productDetail.length));
			}else{
				command.setText(230, 245, "TSS24.BF2", 1, 1, detail);
			}
			//开具日期 
            command.setText(230	, 305, "TSS24.BF2", 1,1 , printData.createDate.substring(0,10));          
			
			//二维码
			command.setQR(434, 300, "L", 3, "A", viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
            //command.setText(422, 410, "TSS24.BF2", 1, 1, "扫描二维码");
            command.setText(414, 435, "TSS24.BF2", 1, 1, "查看更多信息");
			
            command.setPagePrint(1);
        })
        senBlData(command.getData()).then(res => {
            resolve()
        });
    })
}
//白山市合格证(7*6cm)-M32B
function senBleBs76LabelM32B() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = tsc.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)
			let detail = printData.productDetail;
            command.setSize(70, 60);
            command.setCls();
			command.setDirection(1);
			//编号
            command.setText(130, 50, "TSS24.BF2", 1, 1, "编号：" + no.fullNumber);
			//产品名称
            command.setText(230, 120, "TSS24.BF2", 1, 1, printData.productName);
			//数量、单位
            command.setText(230, 155, "TSS24.BF2", 1, 1, printData.productNum + printData.productUnitName);
			//主体名称
            command.setText(230, 190, "TSS24.BF2", 1, 1, printData.entName);
			//联系方式
            command.setText(230, 220, "TSS24.BF2", 1, 1, printData.entContactsPhone);			
			
			//产地
			if(detail.length>13){
				command.setText(230, 250, "TSS24.BF2", 1, 1, detail.substring(0,13));
				command.setText(230, 280, "TSS24.BF2", 1, 1, detail.substring(13,printData.productDetail.length));
			}else{
				command.setText(230, 250, "TSS24.BF2", 1, 1, detail);
			}
			//开具日期
            command.setText(230	, 305, "TSS24.BF2", 1,1 , printData.createDate.substring(0,10));          
			
			//二维码
			command.setQR(434, 300, "L", 3, "A", viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
            //command.setText(422, 410, "TSS24.BF2", 1, 1, "扫描二维码");
            command.setText(414, 435, "TSS24.BF2", 1, 1, "查看更多信息");
			
            command.setPagePrint(1);
        })
        senBlData(command.getData()).then(res => {
            resolve()
        });
    })
}
//白山市合格证(7*6cm)-D45BT
function senBleBs76LabelD45BT() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = tsc.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)
			let detail = printData.productDetail;
            command.setSize(70, 60);
            command.setCls();
			command.setDirection(1);
			//编号
            command.setText(115, 60, "TSS24.BF2", 1, 1, "编号：" + no.fullNumber);
			//产品名称
            command.setText(200, 120, "TSS24.BF2", 1, 1, printData.productName);
			//数量、单位
            command.setText(200, 155, "TSS24.BF2", 1, 1, printData.productNum + printData.productUnitName);
			//主体名称
            command.setText(200, 190, "TSS24.BF2", 1, 1, printData.entName);
			//联系方式
            command.setText(200, 220, "TSS24.BF2", 1, 1, printData.entContactsPhone);			
			
			//产地
			if(detail.length>13){
				command.setText(200, 250, "TSS24.BF2", 1, 1, detail.substring(0,13));
				command.setText(200, 280, "TSS24.BF2", 1, 1, detail.substring(13,printData.productDetail.length));
			}else{
				command.setText(200, 250, "TSS24.BF2", 1, 1, detail);
			}
			//开具日期
            command.setText(200	, 305, "TSS24.BF2", 1,1 , printData.createDate.substring(0,10));          
			
			//二维码
			command.setQR(420, 300, "L", 3, "A", viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
            command.setText(405, 410, "TSS24.BF2", 1, 1, "扫描二维码");
            command.setText(395, 435, "TSS24.BF2", 1, 1, "查看更多信息");
			
            command.setPagePrint(1);
        })
        senBlData(command.getData()).then(res => {
            resolve()
        });
    })
}
//人参合格证(7*6cm)-D45BT
function senBle76v3LabelD45BT() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = tsc.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)
    		let detail = printData.productDetail;
            command.setSize(70, 60);
            command.setCls();
    		command.setDirection(1);
    		let lineLength=13;
    		let entName = printData.entName;
    		//编号
    		command.setText(115, 50, "TSS24.BF2", 1, 1, "编号：" + no.fullNumber);
    		
    		//生产主体名称
    		if(entName.length>lineLength){
    			command.setText(200, 85, "TSS24.BF2", 1, 1, entName.substring(0,lineLength));
    			command.setText(200, 110, "TSS24.BF2", 1, 1, entName.substring(lineLength,printData.entName.length));
    		}else{
    			command.setText(200, 90, "TSS24.BF2", 1, 1, entName);
    		}
    		
    		//数量、单位
    		command.setText(200, 135, "TSS24.BF2", 1, 1, printData.productNum + printData.productUnitName);
    		//生产主体负责人
    		if(entName.length>lineLength){
    			command.setText(200, 160, "TSS24.BF2", 1, 1, entName.substring(0,lineLength));
    			command.setText(200, 185, "TSS24.BF2", 1, 1, entName.substring(lineLength,printData.entName.length));
    		}else{
    			command.setText(200, 175, "TSS24.BF2", 1, 1, entName);
    		}
    		//联系方式
    		command.setText(200, 215, "TSS24.BF2", 1, 1, printData.entContactsPhone);			
    		
    		//产地
    		if(detail.length>lineLength){
    			command.setText(200, 240, "TSS24.BF2", 1, 1, detail.substring(0,lineLength));
    			command.setText(200, 265, "TSS24.BF2", 1, 1, detail.substring(lineLength,printData.productDetail.length));
    		}else{
    			command.setText(200, 240, "TSS24.BF2", 1, 1, detail);
    		}
    		
    		//检测情况
    		let inspectionSituationResult="";
    		if(printData.inspectionSituation=="is01"){
    			inspectionSituationResult="无检测信息";
    		}else if(printData.inspectionSituation=="is02" || printData.inspectionSituation=="is04"){
    			inspectionSituationResult="自检合格";
    		}else if(printData.inspectionSituation=="is03"){
    			inspectionSituationResult="委托检测合格";
    		}
    		command.setText(200, 290, "TSS24.BF2", 1, 1, inspectionSituationResult);
    		
    		//开具日期 
    		command.setText(200	, 320, "TSS24.BF2", 1,1 , printData.createDate.substring(0,10));          
    		
    		//二维码
    		command.setQR(425, 300, "L", 3, "A", viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
    		command.setText(415, 410, "TSS24.BF2", 1, 1, "扫描二维码");
    		command.setText(407, 440, "TSS24.BF2", 1, 1, "查看更多信息");
            
            command.setPagePrint(1);
        })
        senBlData(command.getData()).then(res => {
            resolve()
        });
    })

}
//人参合格证(7*6cm)-M32B
function senBle76v3LabelM32B() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = tsc.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)
			let detail = printData.productDetail;
            command.setSize(70, 60);
            command.setCls();
			command.setDirection(1);
			let lineLength=13;
			let entName = printData.entName;
			//编号
			command.setText(115, 45, "TSS24.BF2", 1, 1, "编号：" + no.fullNumber);
			
			//生产主体名称
			if(entName.length>lineLength){
				command.setText(230, 80, "TSS24.BF2", 1, 1, entName.substring(0,lineLength));
				command.setText(230, 105, "TSS24.BF2", 1, 1, entName.substring(lineLength,printData.entName.length));
			}else{
				command.setText(230, 85, "TSS24.BF2", 1, 1, entName);
			}
			
			//数量、单位
			command.setText(230, 135, "TSS24.BF2", 1, 1, printData.productNum + printData.productUnitName);
			//生产主体负责人
			if(entName.length>lineLength){
				command.setText(230, 160, "TSS24.BF2", 1, 1, entName.substring(0,lineLength));
				command.setText(230, 185, "TSS24.BF2", 1, 1, entName.substring(lineLength,printData.entName.length));
			}else{
				command.setText(230, 175, "TSS24.BF2", 1, 1, entName);
			}
			//联系方式
			command.setText(230, 215, "TSS24.BF2", 1, 1, printData.entContactsPhone);			
			
			//产地
			if(detail.length>lineLength){
				command.setText(230, 240, "TSS24.BF2", 1, 1, detail.substring(0,lineLength));
				command.setText(230, 265, "TSS24.BF2", 1, 1, detail.substring(lineLength,printData.productDetail.length));
			}else{
				command.setText(230, 240, "TSS24.BF2", 1, 1, detail);
			}
			
			//检测情况
			let inspectionSituationResult="";
			if(printData.inspectionSituation=="is01"){
				inspectionSituationResult="无检测信息";
			}else if(printData.inspectionSituation=="is02" || printData.inspectionSituation=="is04"){
				inspectionSituationResult="自检合格";
			}else if(printData.inspectionSituation=="is03"){
				inspectionSituationResult="委托检测合格";
			}
			command.setText(230, 290, "TSS24.BF2", 1, 1, inspectionSituationResult);
			
			//开具日期 
			command.setText(230	, 320, "TSS24.BF2", 1,1 , printData.createDate.substring(0,10));          
			
			//二维码
			command.setQR(425, 300, "L", 3, "A", viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
			//command.setText(415, 410, "TSS24.BF2", 1, 1, "扫描二维码");
			command.setText(407, 440, "TSS24.BF2", 1, 1, "查看更多信息");
            
            command.setPagePrint(1);
        })
        senBlData(command.getData()).then(res => {
            resolve()
        });
    })

}

//承诺达标合格证(7*7cm)-M32B
function senBle77v1LabelM32B() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = tsc.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)
			let detail = printData.productDetail;
            command.setSize(70, 70);
            command.setCls();
			command.setDirection(1);
			let lineLength=13;
			//编号
			command.setText(115, 55, "TSS24.BF2", 1, 1, "编号：" + no.fullNumber);
			
			//检测情况
			for(let i=0;i< printData.inspectionSituationList.length;i++){
				let inspectionSituation = printData.inspectionSituationList[i]
				let inspectionWidth=0;
				if(inspectionSituation=="is01"){
					inspectionWidth=450;
				}else if(inspectionSituation=="is02"){//快检合格
					//inspectionWidth=0;
				}else if(inspectionSituation=="is03"){//委托检测合格
					inspectionWidth=45;
				}else if(inspectionSituation=="is04"){//自行检测合格
					inspectionWidth=165;
				}else if(inspectionSituation=="is05"){//质量安全控制符合要求
					inspectionWidth=290;
				}
				command.setText(inspectionWidth, 235, "TSS24.BF2", 1, 1, "√");
			} 
			
			//产品名称
			command.setText(230, 300, "TSS24.BF2", 1, 1, printData.productName);
			
			//数量、单位
			command.setText(230, 340, "TSS24.BF2", 1, 1, printData.productNum + printData.productUnitName);
			
			//产地
			if(detail.length>lineLength){
				command.setText(230, 365, "TSS24.BF2", 1, 1, detail.substring(0,lineLength));
				command.setText(230, 390, "TSS24.BF2", 1, 1, detail.substring(lineLength,printData.productDetail.length));
			}else{
				command.setText(230, 370, "TSS24.BF2", 1, 1, detail);
			}
			
			//生产主体名称
			let nameLineLength=7;
			let entName = printData.entName;
			if(entName.length<=nameLineLength){
				command.setText(230, 430, "TSS24.BF2", 1, 1, entName);
			}else if(entName.length>nameLineLength && entName.length<=(2*nameLineLength)){
				command.setText(230, 415, "TSS24.BF2", 1, 1, entName.substring(0,nameLineLength));
				command.setText(230, 440, "TSS24.BF2", 1, 1, entName.substring(nameLineLength,entName.length));
			}else if(entName.length>(2*nameLineLength)){
				command.setText(230, 415, "TSS24.BF2", 1, 1, entName.substring(0,nameLineLength));
				command.setText(230, 440, "TSS24.BF2", 1, 1, entName.substring(nameLineLength,(2*nameLineLength)));
				command.setText(230, 465, "TSS24.BF2", 1, 1, entName.substring((2*nameLineLength),entName.length>(3*nameLineLength)?(3*nameLineLength):entName.length));
			}
			
			//联系方式
			command.setText(230, 490, "TSS24.BF2", 1, 1, printData.entContactsPhone);			
			
			//开具日期 
			command.setText(230	, 525, "TSS24.BF2", 1,1 , printData.createDate.substring(0,10));          
			
			//二维码
			command.setQR(425, 440, "L", 3, "A", viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
            
            command.setPagePrint(1);
        })
        senBlData(command.getData()).then(res => {
            resolve()
        });
    })
}
//承诺达标合格证(7*7cm)-D45BT
function senBle77v1LabelD45BT() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = tsc.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)
			let detail = printData.productDetail;
            command.setSize(70, 70);
            command.setCls();
			command.setDirection(1);
			let lineLength=13;
			//编号
			command.setText(115, 55, "TSS24.BF2", 1, 1, "编号：" + no.fullNumber);
			
			//检测情况
			for(let i=0;i< printData.inspectionSituationList.length;i++){
				let inspectionSituation = printData.inspectionSituationList[i]
				let inspectionWidth=0;
			
				if(inspectionSituation=="is01"){
					inspectionWidth=435;
				}else if(inspectionSituation=="is02"){//快检合格
					//inspectionWidth=0;
				}else if(inspectionSituation=="is03"){//委托检测合格
					inspectionWidth=25;
				}else if(inspectionSituation=="is04"){//自行检测合格
					inspectionWidth=145;
				}else if(inspectionSituation=="is05"){//质量安全控制符合要求
					inspectionWidth=270;
				}
				command.setText(inspectionWidth, 240, "TSS24.BF2", 1, 1, "√");
			} 
		
			//产品名称
			command.setText(200, 300, "TSS24.BF2", 1, 1, printData.productName);
			
			//数量、单位
			command.setText(200, 340, "TSS24.BF2", 1, 1, printData.productNum + printData.productUnitName);
			
			//产地
			if(detail.length>lineLength){
				command.setText(200, 365, "TSS24.BF2", 1, 1, detail.substring(0,lineLength));
				command.setText(200, 390, "TSS24.BF2", 1, 1, detail.substring(lineLength,printData.productDetail.length));
			}else{
				command.setText(200, 370, "TSS24.BF2", 1, 1, detail);
			}
			
			//生产主体名称
			let nameLineLength=7;
			let entName = printData.entName;
			if(entName.length<=nameLineLength){
				command.setText(200, 430, "TSS24.BF2", 1, 1, entName);
			}else if(entName.length>nameLineLength && entName.length<=(2*nameLineLength)){
				command.setText(200, 415, "TSS24.BF2", 1, 1, entName.substring(0,nameLineLength));
				command.setText(200, 440, "TSS24.BF2", 1, 1, entName.substring(nameLineLength,entName.length));
				
			}else if(entName.length>(2*nameLineLength)){
				command.setText(200, 415, "TSS24.BF2", 1, 1, entName.substring(0,nameLineLength));
				command.setText(200, 440, "TSS24.BF2", 1, 1, entName.substring(nameLineLength,(2*nameLineLength)));
				command.setText(200, 465, "TSS24.BF2", 1, 1, entName.substring((2*nameLineLength),entName.length>(3*nameLineLength)?(3*nameLineLength):entName.length));
			}
			
			//联系方式
			command.setText(200, 490, "TSS24.BF2", 1, 1, printData.entContactsPhone);			
			
			//开具日期 
			command.setText(200	, 525, "TSS24.BF2", 1,1 , printData.createDate.substring(0,10));          
			
			//二维码
			command.setQR(425, 440, "L", 3, "A", viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
            
            command.setPagePrint(1);
        })
        senBlData(command.getData()).then(res => {
            resolve()
        });
    })
}

//承诺达标合格证(7*7cm)-M32B  -v2
function senBle77v2LabelM32B() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = tsc.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)
			let detail = printData.productDetail;
            command.setSize(70, 70);
            command.setCls();
			command.setDirection(1);
			let lineLength=13;
			//编号
			command.setText(115, 55, "TSS24.BF2", 1, 1, "编号：" + no.fullNumber);
			
			//检测情况
			for(let i=0;i< printData.inspectionSituationList.length;i++){
				let inspectionSituation = printData.inspectionSituationList[i]
				let inspectionWidth=0;
				if(inspectionSituation=="is01"){
					inspectionWidth=450;
				}else if(inspectionSituation=="is02"){//快检合格
					//inspectionWidth=0;
				}else if(inspectionSituation=="is03"){//委托检测合格
					inspectionWidth=395;
				}else if(inspectionSituation=="is04"){//自行检测合格
					inspectionWidth=260;
				}else if(inspectionSituation=="is05"){//质量安全控制符合要求
					inspectionWidth=45;
				}
				command.setText(inspectionWidth, 235, "TSS24.BF2", 1, 1, "√");
			} 
			
			//产品名称
			command.setText(230, 300, "TSS24.BF2", 1, 1, printData.productName);
			
			//数量、单位
			command.setText(230, 340, "TSS24.BF2", 1, 1, printData.productNum + printData.productUnitName);
			
			//产地
			if(detail.length>lineLength){
				command.setText(230, 365, "TSS24.BF2", 1, 1, detail.substring(0,lineLength));
				command.setText(230, 390, "TSS24.BF2", 1, 1, detail.substring(lineLength,printData.productDetail.length));
			}else{
				command.setText(230, 370, "TSS24.BF2", 1, 1, detail);
			}
			
			//生产主体名称
			let nameLineLength=7;
			let entName = printData.entName;
			if(entName.length<=nameLineLength){
				command.setText(230, 430, "TSS24.BF2", 1, 1, entName);
			}else if(entName.length>nameLineLength && entName.length<=(2*nameLineLength)){
				command.setText(230, 415, "TSS24.BF2", 1, 1, entName.substring(0,nameLineLength));
				command.setText(230, 440, "TSS24.BF2", 1, 1, entName.substring(nameLineLength,entName.length));
			}else if(entName.length>(2*nameLineLength)){
				command.setText(230, 415, "TSS24.BF2", 1, 1, entName.substring(0,nameLineLength));
				command.setText(230, 440, "TSS24.BF2", 1, 1, entName.substring(nameLineLength,(2*nameLineLength)));
				command.setText(230, 465, "TSS24.BF2", 1, 1, entName.substring((2*nameLineLength),entName.length>(3*nameLineLength)?(3*nameLineLength):entName.length));
			}
			
			//联系方式
			command.setText(230, 490, "TSS24.BF2", 1, 1, printData.entContactsPhone);			
			
			//开具日期 
			command.setText(230	, 525, "TSS24.BF2", 1,1 , printData.createDate.substring(0,10));          
			
			//二维码
			command.setQR(425, 440, "L", 3, "A", viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
            
            command.setPagePrint(1);
        })
        senBlData(command.getData()).then(res => {
            resolve()
        });
    })
}
//承诺达标合格证(7*7cm)-D45BT -V2
function senBle77v2LabelD45BT() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = tsc.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)
			let detail = printData.productDetail;
            command.setSize(70, 70);
            command.setCls();
			command.setDirection(1);
			let lineLength=13;
			//编号
			command.setText(115, 55, "TSS24.BF2", 1, 1, "编号：" + no.fullNumber);
			
			//检测情况
			
			for(let i=0;i< printData.inspectionSituationList.length;i++){
				let inspectionSituation = printData.inspectionSituationList[i]
				let inspectionWidth=0;
			
				if(inspectionSituation=="is01"){
					inspectionWidth=435;
				}else if(inspectionSituation=="is02"){//快检合格
					//inspectionWidth=0;
				}else if(inspectionSituation=="is03"){//委托检测合格
					inspectionWidth=370;
				}else if(inspectionSituation=="is04"){//自行检测合格
					inspectionWidth=235;
				}else if(inspectionSituation=="is05"){//质量安全控制符合要求
					inspectionWidth=20;
				}
				command.setText(inspectionWidth, 240, "TSS24.BF2", 1, 1, "√");
			} 
			
			//产品名称
			command.setText(200, 300, "TSS24.BF2", 1, 1, printData.productName);
			
			//数量、单位
			command.setText(200, 340, "TSS24.BF2", 1, 1, printData.productNum + printData.productUnitName);
			
			//产地
			if(detail.length>lineLength){
				command.setText(200, 365, "TSS24.BF2", 1, 1, detail.substring(0,lineLength));
				command.setText(200, 390, "TSS24.BF2", 1, 1, detail.substring(lineLength,printData.productDetail.length));
			}else{
				command.setText(200, 370, "TSS24.BF2", 1, 1, detail);
			}
			
			//生产主体名称
			let nameLineLength=7;
			let entName = printData.entName;
			if(entName.length<=nameLineLength){
				command.setText(200, 430, "TSS24.BF2", 1, 1, entName);
			}else if(entName.length>nameLineLength && entName.length<=(2*nameLineLength)){
				command.setText(200, 415, "TSS24.BF2", 1, 1, entName.substring(0,nameLineLength));
				command.setText(200, 440, "TSS24.BF2", 1, 1, entName.substring(nameLineLength,entName.length));
				
			}else if(entName.length>(2*nameLineLength)){
				command.setText(200, 415, "TSS24.BF2", 1, 1, entName.substring(0,nameLineLength));
				command.setText(200, 440, "TSS24.BF2", 1, 1, entName.substring(nameLineLength,(2*nameLineLength)));
				command.setText(200, 465, "TSS24.BF2", 1, 1, entName.substring((2*nameLineLength),entName.length>(3*nameLineLength)?(3*nameLineLength):entName.length));
			}
			
			//联系方式
			command.setText(200, 490, "TSS24.BF2", 1, 1, printData.entContactsPhone);			
			
			//开具日期 
			command.setText(200	, 525, "TSS24.BF2", 1,1 , printData.createDate.substring(0,10));          
			
			//二维码
			command.setQR(425, 440, "L", 3, "A", viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
            
            command.setPagePrint(1);
        })
        senBlData(command.getData()).then(res => {
            resolve()
        });
    })
}

//长春市合格证(7*6cm)-M22
function senBle76LabelM22() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = cpcl.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)

            command.setPageHeight(600);
            command.setPageWidth(700);
            var rotate="180";//字体旋转度数
            var font="56";//字体名称
            command.setText(rotate,font, 450, 415, 0, "编号：" + no.fullNumber);
            //产品名称
            command.setText(rotate,font, 320, 365, 0, printData.productName);
            //数量、单位
            command.setText(rotate,font, 320, 335, 0, printData.productNum + printData.productUnitName);

            let lineLength=13;
            //主体名称
            let entName = printData.entName;
            if(entName.length>lineLength){
                command.setText(rotate,font,320, 305,  0, entName.substring(0,lineLength));
                command.setText(rotate,font,320, 280,  0, entName.substring(lineLength,printData.entName.length));
            }else{
                command.setText(rotate,font,320, 305,  0, entName);
            }
            //联系方式
            command.setText(rotate,font,320, 250,  0, printData.entContactsPhone);

            //产地
            let detail = printData.productDetail;
            if(detail.length>lineLength){
                command.setText(rotate,font,320, 225,  0, detail.substring(0,lineLength));
                command.setText(rotate,font,320, 195,  0, detail.substring(lineLength,printData.productDetail.length));
            }else{
                command.setText(rotate,font,320, 225,  0, detail);
            }
            //开具日期
            command.setText(rotate,font,320, 170, 0 , printData.createDate.substring(0,10));

            //二维码
            command.setQR(25, 55, 3, viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
            command.setText(rotate,font,145, 50,  0, "扫描二维码");
            command.setText(rotate,font,155, 0,  0, "查看更多信息");

            command.setForm();
            command.setPagePrint(1);
        })

        senBlData(command.getData()).then(res => {
            resolve()
        });
    })

}
//白山市合格证(7*6cm)-M22
function senBleBs76LabelM22() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = cpcl.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)

            command.setPageHeight(600);
            command.setPageWidth(700);
            var rotate="180";//字体旋转度数
            var font="56";//字体名称
            command.setText(rotate,font, 450, 415, 0, "编号：" + no.fullNumber);
            //产品名称
            command.setText(rotate,font, 320, 355, 0, printData.productName);
            //数量、单位
            command.setText(rotate,font, 320, 320, 0, printData.productNum + printData.productUnitName);

            let lineLength=13;
            //主体名称
            let entName = printData.entName;
            command.setText(rotate,font,320, 285,  0, entName);

            //联系方式
            command.setText(rotate,font,320, 255,  0, printData.entContactsPhone);

            //产地
            let detail = printData.productDetail;
            if(detail.length>lineLength){
                command.setText(rotate,font,320, 225,  0, detail.substring(0,lineLength));
                command.setText(rotate,font,320, 195,  0, detail.substring(lineLength,printData.productDetail.length));
            }else{
                command.setText(rotate,font,320, 225,  0, detail);
            }
            //开具日期
            command.setText(rotate,font,320, 170, 0 , printData.createDate.substring(0,10));

            //二维码
            command.setQR(25, 55, 3, viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
            command.setText(rotate,font,145, 50,  0, "扫描二维码");
            command.setText(rotate,font,155, 0,  0, "查看更多信息");

            command.setForm();
            command.setPagePrint(1);
        })

        senBlData(command.getData()).then(res => {
            resolve()
        });
    })

}
//通用版合格证(7*6cm)-M22
function senBle76v2LabelM22() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = cpcl.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)

            command.setPageHeight(600);
            command.setPageWidth(700);
            var rotate="180";//字体旋转度数
            var font="56";//字体名称
            command.setText(rotate,font, 450, 420, 0, "编号：" + no.fullNumber);
            //产品名称
            command.setText(rotate,font, 320, 380, 0, printData.productName);
            //数量、单位
            command.setText(rotate,font, 320, 345, 0, printData.productNum + printData.productUnitName);

            let lineLength=13;
            //主体名称
            let entName = printData.entName;
            if(entName.length>lineLength){
                command.setText(rotate,font,320, 305,  0, entName.substring(0,lineLength));
                command.setText(rotate,font,320, 280,  0, entName.substring(lineLength,printData.entName.length));
            }else{
                command.setText(rotate,font,320, 295,  0, entName);
            }

            //联系方式
            command.setText(rotate,font,320, 245,  0, printData.entContactsPhone);

            //产地
            let detail = printData.productDetail;
            if(detail.length>lineLength){
                command.setText(rotate,font,320, 210,  0, detail.substring(0,lineLength));
                command.setText(rotate,font,320, 180,  0, detail.substring(lineLength,printData.productDetail.length));
            }else{
                command.setText(rotate,font,320, 210,  0, detail);
            }
            //开具日期
            command.setText(rotate,font,320, 155, 0 , printData.createDate.substring(0,10));

            //二维码
            command.setQR(25, 55, 3, viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
            command.setText(rotate,font,145, 50,  0, "扫描二维码");
            command.setText(rotate,font,155, 0,  0, "查看更多信息");

            command.setForm();
            command.setPagePrint(1);
        })

        senBlData(command.getData()).then(res => {
            resolve()
        });
    })

}
//人参合格证(7*6cm)-M22
function senBle76v3LabelM22() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = cpcl.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)

            command.setPageHeight(600);
            command.setPageWidth(700);
            var rotate="180";//字体旋转度数
            var font="56";//字体名称
            let lineLength=13;

            command.setText(rotate,font, 450, 425, 0, "编号：" + no.fullNumber);
            //生产主体名称
            let entName = printData.entName;
            if(entName.length>lineLength){
                command.setText(rotate,font,320, 395,  0, entName.substring(0,lineLength));
                command.setText(rotate,font,320, 370,  0, entName.substring(lineLength,printData.entName.length));
            }else{
                command.setText(rotate,font,320, 380,  0, entName);
            }

            //数量、单位
            command.setText(rotate,font, 320, 340, 0, printData.productNum + printData.productUnitName);

            //生产主体负责人
            if(entName.length>lineLength){
                command.setText(rotate,font,320, 315,  0, entName.substring(0,lineLength));
                command.setText(rotate,font,320, 290,  0, entName.substring(lineLength,printData.entName.length));
            }else{
                command.setText(rotate,font,320, 300,  0, entName);
            }

            //联系方式
            command.setText(rotate,font,320, 260,  0, printData.entContactsPhone);

            //产地
            let detail = printData.productDetail;
            if(detail.length>lineLength){
                command.setText(rotate,font,320, 235,  0, detail.substring(0,lineLength));
                command.setText(rotate,font,320, 210,  0, detail.substring(lineLength,printData.productDetail.length));
            }else{
                command.setText(rotate,font,320, 230,  0, detail);
            }

            //检测情况
            let inspectionSituationResult="";
            if(printData.inspectionSituation=="is01"){
                inspectionSituationResult="无检测信息";
            }else if(printData.inspectionSituation=="is02" || printData.inspectionSituation=="is04"){
                inspectionSituationResult="自检合格";
            }else if(printData.inspectionSituation=="is03"){
                inspectionSituationResult="委托检测合格";
            }
            command.setText(rotate,font,320, 185,  0, inspectionSituationResult);

            //开具日期
            command.setText(rotate,font,320, 155, 0 , printData.createDate.substring(0,10));

            //二维码
            command.setQR(25, 55, 3, viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);
            command.setText(rotate,font,145, 50,  0, "扫描二维码");
            command.setText(rotate,font,155, 0,  0, "查看更多信息");

            command.setForm();
            command.setPagePrint(1);
        })

        senBlData(command.getData()).then(res => {
            resolve()
        });
    })

}
//承诺达标合格证(7*7cm)-M22
function senBle77v1LabelM22() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = cpcl.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)

            command.setPageHeight(700);
            command.setPageWidth(700);
            var rotate="180";//字体旋转度数
            var font="56";//字体名称
            let lineLength=13;

            command.setText(rotate,font, 450, 500, 0, "编号：" + no.fullNumber);

            //检测情况
            //2023-11-15 新需求多选
            for(let i=0;i< printData.inspectionSituationList.length;i++){
                let inspectionSituation = printData.inspectionSituationList[i]
                let inspectionWidth=0;
                if(inspectionSituation =="is01"){
                    inspectionWidth=100;
                }else if(inspectionSituation=="is02"){//快检合格
                    //inspectionWidth=0;
                }else if(inspectionSituation=="is03"){//委托检测合格
                    inspectionWidth=505;
                }else if(inspectionSituation=="is04"){//自行检测合格
                    inspectionWidth=385;
                }else if(inspectionSituation=="is05"){//质量安全控制符合要求
                    inspectionWidth=260;
                }
                command.setText(rotate,font,inspectionWidth, 315,  0, "√");
            }

            //产品名称
            command.setText(rotate,font, 320, 255, 0, printData.productName);

            //数量、单位
            command.setText(rotate,font, 320, 215, 0, printData.productNum + printData.productUnitName);
            //产地
            let detail = printData.productDetail;
            if(detail.length>lineLength){
                command.setText(rotate,font,320, 190,  0, detail.substring(0,lineLength));
                command.setText(rotate,font,320, 165,  0, detail.substring(lineLength,printData.productDetail.length));
            }else{
                command.setText(rotate,font,320, 185,  0, detail);
            }

            //生产主体名称
            let nameLineLength=7;
            let entName = printData.entName;
            if(entName.length<=nameLineLength){
                command.setText(rotate,font,320, 125,  0, entName);
            }else if(entName.length>nameLineLength && entName.length<=(2*nameLineLength)){
                command.setText(rotate,font,320, 140,  0, entName.substring(0,nameLineLength));
                command.setText(rotate,font,320, 115,  0, entName.substring(nameLineLength,entName.length));
            }else if(entName.length>(2*nameLineLength)){
                command.setText(rotate,font,320, 140,  0, entName.substring(0,nameLineLength));
                command.setText(rotate,font,320, 115,  0, entName.substring(nameLineLength,(2*nameLineLength)));
                command.setText(rotate,font,320, 90,  0, entName.substring((2*nameLineLength),entName.length>(3*nameLineLength)?(3*nameLineLength):entName.length));
            }

            //联系方式
            command.setText(rotate,font,320, 65,  0, printData.entContactsPhone);

            //开具日期
            command.setText(rotate,font,320, 30, 0 , printData.createDate.substring(0,10));

            //二维码
            command.setQR(10, 5, 3, viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);

            command.setForm();
            command.setPagePrint(1);
        })

        senBlData(command.getData()).then(res => {
            resolve()
        });
    })

}

//承诺达标合格证(7*7cm)-E3PLUS
function senBle77v1LabelE3PLUS() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = cpcl.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)

            command.setPageHeight(550);
            command.setPageWidth(700);
            var rotate="180";//字体旋转度数
            var font="56";//字体名称
            let lineLength=13;

            command.setText(rotate,font, 450, 500, 0, "编号：" + no.fullNumber);

            //检测情况
            for(let i=0;i< printData.inspectionSituationList.length;i++){
                let inspectionSituation = printData.inspectionSituationList[i]
                let inspectionWidth=0;
                if(inspectionSituation=="is01"){
                    inspectionWidth=100;
                }else if(inspectionSituation=="is02"){//快检合格
                    //inspectionWidth=0;
                }else if(inspectionSituation=="is03"){//委托检测合格
                    inspectionWidth=510;
                }else if(inspectionSituation=="is04"){//自行检测合格
                    inspectionWidth=390;
                }else if(inspectionSituation=="is05"){//质量安全控制符合要求
                    inspectionWidth=265;
                }
                command.setText(rotate,font,inspectionWidth, 315,  0, "√");
            }

            //产品名称
            command.setText(rotate,font, 320, 255, 0, printData.productName);

            //数量、单位
            command.setText(rotate,font, 320, 220, 0, printData.productNum + printData.productUnitName);
            //产地
            let detail = printData.productDetail;
            if(detail.length>lineLength){
                command.setText(rotate,font,320, 190,  0, detail.substring(0,lineLength));
                command.setText(rotate,font,320, 165,  0, detail.substring(lineLength,printData.productDetail.length));
            }else{
                command.setText(rotate,font,320, 185,  0, detail);
            }

            //生产主体名称
            let nameLineLength=7;
            let entName = printData.entName;
            if(entName.length<=nameLineLength){
                command.setText(rotate,font,320, 125,  0, entName);
            }else if(entName.length>nameLineLength && entName.length<=(2*nameLineLength)){
                command.setText(rotate,font,320, 140,  0, entName.substring(0,nameLineLength));
                command.setText(rotate,font,320, 115,  0, entName.substring(nameLineLength,entName.length));
            }else if(entName.length>(2*nameLineLength)){
                command.setText(rotate,font,320, 140,  0, entName.substring(0,nameLineLength));
                command.setText(rotate,font,320, 115,  0, entName.substring(nameLineLength,(2*nameLineLength)));
                command.setText(rotate,font,320, 90,  0, entName.substring((2*nameLineLength),entName.length>(3*nameLineLength)?(3*nameLineLength):entName.length));
            }

            //联系方式
            command.setText(rotate,font,320, 65,  0, printData.entContactsPhone);

            //开具日期
            command.setText(rotate,font,320, 30, 0 , printData.createDate.substring(0,10));

            //二维码
            command.setQR(10, 15, 3, viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);

            command.setForm();
            command.setPagePrint(1);
        })

        senBlData(command.getData()).then(res => {
            resolve()
        });
    })

}

//承诺达标合格证(7*7cm)-M22  -V2
function senBle77v2LabelM22() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = cpcl.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)

            command.setPageHeight(700);
            command.setPageWidth(700);
            var rotate="180";//字体旋转度数
            var font="56";//字体名称
            let lineLength=13;

            command.setText(rotate,font, 450, 500, 0, "编号：" + no.fullNumber);

            //检测情况
            //2023-11-15 新需求多选
            for(let i=0;i< printData.inspectionSituationList.length;i++){
                let inspectionSituation = printData.inspectionSituationList[i]
                let inspectionWidth=0;
                if(inspectionSituation =="is01"){
                    inspectionWidth=100;
                }else if(inspectionSituation=="is02"){//快检合格
                    //inspectionWidth=0;
                }else if(inspectionSituation=="is03"){//委托检测合格
                    inspectionWidth=165;
                }else if(inspectionSituation=="is04"){//自行检测合格
                    inspectionWidth=304;
                }else if(inspectionSituation=="is05"){//质量安全控制符合要求
                    inspectionWidth=520;
                }
                command.setText(rotate,font,inspectionWidth, 315,  0, "√");
            }





            //产品名称
            command.setText(rotate,font, 320, 255, 0, printData.productName);

            //数量、单位
            command.setText(rotate,font, 320, 215, 0, printData.productNum + printData.productUnitName);
            //产地
            let detail = printData.productDetail;
            if(detail.length>lineLength){
                command.setText(rotate,font,320, 190,  0, detail.substring(0,lineLength));
                command.setText(rotate,font,320, 165,  0, detail.substring(lineLength,printData.productDetail.length));
            }else{
                command.setText(rotate,font,320, 185,  0, detail);
            }

            //生产主体名称
            let nameLineLength=7;
            let entName = printData.entName;
            if(entName.length<=nameLineLength){
                command.setText(rotate,font,320, 125,  0, entName);
            }else if(entName.length>nameLineLength && entName.length<=(2*nameLineLength)){
                command.setText(rotate,font,320, 140,  0, entName.substring(0,nameLineLength));
                command.setText(rotate,font,320, 115,  0, entName.substring(nameLineLength,entName.length));
            }else if(entName.length>(2*nameLineLength)){
                command.setText(rotate,font,320, 140,  0, entName.substring(0,nameLineLength));
                command.setText(rotate,font,320, 115,  0, entName.substring(nameLineLength,(2*nameLineLength)));
                command.setText(rotate,font,320, 90,  0, entName.substring((2*nameLineLength),entName.length>(3*nameLineLength)?(3*nameLineLength):entName.length));
            }

            //联系方式
            command.setText(rotate,font,320, 65,  0, printData.entContactsPhone);

            //开具日期
            command.setText(rotate,font,320, 30, 0 , printData.createDate.substring(0,10));

            //二维码
            command.setQR(10, 5, 3, viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);

            command.setForm();
            command.setPagePrint(1);
        })

        senBlData(command.getData()).then(res => {
            resolve()
        });
    })

}

//承诺达标合格证(7*7cm)-E3PLUS  -V2
function senBle77v2LabelE3PLUS() {
    return new Promise((resolve, reject) => {
        //标签模式f
        var printData = store.state.printDetailData;
        var command = cpcl.jpPrinter.createNew();
        printData.certificateNoList.forEach(no => {
            // console.log(command)

            command.setPageHeight(550);
            command.setPageWidth(700);
            var rotate="180";//字体旋转度数
            var font="56";//字体名称
            let lineLength=13;

            command.setText(rotate,font, 450, 500, 0, "编号：" + no.fullNumber);

            //检测情况
            for(let i=0;i< printData.inspectionSituationList.length;i++){
                let inspectionSituation = printData.inspectionSituationList[i]
                let inspectionWidth=0;
                if(inspectionSituation=="is01"){
                    inspectionWidth=100;
                }else if(inspectionSituation=="is02"){//快检合格
                    //inspectionWidth=0;
                }else if(inspectionSituation=="is03"){//委托检测合格
                    inspectionWidth=165;
                }else if(inspectionSituation=="is04"){//自行检测合格
                    inspectionWidth=304;
                }else if(inspectionSituation=="is05"){//质量安全控制符合要求
                    inspectionWidth=520;
                }
                command.setText(rotate,font,inspectionWidth, 315,  0, "√");
            }

            //产品名称
            command.setText(rotate,font, 320, 255, 0, printData.productName);

            //数量、单位
            command.setText(rotate,font, 320, 220, 0, printData.productNum + printData.productUnitName);
            //产地
            let detail = printData.productDetail;
            if(detail.length>lineLength){
                command.setText(rotate,font,320, 190,  0, detail.substring(0,lineLength));
                command.setText(rotate,font,320, 165,  0, detail.substring(lineLength,printData.productDetail.length));
            }else{
                command.setText(rotate,font,320, 185,  0, detail);
            }

            //生产主体名称
            let nameLineLength=7;
            let entName = printData.entName;
            if(entName.length<=nameLineLength){
                command.setText(rotate,font,320, 125,  0, entName);
            }else if(entName.length>nameLineLength && entName.length<=(2*nameLineLength)){
                command.setText(rotate,font,320, 140,  0, entName.substring(0,nameLineLength));
                command.setText(rotate,font,320, 115,  0, entName.substring(nameLineLength,entName.length));
            }else if(entName.length>(2*nameLineLength)){
                command.setText(rotate,font,320, 140,  0, entName.substring(0,nameLineLength));
                command.setText(rotate,font,320, 115,  0, entName.substring(nameLineLength,(2*nameLineLength)));
                command.setText(rotate,font,320, 90,  0, entName.substring((2*nameLineLength),entName.length>(3*nameLineLength)?(3*nameLineLength):entName.length));
            }

            //联系方式
            command.setText(rotate,font,320, 65,  0, printData.entContactsPhone);

            //开具日期
            command.setText(rotate,font,320, 30, 0 , printData.createDate.substring(0,10));

            //二维码
            command.setQR(10, 15, 3, viewCertificateUrl + "a/bas/certificate/viewScanCertificateNo?id=" + no.id);

            command.setForm();
            command.setPagePrint(1);
        })

        senBlData(command.getData()).then(res => {
            resolve()
        });
    })

}

//选择打印样式
function chooseBlueTypeLabel(template,type,command) {
    if (command === "cpcl") {
        return chooseBlueTypeLabelCpcl(template,type);
    }
    return chooseBlueTypeLabelTsc(template,type);
}

function chooseBlueTypeLabelTsc(template,type) {
    if(1==template){//长春市合格证(7*6cm)
        if(type=="D45BT"){
            return senBle76LabelD45BT();
        }else if(type=="M32B"){
            return senBle76LabelM32B();
        }
    }else if(2==template){//白山市合格证(7*6cm)
        if(type=="D45BT"){
            return senBleBs76LabelD45BT();
        }else if(type=="M32B"){
            return senBleBs76LabelM32B();
        }
    }else if(3==template){//白山市合格证(8*8cm)
        return senBle80LabelD45BT();
    }else if(4==template){//通用版合格证(7*6cm)
        if(type=="D45BT"){
            return senBle76v2LabelD45BT();
        }else if(type=="M32B"){
            return senBle76v2LabelM32B();
        }
    }else if(5==template){//人参合格证(7*6cm)
        if(type=="D45BT"){
            return senBle76v3LabelD45BT();
        }else if(type=="M32B"){
            return senBle76v3LabelM32B();
        }
    }else if(6==template){//承诺达标合格证(7*7cm)
        if(type=="D45BT"){
            return senBle77v1LabelD45BT();
        }else if(type=="M32B"){
            return senBle77v1LabelM32B();
        }
    }else if(7==template){//人参初级产品承诺达标合格证(7*7cm)
        if(type=="D45BT"){
            return senBle77v1LabelD45BT();
        }else if(type=="M32B"){
            return senBle77v1LabelM32B();
        }
    }else if(8==template){//2023-11-10 新需求 (7*7cm)
        if(type=="D45BT"){
            return senBle77v2LabelD45BT();
        }else if(type=="M32B"){
            return senBle77v2LabelM32B();
        }
    }
}
function chooseBlueTypeLabelCpcl(template,type) {
    if(1==template){//长春市合格证(7*6cm)
        return senBle76LabelM22();
    }else if(2==template){//白山市合格证(7*6cm)
        return senBleBs76LabelM22();
    }else if(3==template){

    }else if(4==template){//通用版合格证(7*6cm)
        return senBle76v2LabelM22();
    }else if(5==template){//人参合格证(7*6cm)
        return senBle76v3LabelM22();
    }else if(6==template){//承诺达标合格证(7*7cm)
        if(type=="M22"){
            return senBle77v1LabelM22();
        }else if(type=="E3PLUS"){
            return senBle77v1LabelE3PLUS();
        }
    }else if(7==template){//人参初级产品承诺达标合格证(7*7cm)
        return senBle77v1LabelM22();
    }else if(8==template){ //2023-11-10 新需求 (7*7cm)
        if(type=="M22"){
            return senBle77v2LabelM22();
        }else if(type=="E3PLUS"){
            return senBle77v2LabelE3PLUS();
        }
    }
    return Promise.resolve();
}

module.exports = {
    openBluetoothAdapter,
    searchDevice,
    connectDevices,
    getBleServices,
    stopFindBule,
    disconnetDevices,
    closeBluetoothAdapter,
    getAdapterState,
    chooseBlueTypeLabel
};