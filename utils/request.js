
var config = require('../common/config.js')
const commonUrl = config.BASE_PATH; //公共路径
const orcCode=config.OCR_CODE;
//let api = require('../api/user.js');
import Vue from 'vue'
import Router from '@/router'

// post请求封装
function post(url, data) {
	let promise = new Promise((resolve, reject) => {
		let that = this;
		let postData = data;
		uni.request({
			url: commonUrl + url,
			data: postData,
			method: 'POST',
			header: getHeaders(),
			success: function(res) {
				httpStatus(resolve, res);
				//resolve('suc');
			},
			error: function(e) {
				reject('网络出错');
			}
		})
	});
	return promise;
}

function upload(url, filePath, tableName) {

	let promise = new Promise((resolve, reject) => {
		let that = this;
		uni.uploadFile({
			url: commonUrl + url,
			filePath: filePath,
			name: 'file',
			header: getHeaders(),
			formData: {
				tableName: tableName
			},
			success: (uploadFileRes) => {
				httpStatus(resolve, uploadFileRes);
			},
			error: function(e) {
				reject('网络出错');
			}
		})
	});
	return promise;
}



// get请求封装
function get(url, data) {
	let promise = new Promise((resolve, reject) => {
		let that = this;
		let postData = data;
		uni.request({
			url: commonUrl + url,
			data: postData,
			method: 'GET',
			header: getHeaders(),
			success: function(res) {
				httpStatus(resolve, res);
				//resolve('suc');
			},
			error: function(e) {
				reject('网络出错');
			}
		})
	});
	return promise;
}

function getHeaders() {
	let headers = {};
	headers['Content-Type'] = 'application/json;charset=utf-8';
	if (uni.getStorageSync("token")) {
		headers['Authorization'] = uni.getStorageSync("token");
	}else{
		headers['Authorization'] = '';
	}
	return headers;
}

function httpStatus(resolve, res) {
		
	if (res.statusCode == 200) {
		resolve(res.data);
	} else if (res.statusCode == 401) {
		let title = res.data.code == 1 ? res.data.message : "未认证或认证已过期，请重新登录!";
		//token认证失败
		console.log(res.data.code)
		if (res.data.code == -1) {
			//如果是绑定用户，通过openId去更新token
			let openId = uni.getStorageSync("openId");
			if (openId) {
				let url = "wechat/rest/userInfo/";
				let postData = {
					openId: openId,
					phoneNumber: uni.getStorageSync("userInfo").phoneNumber,
				}
				uni.request({
					url: commonUrl + url,
					data: postData,
					method: 'POST',
					header: getHeaders(),
					success: function(info_res) {
						if (info_res.data.code == 0) {
							uni.setStorageSync('token', info_res.data.data.token);
							uni.setStorageSync('userInfo', info_res.data.data.userInfo);
							Router.replaceAll("/pages/index/index")
						}
					},
					error: function(e) {
						reject('网络出错');
					}
				})
			} else {
				try {
					uni.clearStorageSync();
					console.log("同步清理本地数据缓存。成功")
					uni.showToast({
						icon: 'none',
						title: title,
						mask: true,
						duration: 1500,
						success() {
							setTimeout(() => {
								Router.replaceAll("/pages/index/index")
							}, 1500);
						}
					})
				} catch (e) {
					console.log("同步清理本地数据缓存。失败")
				}
			}
		}else{
			//判断是否为祥云登录返回的错误，需要特殊业务处理
			if(title.startsWith('自动登录失败，请手动登录.')){
                resolve(res.data);
			}else {
                uni.showToast({
                    icon: 'none',
                    title: title,
                    mask: true,
                    duration: 1500,
                    success() {
						/* if (res.data.code == -1) {
						 setTimeout(() => {
						 Router.replaceAll("/pages/index/index")
						 }, 1500);
						 } */
                    }
                })
			}
		}

	} else {
		uni.showToast({
			icon: 'none',
			title: '获取数据失败',
			mask: true,
			duration: 1500,
		})
	}

}
// postNew请求封装
function postOcr(url, data) {
	let promise = new Promise((resolve, reject) => {
		let that = this;
		let postData = data;
		uni.request({
			url: url,
			data: JSON.stringify(data),
			method: 'POST',
			dataType:'json',
			header: getHeadersOcr(),
			success: function(res) {
				httpStatus(resolve, res);
			},
			error: function(e) {
				reject('网络出错');
			}
		})
	});
	return promise;
}

function getHeadersOcr() {
	let headers = {};
	headers['Content-Type'] = 'application/octet-stream; charset=utf-8';
	headers['Authorization'] = 'APPCODE '+orcCode;
	return headers;
}

module.exports = {
	post,
	get,
	upload,
	postOcr
}
