{
    "pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
        {
            "path": "pages/index/index",
            "name": "index",
            "meta": {
                "title": "首页",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/index/indexCloud",
            "name": "indexCloud",
            "meta": {
                "title": "首页",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": ""
            }
        }, {
            "path": "pages/login/index",
            "name": "loginIndex",
            "meta": {
                "title": "登录首页",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": "登录首页"
            }
        }, {
            "path": "pages/login/login",
            "name": "login",
            "meta": {
                "title": "登录页",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": "登录页"
            }
        }, {
            "path": "pages/login/phoneLogin",
            "name": "phoneLogin",
            "meta": {
                "title": "手机登录页",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": "手机登录页"
            }
        }, {
            "path": "pages/login/agreementView",
            "name": "agreementView",
            "meta": {
                "title": "用户服务协议",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": "用户服务协议"
            }
        }, {
            "path": "pages/login/privacyView",
            "name": "privacyView",
            "meta": {
                "title": "隐私政策",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": "隐私政策"
            }
        }, {
            "path": "pages/user/user",
            "name": "user",
            "meta": {
                "title": "我的",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": "我的"
            }
        }, {
            "path": "pages/user/checkOldPhone",
            "name": "userCheckOldPhone",
            "meta": {
                "title": "更换手机号",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "更换手机号"
            }
        }, {
            "path": "pages/user/editPhone",
            "name": "userEditPhone",
            "meta": {
                "title": "修改手机号",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "修改手机号"
            }
        }, {
            "path": "pages/product/list",
            "name": "productList",
            "meta": {
                "title": "产品",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "产品",
                "onReachBottomDistance": 50,
                "enablePullDownRefresh": true
            }
        }, {
            "path": "pages/product/add",
            "name": "productAdd",
            "meta": {
                "title": "产品添加",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "产品添加"
            }
        }, {
            "path": "pages/product/edit",
            "name": "productEdit",
            "meta": {
                "title": "产品修改",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "产品修改"
            }
        }, {
            "path": "pages/ent/chooseType",
            "name": "chooseType",
            "meta": {
                "title": "维护主体信息",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": ""
            }
        }, {
            "path": "pages/ent/chooseFarm",
            "name": "chooseFarm",
            "meta": {
                "title": "维护主体信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "维护主体信息"
            }
        }, {
            "path": "pages/ent/chooseNature",
            "name": "chooseNature",
            "meta": {
                "title": "维护主体信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "维护主体信息"
            }
        }, {
            "path": "pages/ent/enterpriseInfo",
            "name": "enterpriseInfo",
            "meta": {
                "title": "维护主体信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "维护主体信息"
            }
        },
        {
            "path": "pages/product/product-add",
            "name": "product-add",
            "meta": {
                "title": "产品信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "产品信息"
            }
        },
        {
            "path": "pages/product/product-list",
            "name": "product-list",
            "meta": {
                "title": "产品",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": "产品",
                "onReachBottomDistance": 50,
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/product/product-edit",
            "name": "product-edit",
            "meta": {
                "title": "产品信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "产品信息"
            }
        },
        {
            "path": "pages/ent/personInfo",
            "name": "personInfo",
            "meta": {
                "title": "维护主体信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "维护主体信息"
            }
        },
        {
            "path": "pages/ent/personInfoShow",
            "name": "personInfoShow",
            "meta": {
                "title": "维护主体信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "维护主体信息"
            }
        },
        {
            "path": "pages/ent/personInfoUpdate",
            "name": "personInfoUpdate",
            "meta": {
                "title": "维护主体信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "维护主体信息"
            }
        },
        {
            "path": "pages/device/searchDevice",
            "name": "searchDevice",
            "meta": {
                "title": "设备连接",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": "设备连接"
            }
        },
        {
            "path": "pages/certificate/certificate",
            "name": "certificate",
            "meta": {
                "title": "合格证开具",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "合格证开具"
            }
        },
        {
            "path": "pages/certificate/showCertificate",
            "name": "showCertificate",
            "meta": {
                "title": "合格证预览",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "合格证预览"
            }
        },
        {
            "path": "pages/certificate/list",
            "name": "certificateList",
            "meta": {
                "title": "开具记录",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "开具记录",
                "onReachBottomDistance": 50,
                "enablePullDownRefresh": true
            }
        }, {
            "path": "pages/certificate/view",
            "name": "certificateView",
            "meta": {
                "title": "开具记录",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "开具记录"
            }
        },
        {
            "path": "pages/guidance/list",
            "name": "guidanceList",
            "meta": {
                "title": "农业技术指导",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": "农业技术指导",
                "onReachBottomDistance": 50,
                "enablePullDownRefresh": true
            }
        }, {
            "path": "pages/guidance/view",
            "name": "guidanceView",
            "meta": {
                "title": "农业技术指导",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": "农业技术指导查看"
            }
        }, {
            "path": "pages/video/list",
            "name": "videoList",
            "meta": {
                "title": "视频培训",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": "视频培训",
                "onReachBottomDistance": 50,
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/ent/enterpriseInfoShow",
            "name": "enterpriseInfoShow",
            "meta": {
                "title": "主体信息查看",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "主体信息查看"
            }
        },
        {
            "path": "pages/ent/enterpriseInfoUpdate",
            "name": "enterpriseInfoUpdate",
            "meta": {
                "title": "维护主体信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "维护主体信息"
            }
        },
        {
            "path": "pages/direction/direction",
            "name": "direction",
            "meta": {
                "title": "使用说明",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": "使用说明"
            }
        },
        {
            "path": "pages/device/supportDevice",
            "name": "supportDevice",
            "meta": {
                "title": "支持设备",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "支持设备"
            }
        }, {
            "path": "pages/product/product-show",
            "style": {}
        },
        {
            "path": "pages/certificate/showCertificateBaishan",
            "name": "showCertificateBaishan",
            "meta": {
                "title": "合格证预览",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "合格证预览"
            }
        },
        {
            "path": "pages/user/feedback",
            "name": "feedback",
            "meta": {
                "title": "意见反馈",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "意见反馈"
            }
        },
        {
            "path": "pages/inspection/chooseType",
            "name": "inspectionChooseType",
            "meta": {
                "title": "承诺依据证明",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "承诺依据证明"
            }
        },
        {
            "path": "pages/inspection/list",
            "name": "inspectionList",
            "meta": {
                "title": "承诺依据证明",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "承诺依据证明"
            }
        },
        {
            "path": "pages/inspection/view",
            "name": "inspectionView",
            "meta": {
                "title": "承诺依据证明",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "承诺依据证明",
                "onReachBottomDistance": 50,
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/inspection/viewDetail",
            "name": "inspectionViewDetail",
            "meta": {
                "title": "承诺依据证明",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "承诺依据证明"
            }
        },
        {
            "path": "pages/inspection/enterList",
            "name": "inspectionEnterList",
            "meta": {
                "title": "承诺依据证明",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "承诺依据证明",
                "onReachBottomDistance": 50,
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/inspection/enterForm",
            "name": "inspectionEnterForm",
            "meta": {
                "title": "承诺依据证明",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "承诺依据证明"
            }
        },
        {
            "path": "pages/inspection/enterView",
            "name": "inspectionEnterView",
            "meta": {
                "title": "承诺依据证明",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "承诺依据证明"
            }
        },
        {
            "path": "pages/inspection/companyEnterView",
            "name": "companyEnterView",
            "meta": {
                "title": "承诺依据证明",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "承诺依据证明"
            }
        },
        {
            "path": "pages/inspection/recordList",
            "name": "inspectionrRecordList",
            "meta": {
                "title": "承诺依据证明",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "承诺依据证明",
                "onReachBottomDistance": 50,
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/inspection/companyInspectRecordList",
            "name": "companyInspectRecordList",
            "meta": {
                "title": "承诺依据证明",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "承诺依据证明",
                "onReachBottomDistance": 50,
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/inspection/entSearch",
            "name": "entSearch",
            "meta": {
                "title": "选择单位",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "选择单位",
                "onReachBottomDistance": 50
            }
        },
        {
            "path": "pages/inspection/companyEnterForm",
            "name": "companyEnterForm",
            "meta": {
                "title": "承诺依据证明",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "承诺依据证明"
            }
        },
        {
            "path": "pages/inspection/internalList",
            "name": "inspectionrInternalList",
            "meta": {
                "title": "承诺依据证明",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "承诺依据证明",
                "onReachBottomDistance": 50,
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/inspection/internalForm",
            "name": "inspectionrInternalForm",
            "meta": {
                "title": "承诺依据证明",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "承诺依据证明",
                "onReachBottomDistance": 50,
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/inspection/internalView",
            "name": "inspectionrInternalView",
            "meta": {
                "title": "承诺依据证明",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "承诺依据证明",
                "onReachBottomDistance": 50,
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/inspection/internalListQuery",
            "name": "inspectionrInternalListQuery",
            "meta": {
                "title": "承诺依据证明",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "承诺依据证明",
                "onReachBottomDistance": 50,
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/basic/basicEntForm",
            "name": "basicEntForm",
            "meta": {
                "title": "养殖企业信息填报",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "养殖企业信息填报"
            }
        },
        {
            "path": "pages/basic/basicFarmLivestockForm",
            "name": "basicFarmLivestockForm",
            "meta": {
                "title": "养殖企业信息填报",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "养殖企业信息填报"
            }
        },
        {
            "path": "pages/shop/shop",
            "name": "shop",
            "meta": {
                "title": "",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/pay/pay",
            "name": "pay",
            "meta": {
                "title": "",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/entChange/chooseFarm",
            "name": "entChangeChooseFarm",
            "meta": {
                "title": "维护主体信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "维护主体信息"
            }
        },
        {
            "path": "pages/entChange/chooseNature",
            "name": "entChangeChooseNature",
            "meta": {
                "title": "维护主体信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "维护主体信息"
            }
        },
        {
            "path": "pages/entChange/chooseType",
            "name": "entChangeChooseType",
            "meta": {
                "title": "维护主体信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "维护主体信息"
            }
        },
        {
            "path": "pages/entChange/enterpriseInfo",
            "name": "entChangeEnterpriseInfo",
            "meta": {
                "title": "维护主体信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "维护主体信息"
            }
        },
        {
            "path": "pages/entChange/personInfo",
            "name": "entChangePersonInfo",
            "meta": {
                "title": "维护主体信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "维护主体信息"
            }
        },
        {
            "path": "pages/entChange/viewCheck",
            "name": "entChangeViewCheck",
            "meta": {
                "title": "维护主体信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "维护主体信息"
            }
        },
        {
            "path": "pages/entChange/enterpriseInfoShow",
            "name": "entChangeEnterpriseInfoShow",
            "meta": {
                "title": "维护主体信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "维护主体信息"
            }
        },
        {
            "path": "pages/entChange/personInfoShow",
            "name": "entChangePersonInfoShow",
            "meta": {
                "title": "维护主体信息",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "维护主体信息"
            }
        },
        {
            "path": "pages/certificate/certificateImgView",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }

        },
        {
            "path": "pages/product/product-select",
            "name": "productSelect",
            "meta": {
                "title": "选择关联商品",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "选择关联商品",
                "onReachBottomDistance": 50,
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/index/assisting",
            "name": "assisting",
            "meta": {
                "title": "助农服务",
                "requireLogin": true
            },
            "style": {
                "navigationBarTitleText": "助农服务"
            }
        },
        {
            "path": "pages/index/yinong",
            "name": "yinong",
            "meta": {
                "title": "壹农益品",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": "壹农益品"
            }
        },
        {
            "path": "pages/index/zhenxuan",
            "name": "zhenxuan",
            "meta": {
                "title": "壹农益品甄选",
                "requireLogin": false
            },
            "style": {
                "navigationBarTitleText": "壹农益品甄选"
            }
        }
    ],
    "subPackages": [
        {
            "root": "inspectionReport",
            "pages": [
                {
                    "path": "index/inspectionReportList",
                    "name": "inspectionReportList",
                    "meta": {
                        "title": "检测报告管理",
                        "requireLogin": true
                    },
                    "style": {
                        "navigationBarTitleText": "检测报告管理",
                        "onReachBottomDistance": 50,
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "index/inspectionReportEnterForm",
                    "name": "inspectionReportEnterForm",
                    "meta": {
                        "title": "检测报告录入",
                        "requireLogin": true
                    },
                    "style": {
                        "navigationBarTitleText": "检测报告录入"
                    }
                },
                {
                    "path": "index/inspectionReportDetail",
                    "name": "inspectionReportDetail",
                    "meta": {
                        "title": "检测报告详情",
                        "requireLogin": true
                    },
                    "style": {
                        "navigationBarTitleText": "检测报告详情"
                    }
                }
            ]
        }
    ],

/*    "tabBar": {
        "color": "#7a7e83",
        "selectedColor": "#3EB073",
        "backgroundColor": "#ffffff",
        "list": [{
            "pagePath": "pages/index/index",
            "text": "主页",
            "iconPath": "static/img/home.png",
            "selectedIconPath": "static/img/homeHL.png"
        }, {

            "pagePath": "pages/product/product-list",
            "text": "产品",
            "iconPath": "static/img/cp.png",
            "selectedIconPath": "static/img/cpHL.png"
        }, {
            "pagePath": "pages/ent/chooseType",
            "text": "生产经营主体",
            "iconPath": "static/img/sc.png",
            "selectedIconPath": "static/img/scHL.png"
        }, {
            "pagePath": "pages/user/user",
            "text": "我的",
            "iconPath": "static/img/my.png",
            "selectedIconPath": "static/img/myHL.png"
        }]
    },*/
    "globalStyle": {
        "navigationBarTextStyle": "white",
        "navigationBarTitleText": "uni-app",
        "navigationBarBackgroundColor": "#02994f",
        "backgroundColor": "#F8F8F8"
    },
    "condition": { //模式配置，仅开发期间生效
        "current": 0, //当前激活的模式(list 的索引项)
        "list": [{
            "name": "", //模式名称
            "path": "", //启动页面，必选
            "query": "" //启动参数，在页面的onLoad函数里面得到
        }]
    }
}