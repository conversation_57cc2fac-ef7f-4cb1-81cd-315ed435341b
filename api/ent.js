var api = require('../utils/request.js')

//添加主体信息
var saveEnt=function(param){
	return api.post("wechat/rest/ent/save",param);
}

//获取区域
var getArea=function(type){
	return api.post("wechat/rest/ent/getArea?type="+type);
}

//上传附件
var upload =function(filePath,tableName){
	return api.upload("wechat/rest/common/saveUpload",filePath,tableName);
}

//提交主体个人信息
var savePerson =function(param){
	return api.post("wechat/rest/ent/savePerson",param);
}

var getBase64  =function(filePath){
	return api.upload("wechat/rest/common/getBase64",filePath);
}

//身份证唯一校验
var checkIdCard=function(param){
	return api.post("wechat/rest/ent/checkIdCard",param);
}

//获取主体信息
var getEntInfo=function(param){
	return api.post("wechat/rest/ent/getEntInfo",param);
}

//社会信用代码唯一校验
var checkSocialCode=function(param){
	return api.post("wechat/rest/ent/checkSocialCode",param);
}


//获取七牛token
var get7nToken=function(){
	return api.post("wechat/rest/common/get7nToken");
}
//获取主体信息
var getEnt=function(){
	return api.get("wechat/rest/ent/getEnt");
}

//识别工商营业执照图片信息
var ocrLicensePic = function(param){
	console.log("param="+param)
	return api.post("wechat/rest/attachment/ocrLicensePic",param);
}


//识别身份证信息
var ocrIdCardPic = function(param){
	return api.post("wechat/rest/attachment/ocrIdCardPic",param);
}

//获取主体类别集合-企业
var findMainTypeEntList = function(){
	return api.get("wechat/rest/ent/findMainTypeList/0");
}
//获取主体类别集合-个体
var findMainTypePersonList = function(){
	return api.get("wechat/rest/ent/findMainTypeList/1");
}
var findMainTypeCompanyList = function(){
	return api.get("wechat/rest/ent/findMainTypeList/2");
}

//识别身份证信息-前端
var ocrIdCardPicJs = function(param){
	return api.postOcr("https://dm-51.data.aliyun.com/rest/160601/ocr/ocr_idcard.json",param);
}

//识别工商营业执照信息-前端
var ocrLicensePicJs = function(param){
	return api.postOcr("https://dm-58.data.aliyun.com/rest/160601/ocr/ocr_business_license.json",param);
}

//识别公章图片信息
var ocrSealPic = function(param){
	return api.post("wechat/rest/attachment/ocrSealPic",param);
}
//主体变更业务查看状态更新
var updateChangeView = function(){
	return api.post("wechat/rest/ent/updateChangeView");
}

var findEntListPageQuick = function(pageNo, param){
	let page = (!pageNo) ? '' : "?pageNo=" + pageNo;
	return api.post("wechat/rest/ent/findListPageQuick"+page, param);
}
module.exports = {
	saveEnt,
	getArea,
	upload,
	savePerson,
	getBase64,
	checkIdCard,
	getEntInfo,
	checkSocialCode,
	get7nToken,
	getEnt,
	ocrLicensePic,
	ocrIdCardPic,
	findMainTypeEntList,
	findMainTypePersonList,
	findMainTypeCompanyList,
	ocrIdCardPicJs,
	ocrLicensePicJs,
	ocrSealPic,
	updateChangeView,
	findEntListPageQuick
}
