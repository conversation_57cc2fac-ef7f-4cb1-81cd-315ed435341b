var api = require('../utils/request.js')

//获取产品集合信息
var findProducts = function(){
    return api.post("wechat/rest/inspection/findInspectionList");
}

//添加合格证信息
var saveCertificate = function(param){
    return api.post("wechat/rest/certificate/saveCertificate",param);
}
var printCertificateAgain = function(param){
	return api.post("wechat/rest/certificate/printCertificateAgain",param);
}
//获取合格证集合信息
var getSummaryInfo=function(){
	return api.post("wechat/rest/certificate/summaryInfo");
}
//获取合格证集合信息
var findList=function(pageNo,param){
	let page = (!pageNo) ? '' : "?pageNo=" + pageNo;
	return api.post("wechat/rest/certificate/list"+page,param);
}

var getCertificate=function(id){
	return api.post("wechat/rest/certificate/getById?id="+id);
}
var getCertificateForElectronicShow=function(id){
	return api.post("wechat/rest/certificate/getForElectronicShow?id="+id);
}

module.exports = {
    findProducts,
    saveCertificate,
	getSummaryInfo,
	findList,
	getCertificate,
	printCertificateAgain,
	getCertificateForElectronicShow
}
