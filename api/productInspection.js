var api = require('../utils/request.js')

//获取录入产品检测信息集合
var findList=function(pageNo,param){
	let page = (!pageNo) ? '' : "?pageNo=" + pageNo;
	return api.post("wechat/rest/productInspection/list"+page,param);
}
//产品录入检测信息保存方法
var saveEnter=function(param){
	return api.post("wechat/rest/productInspection/saveEnter",param);
}
//获取录入检测信息
var getEnter=function(param){
	return api.post("wechat/rest/productInspection/getEnter",param);
}
//获取最新检测数据集合
var findNewestList=function(param){
	return api.post("wechat/rest/productInspection/findNewestList",param);
}
//获取最新检测数据集合根据多选承诺依据
var findNewestListByInspectionSituationList=function(param){
	return api.post("wechat/rest/productInspection/findNewestListByInspectionSituationList",param);
}

module.exports = {
	findList,
	saveEnter,
	getEnter,
	findNewestList,
	findNewestListByInspectionSituationList,
}
