var api = require('../utils/request.js')

//获取产品集合信息
var findProductList = function(pageNo, param) {
    let page = (!pageNo) ? '' : "?pageNo=" + pageNo;
    return api.post("wechat/rest/product/findProductPage" + page, param, );
}

//添加产品
var saveProduct = function(param) {
    return api.post("wechat/rest/product/save", param);
}
//获取产品
var getProduct = function(id) {
    return api.get("wechat/rest/product/get/" + id);
}
//删除产品
var deleteProduct = function(id) {
    return api.get("wechat/rest/product/delete/" + id);
}

//批量删除产品
var deleteBatch = function(param) {
    return api.post("wechat/rest/product/deleteBatch", param);
}

//获取附件
var findProductAttachment = function(param) {
    return api.post("wechat/rest/product/findProductAttachment", param);
}

//添加产品
var saveProductNew = function(param) {
    return api.post("wechat/rest/product/saveProduct", param);
}

//获取产品集合
var findList = function() {
    return api.post("wechat/rest/product/findList");
}
var findListByEntId = function(param) {
    return api.post("/wechat/rest/product/findListByEntId", param);
}
//获取主体的一个产品
var getOne = function() {
    return api.get("wechat/rest/product/getOne");
}

//甄选平台是否包含当前主体
var checkPhoneNumOpenShopCart = function(param) {
    return api.post("/wechat/rest/product/checkPhoneNumOpenShopCart", param);
}
//复购商品列表
var productfindReBuyProductPage = function(param) {
    return api.post("wechat/rest/product/findReBuyProductPage", param);
}

module.exports = {
    findProductList,
    saveProduct,
    deleteProduct,
    getProduct,
    findProductAttachment,
    deleteBatch,
    saveProductNew,
    findList,
    findListByEntId,
    getOne,
    productfindReBuyProductPage,
    checkPhoneNumOpenShopCart
}