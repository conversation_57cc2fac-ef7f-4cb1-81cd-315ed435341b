var api = require('../utils/request.js')

//获取产品检测集合信息
var findInspectionList=function(){
	return api.post("wechat/rest/inspection/findInspectionList");
}

//获取某产品检测样品集合信息
var findInspectionRecordList=function(pageNo,productRecord){
    let page = (!pageNo) ? '' : "?pageNo=" + pageNo;
    return api.post("wechat/rest/inspection/findInspectionRecordList"+page,productRecord);
}

//申请检测
var applyInspection=function(applyList){
	return api.post("wechat/rest/inspection/saveRecord",applyList);
}
//获取检测详情
var getInspectionDetail=function(sampleNo){
	return api.post("wechat/rest/inspection/"+sampleNo+"/findResultListBySampleNo");
}
 
module.exports = {
	findInspectionList,
    findInspectionRecordList,
	applyInspection,
	getInspectionDetail
}
