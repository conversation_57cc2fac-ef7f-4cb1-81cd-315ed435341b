var api = require('../utils/request.js')

//上传附件
var upload =function(filePath,tableName){
	return api.upload("wechat/rest/common/saveUpload",filePath,tableName);
}

var getBase64  =function(filePath){
	return api.upload("wechat/rest/common/getBase64",filePath);
}

//获取七牛token
var get7nToken=function(){
	return api.post("wechat/rest/common/get7nToken");
}

//识别工商营业执照图片信息
var ocrLicensePic = function(param){
	console.log("param="+param)
	return api.post("wechat/rest/attachment/ocrLicensePic",param);
}

//识别身份证信息
var ocrIdCardPic = function(param){
	return api.post("wechat/rest/attachment/ocrIdCardPic",param);
}

//识别身份证信息-前端
var ocrIdCardPicJs = function(param){
	return api.postOcr("https://dm-51.data.aliyun.com/rest/160601/ocr/ocr_idcard.json",param);
}

//识别工商营业执照信息-前端
var ocrLicensePicJs = function(param){
	return api.postOcr("https://dm-58.data.aliyun.com/rest/160601/ocr/ocr_business_license.json",param);
}

//识别公章图片信息
var ocrSealPic = function(param){
	return api.post("wechat/rest/attachment/ocrSealPic",param);
}

module.exports = {
	upload,
	getBase64,
	get7nToken,
	ocrLicensePic,
	ocrIdCardPic,
	ocrIdCardPicJs,
	ocrLicensePicJs,
	ocrSealPic
}
