var api = require('../utils/request.js')

//获取检测报告列表
var findList = function(pageNo, param) {
	let page = (!pageNo) ? '' : "?pageNo=" + pageNo;
	return api.post("wechat/rest/inspectionReport/list" + page, param);
}

//保存检测报告
var saveReport = function(param) {
	return api.post("wechat/rest/inspectionReport/saveOrUpdate", param);
}

//获取检测报告详情
var getReport = function(id) {
	return api.get("wechat/rest/inspectionReport/detail/" + id);
}


var getEntInfo = function(id) {
	return api.get("wechat/rest/inspectionReport/getEntInfo" );
}

//删除检测报告
var deleteReport = function(id) {
	return api.get("wechat/rest/inspectionReport/delete/" + id);
}

//更新检测报告
var updateReport = function(param) {
	return api.post("wechat/rest/inspectionReport/update", param);
}

module.exports = {
	findList,
	saveReport,
	getReport,
	deleteReport,
	updateReport,
	getEntInfo
}
