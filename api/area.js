var api = require('../utils/request.js')

//获取行政区划（原有接口，保持兼容）
var findTreeList=function(param){
	return api.post("wechat/rest/area/findProvinceCity", param);
}

//获取基础信息街道级行政区划（原有接口，保持兼容）
var findBasicStreetList=function(param){
	return api.post("wechat/rest/area/findBasicStreetList", param);
}

//统一的地区查询接口（推荐使用）
var findRegionsByParent=function(param){
	return api.post("wechat/rest/area/findRegionsByParent", param);
}

//获取全国省份列表（兼容方法，内部调用统一接口）
var findProvinces=function(){
	return findRegionsByParent({parentId: null});
}

//根据省份ID获取城市列表（兼容方法，内部调用统一接口）
var findCitiesByProvince=function(provinceId){
	return findRegionsByParent({parentId: provinceId});
}

//根据城市ID获取区县列表（兼容方法，内部调用统一接口）
var findDistrictsByCity=function(cityId){
	return findRegionsByParent({parentId: cityId});
}

module.exports = {
	findTreeList,
	findBasicStreetList,
	findRegionsByParent,
	findProvinces,
	findCitiesByProvince,
	findDistrictsByCity,
}
