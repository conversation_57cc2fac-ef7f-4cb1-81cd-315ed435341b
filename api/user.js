var api = require('../utils/request.js')

//获取用户信息
var getUser=function(){
	return api.get("wechat/rest/user");
}
//退出
var loginOut=function(){
	return api.get("wechat/rest/loginOut");
}

//校验验证码
var checkCaptcha=function(param){
	return api.post("wechat/rest/checkCaptcha",param);
}
//更新手机号
var updatePhone=function(param){
	return api.post("wechat/rest/updatePhone",param);
}
//保存微信用户数据
var saveWechatUser=function(openId){
	return api.get("wechat/rest/saveWechatUser/"+openId);
}
module.exports = {
	getUser,
	loginOut,
	checkCaptcha,
	updatePhone,
	saveWechatUser,
}
