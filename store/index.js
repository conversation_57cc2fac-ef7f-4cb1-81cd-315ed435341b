import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

const store = new Vuex.Store({
	state: {
		token:"",
		openId:"",
		sessionKey:"",
		userInfo: {},
		bleDevice: {},
		printData: {},
		printDetailData: {},
		bluetoothAvailable: false,
		bluetoothDiscovering: false
	},
	mutations: {
		login(state, data) {
			state.token = data.token;
			uni.setStorageSync('token', data.token);
		},
		logout(state) {
			state.userInfo = {};
			/* uni.removeStorageSync('token');
			uni.removeStorageSync('userInfo');
			uni.removeStorageSync('openId');
			uni.removeStorageSync('sessionKey');
			uni.removeStorageSync('wechatUser'); */
			try {
			    uni.clearStorageSync();
				console.log("同步清理本地数据缓存。成功")
			} catch (e) {
			    console.log("同步清理本地数据缓存。失败")
			}
		},
		setOpenId(state, data) {
			state.openId = data;
			uni.setStorageSync('openId', data);
		},
		setSessionKey(state, data) {
			state.sessionKey = data;
			uni.setStorageSync('sessionKey', data);
		},
		setBleDevice(state, data) {
			state.bleDevice = data;
		},
		setPrintData(state, data) {
			state.printData = data;
		},
		setPrintDetailData(state, data) {
			state.printDetailData = data;
		},
		setBluetoothAvailable(state, data) {
			state.bluetoothAvailable = data;
		},
		setBluetoothDiscovering(state, data) {
			state.bluetoothDiscovering = data;
		},
	},
	getters: {
		getBleDevice: state => state.bleDevice,
		getPrintData: state => state.printData,
		getBluetoothAvailable: state => state.bluetoothAvailable,
		getBluetoothDiscovering: state => state.bluetoothDiscovering,
	}
})

export default store
