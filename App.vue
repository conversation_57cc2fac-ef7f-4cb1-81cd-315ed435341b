<script>
	import bluetooth from "./utils/bluetooth";

    export default {
			globalData: {
				showPrivacy: false,
				linkTitle: ''
			},
		// #ifndef MP 
			created() {
				//在页面加载时读取sessionStorage里的状态信息
				if (sessionStorage.getItem("store")) {
					this.$store.replaceState(Object.assign({}, this.$store.state, JSON.parse(sessionStorage.getItem("store"))))
				}
				//在页面刷新时将vuex里的信息保存到sessionStorage里
				window.addEventListener("beforeunload", () => {
					sessionStorage.setItem("store", JSON.stringify(this.$store.state))
				})
			},
		// #endif
		onLaunch: function(options) {
			console.log('App Launch')
			if (uni.getPrivacySetting) {
				uni.getPrivacySetting({
					success: res => {
						console.log("是否需要授权：", res.needAuthorization, "隐私协议的名称为：", res.privacyContractName)
						if (res.needAuthorization) {
							getApp().globalData.showPrivacy = true;
							getApp().globalData.linkTitle = res.privacyContractName;
						} else {
							getApp().globalData.showPrivacy = false;
						}
					},
					fail: () => {
					},
					complete: () => {},
				})
			}
		},
		onShow: function() {
			console.log('App Show');
            let that = this
            //修改蓝牙设备的连接状态
            uni.onBLEConnectionStateChange(res => {
                // 该方法回调中可以用于处理连接意外断开等异常情况
                let bleDevice = this.$store.state.bleDevice;
                if (bleDevice && bleDevice.deviceId === res.deviceId && !res.connected) {
                    that.$store.commit("setBleDevice", {});
                    uni.showToast({
                        title: '蓝牙连接断开',
                        icon: "none",
                        duration: 3000,
						mask: true
                    });

                }
                console.log(`device ${res.deviceId} state has changed, connected: ${res.connected}`)
            })

            uni.onBluetoothAdapterStateChange(res => {
                this.$store.commit("setBluetoothAvailable", res.available);
                this.$store.commit("setBluetoothDiscovering", res.discovering);
            })
		},
		onHide: function() {
			console.log('App Hide');
		}
	}
</script>

<style>
	@import "./common/css/style.css";
	image {
		    	image-rendering:-moz-crisp-edges;
		        image-rendering:-o-crisp-edges;
		        image-rendering:-webkit-optimize-contrast;
		        image-rendering: crisp-edges;
		        -ms-interpolation-mode:nearest-neighbor;
		}
	
</style>
