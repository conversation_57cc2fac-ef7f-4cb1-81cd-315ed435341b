<template>
	<view class="weui-tabbar" :style="extClass">
		<!-- 选中的时候往weui-tabbar__item加class:weui-bar__item_on -->
		<view  @click="tabChange(index)" v-for="(item, index) in list" :key="index" class="weui-tabbar__item" :class="{'weui-bar__item_on':index === current}" v-if="item.show">
			<view style="position: relative;display:inline-block;"><image :src="current === index ? item.selectedIconPath : item.iconPath" class="weui-tabbar__icon"></image></view>
			<view class="weui-tabbar__label">{{ item.text }}</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		/* list: {
			type: Array,
			default: function() {
				return []
			}
		} */
		current:{
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			extClass: 'position:fixed;bottom:0;width:100%;left:0;right:0;',
			//current: 0,
			list: [],
		};
	},
	methods: {
    refresh() {
      let list = [{
        "text": "主页",
        "iconPath": '../../static/img/home.png',
        "selectedIconPath": '../../static/img/homeHL.png',
        "show": true
      },
        {
          "text": "产品",
          "iconPath": '../../static/img/cp.png',
          "selectedIconPath": '../../static/img/cpHL.png',
          "show": true
        },
        {
          "text": "生产经营主体",
          "iconPath": '../../static/img/sc.png',
          "selectedIconPath": '../../static/img/scHL.png',
          "show": true
        },
        {
          "text": "我的",
          "iconPath": '../../static/img/my.png',
          "selectedIconPath": '../../static/img/myHL.png',
          "show": true
        }
      ]
      let ent = uni.getStorageSync("ent");
      if (ent && ent.businessType === "2") {
        list[1].show = false;
        list[2].text = "建档信息";
      }
      this.list = list;
    },
		tabChange(index) {
			if (index === this.current) {
				return;
			}
			//this.current = index;
			//this.$emit('tabChange',index)
			if(index==0){
				this.$Router.replaceAll("/pages/index/index");
				//uni.switchTab({ url:'/pages/index/index' }); 
			}else if(index==1){
				//uni.switchTab({ url:'/pages/product/product-list' }); 
				this.$Router.replaceAll("/pages/product/product-list")
				
			}else if(index==2){
				//uni.switchTab({ url:'/pages/ent/chooseType' }); 
				this.$Router.replaceAll("/pages/ent/chooseType")
			}else if(index==3){
				//uni.switchTab({ url:'/pages/user/user' }); 
				this.$Router.replaceAll("/pages/user/user")
			}
		}
	},
  mounted() {
    this.refresh();
  }
};
</script>

<style lang="scss" scoped >
.weui-tabbar {
	display: flex;
	position: relative;
	z-index: 500;
	background-color: #ffffff;
}
.weui-tabbar:before {
	content: ' ';
	position: absolute;
	left: 0;
	top: 0;
	right: 0;
	height: 1px;
	border-top: 1rpx solid rgba(0, 0, 0, 0.1);
	color: rgba(0, 0, 0, 0.1);
}
.weui-tabbar__item {
	display: block;
	flex: 1;
	padding: 8px 0 4px;
	padding-bottom: calc(8px + constant(safe-area-inset-bottom));
	padding-bottom: calc(8px + env(safe-area-inset-bottom));
	font-size: 0;
	color: rgba(0, 0, 0, 0.5);
	text-align: center;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.weui-tabbar__item:first-child {
	padding-left: constant(safe-area-inset-left);
	padding-left: env(safe-area-inset-left);
}
.weui-tabbar__item:last-child {
	padding-right: constant(safe-area-inset-right);
	padding-right: env(safe-area-inset-right);
}
.weui-tabbar__item.weui-bar__item_on .weui-tabbar__icon,
.weui-tabbar__item.weui-bar__item_on .weui-tabbar__icon > i,
.weui-tabbar__item.weui-bar__item_on .weui-tabbar__label {
	color: #3EB073;
}
.weui-tabbar__icon {
	display: inline-block;
	width: 28px;
	height: 28px;
	margin-bottom: 2px;
}
i.weui-tabbar__icon,
.weui-tabbar__icon > i {
	font-size: 24px;
	color: rgba(0, 0, 0, 0.5);
}
.weui-tabbar__icon image {
	width: 100%;
	height: 100%;
}
.weui-tabbar__label {
	color: #bfbfbf;
	font-size: 10px;
	line-height: 1.4;
}
</style>