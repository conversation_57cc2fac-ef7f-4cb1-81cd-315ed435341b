<template>
	<view v-if="visibleSync" class="signature" :class="{'visible':show}">
		<view class="leftContent">
			<view class="left" @click="finish">保存</view>
			<view class="right" @click="clear">清除</view>
			<view class="close" @click="close">关闭</view>
		</view>
		
		<canvas class="mycanvas" canvas-id="mycanvas" @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend" disable-scroll="true"></canvas>
		
	</view>
</template>

<script>
	var x = 20;
	var y =20;
	export default {
		props:{
			visible: {
				type: Boolean,
				default: false
			}
		},
	data() {
		return {
			show:true,
			visibleSync: false,
			ctx:'',         //绘图图像
			points:[],       //路径点集合 
			signature:'',
			hasDh:false,
		}
	},
	watch:{
		visible(val) {
			this.visibleSync = val;
			this.show = val;
			this.createCanvas()
		}
	},
	created(options) {
		this.visibleSync = this.visible
		this.show = this.visible;
		this.createCanvas()
		setTimeout(() => {
			this.show = this.visible;
		}, 100)
	},
	methods: {
		//关闭并清空画布
		close:function(){
			this.show=false;
			this.visibleSync=false;
			this.clear();
			this.$emit('close');
			this.hasDh = false;
		},
		//创建并显示画布
		createCanvas:function(){
			this.ctx = uni.createCanvasContext("mycanvas",this);   //创建绘图对象	
			//设置画笔样式
			this.ctx.lineWidth = 4;
			this.ctx.lineCap = "round"
			this.ctx.lineJoin = "round"
		},
		//触摸开始，获取到起点
		touchstart:function(e){
			let startX = e.changedTouches[0].x;
			let startY = e.changedTouches[0].y;
			let startPoint = {X:startX,Y:startY};
			this.points.push(startPoint);
			//每次触摸开始，开启新的路径
			this.ctx.beginPath();
			this.hasDh = true;
		},
		
		//触摸移动，获取到路径点
		touchmove:function(e){
			let moveX = e.changedTouches[0].x;
			let moveY = e.changedTouches[0].y;
			let movePoint = {X:moveX,Y:moveY};
			this.points.push(movePoint);       //存点
			let len = this.points.length;
			if(len>=2){
				this.draw();                   //绘制路径
			}			
		},
		
		// 触摸结束，将未绘制的点清空防止对后续路径产生干扰
		touchend:function(){                   
			this.points=[];
		},
		
		/* ***********************************************
		#   绘制笔迹
		#	1.为保证笔迹实时显示，必须在移动的同时绘制笔迹
		#	2.为保证笔迹连续，每次从路径集合中区两个点作为起点（moveTo）和终点(lineTo)
		#	3.将上一次的终点作为下一次绘制的起点（即清除第一个点）
		************************************************ */
		draw: function() {
			let point1 = this.points[0]
			let point2 = this.points[1]
			this.points.shift()
			this.ctx.moveTo(point1.X, point1.Y)
			this.ctx.lineTo(point2.X, point2.Y)
			this.ctx.stroke()
			this.ctx.draw(true)
		},
		
		//清空画布
		clear:function(){
			let that = this;
			uni.getSystemInfo({
				success: function(res) {
					let canvasw = res.windowWidth;
					let canvash = res.windowHeight;
					that.ctx.clearRect(0, 0, canvasw, canvash);
					that.ctx.draw(true);
					that.hasDh = false;
				},
			})
		},
		//完成绘画并保存到本地
		finish:function(){
			let that = this;
			if(!this.hasDh){
				uni.showToast({title:'请先签字',icon:'none'})
				return false;
			}
			uni.showLoading({title:'生成中...',mask:true})
			uni.canvasToTempFilePath({
			  canvasId: 'mycanvas',
			  success: function(res) {
				that.$emit('save',res.tempFilePath);
				//uni.hideLoading();
				that.hasDh = false;
				that.clear();
				that.showCanvas = false;
			  } 
			},this)
		},
	}
}
</script>
<style>
	.signature {position: absolute;top: 0px;left: 2%;z-index: 999;width:96%;display: flex;flex-direction:row;}
	page{
		background: #fff;
	}
	.container {
		padding: 20rpx 0 120rpx 0;
		box-sizing: border-box;
	}
	.title{
		height:50upx;
		line-height: 50upx;
		font-size: 16px;
	}
	.mycanvas{
		width: 80%;
		height: calc(100vh);
		background-color: #ECECEC;
	}
	.leftContent{
		width:20%;
		font-size: 14px;
		height: calc(100vh);
		display: flex;
		justify-content: space-around;
		align-items: center;
		display: flex;flex-direction:column;
	}
	.left,.right,.close{
		line-height: 100upx;
		height: 100upx;
		text-align: center;
		font-weight: bold;
		color: white;
		border-radius: 5upx;
		transform:rotate(90deg);
		width:100%;
	}
	.left{
		background: #007AFF;
	}
	.right{
		background:orange;
	}
	.close {
		background:#A3A3A3;
	}
</style>