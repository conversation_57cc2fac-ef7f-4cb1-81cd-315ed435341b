<template>
    <picker mode="multiSelector" 
            :value="multiIndex" 
            :range="multiArray" 
            @change="handleValueChange"
            @columnchange="handleColumnChange">
        <slot></slot>
    </picker>
</template>

<script>
import areaApi from '@/api/area.js'

// 静态数据作为降级方案
const FALLBACK_REGIONS = require('../pick-regions/regions.json')

export default {
    props: {
        defaultRegions: {
            type: Array,
            default() {
                return []
            }
        },
        defaultRegionCode: {
            type: String
        },
        defaultRegion: [String, Array],
        // 新增：省份代码，用于限制数据范围
        provinceCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            // 三级数据数组
            provinceArr: [],
            cityArr: [],
            districtArr: [],
            // 选择器索引
            multiIndex: [0, 0, 0],
            // 是否初始化状态
            isInitMultiArray: true,
            // 数据缓存
            dataCache: {
                provinces: null,
                cities: {},
                districts: {},
                provinceNames: {} // 省份名称缓存
            },
            // 加载状态
            loading: {
                provinces: false,
                cities: false,
                districts: false
            },
            // 是否使用降级数据
            useFallbackData: false
        }
    },
    watch: {
        defaultRegion: {
            handler(region, oldRegion) {
                if (Array.isArray(region)) {
                    // 避免传的是字面量的时候重复触发
                    oldRegion = oldRegion || []
                    if (region.join('') !== oldRegion.join('')) {
                        this.handleDefaultRegion(region)
                    }
                } else if (region && region.length == 6) {
                    this.handleDefaultRegion(region)
                } else if (region) {
                    console.warn('defaultRegion非有效格式')
                }
            },
            immediate: true,
        },
        provinceCode: {
            handler(newCode, oldCode) {
                if (newCode !== oldCode) {
                    this.initializeData()
                }
            },
            immediate: true
        }
    },
    computed: {
        multiArray() {
            return this.pickedArr.map(arr => arr.map(item => item.name))
        },
        pickedArr() {
            // 进行初始化
            if (this.isInitMultiArray) {
                if (this.useFallbackData) {
                    return [
                        FALLBACK_REGIONS,
                        FALLBACK_REGIONS[0]?.childs || [],
                        FALLBACK_REGIONS[0]?.childs?.[0]?.childs || []
                    ]
                } else {
                    return [
                        this.provinceArr,
                        this.cityArr,
                        this.districtArr
                    ]
                }
            }
            return [this.provinceArr, this.cityArr, this.districtArr];
        }
    },
    async mounted() {
        await this.initializeData()
    },
    methods: {
        // 初始化数据
        async initializeData() {
            try {
                if (this.provinceCode) {
                    // 省内模式：只加载指定省份的城市数据
                    await this.loadProvinceModeData()
                } else {
                    // 全国模式：加载所有省份
                    await this.loadProvinces()
                }
            } catch (error) {
                console.error('初始化地区数据失败:', error)
                this.useFallbackMode()
            }
        },

        // 省内模式数据加载
        async loadProvinceModeData() {
            try {
                // 动态查询省份名称
                const provinceName = await this.getProvinceNameByCode(this.provinceCode)
                this.provinceArr = [{
                    id: this.provinceCode,
                    code: this.provinceCode,
                    name: provinceName,
                    type: '2'
                }]

                // 加载该省的城市数据
                await this.loadCities(this.provinceCode, 0)
            } catch (error) {
                console.error('加载省内数据失败:', error)
                throw error
            }
        },

        // 根据省份代码动态查询省份名称
        async getProvinceNameByCode(code) {
            // 检查缓存中是否已有该省份信息
            const cacheKey = `province_${code}`
            if (this.dataCache.provinceNames && this.dataCache.provinceNames[cacheKey]) {
                return this.dataCache.provinceNames[cacheKey]
            }

            try {
                // 通过API查询指定code的省份信息
                const response = await areaApi.findRegionsByParent({
                    parentId: null,
                    type: '2'
                })

                if (response.code === 0 && response.data) {
                    // 查找匹配的省份
                    const province = response.data.find(p => p.code === code || p.id === code)

                    if (province) {
                        // 缓存查询结果
                        if (!this.dataCache.provinceNames) {
                            this.dataCache.provinceNames = {}
                        }
                        this.dataCache.provinceNames[cacheKey] = province.name

                        return province.name
                    } else {
                        console.warn(`未找到省份代码 ${code} 对应的省份信息`)
                        return `省份代码${code}`
                    }
                } else {
                    throw new Error(response.message || '查询省份信息失败')
                }
            } catch (error) {
                console.error('动态查询省份名称失败:', error)

                // 降级处理：返回默认格式的省份名称
                const fallbackName = this.getFallbackProvinceName(code)

                // 缓存降级结果，避免重复请求
                if (!this.dataCache.provinceNames) {
                    this.dataCache.provinceNames = {}
                }
                this.dataCache.provinceNames[cacheKey] = fallbackName

                return fallbackName
            }
        },

        // 降级处理：生成默认的省份名称
        getFallbackProvinceName(code) {
            // 如果是常见的省份代码，可以提供一些基础映射
            const commonProvinces = {
                '11': '北京市',
                '12': '天津市',
                '22': '吉林省',
                '31': '上海市',
                '50': '重庆市'
            }

            return commonProvinces[code] || `省份代码${code}`
        },

        // 加载省份数据
        async loadProvinces() {
            if (this.dataCache.provinces) {
                this.provinceArr = this.dataCache.provinces
                if (this.provinceArr.length > 0) {
                    await this.loadCities(this.provinceArr[0].id, 0)
                }
                return
            }

            this.loading.provinces = true
            try {
                const response = await areaApi.findRegionsByParent({parentId: null})
                if (response.code === 0 && response.data) {
                    this.provinceArr = response.data
                    this.dataCache.provinces = response.data

                    // 加载第一个省份的城市数据
                    if (this.provinceArr.length > 0) {
                        await this.loadCities(this.provinceArr[0].id, 0)
                    }
                } else {
                    throw new Error(response.message || '获取省份数据失败')
                }
            } catch (error) {
                console.error('加载省份数据失败:', error)
                throw error
            } finally {
                this.loading.provinces = false
            }
        },

        // 加载城市数据
        async loadCities(provinceId, provinceIndex) {
            const cacheKey = provinceId
            if (this.dataCache.cities[cacheKey]) {
                this.cityArr = this.dataCache.cities[cacheKey]
                if (this.cityArr.length > 0) {
                    await this.loadDistricts(this.cityArr[0].id, 0)
                }
                return
            }

            this.loading.cities = true
            try {
                const response = await areaApi.findRegionsByParent({parentId: provinceId})
                if (response.code === 0 && response.data) {
                    this.cityArr = response.data
                    this.dataCache.cities[cacheKey] = response.data

                    // 加载第一个城市的区县数据
                    if (this.cityArr.length > 0) {
                        await this.loadDistricts(this.cityArr[0].id, 0)
                    }
                } else {
                    throw new Error(response.message || '获取城市数据失败')
                }
            } catch (error) {
                console.error('加载城市数据失败:', error)
                // 城市加载失败时，设置空数组
                this.cityArr = []
                this.districtArr = []
            } finally {
                this.loading.cities = false
            }
        },

        // 加载区县数据
        async loadDistricts(cityId, cityIndex) {
            const cacheKey = cityId
            if (this.dataCache.districts[cacheKey]) {
                this.districtArr = this.dataCache.districts[cacheKey]
                return
            }

            this.loading.districts = true
            try {
                const response = await areaApi.findRegionsByParent({parentId: cityId})
                if (response.code === 0 && response.data) {
                    this.districtArr = response.data
                    this.dataCache.districts[cacheKey] = response.data
                } else {
                    throw new Error(response.message || '获取区县数据失败')
                }
            } catch (error) {
                console.error('加载区县数据失败:', error)
                // 区县加载失败时，设置空数组
                this.districtArr = []
            } finally {
                this.loading.districts = false
            }
        },

        // 使用降级模式
        useFallbackMode() {
            console.warn('网络请求失败，使用静态数据作为降级方案')
            this.useFallbackData = true
            
            if (this.provinceCode) {
                // 省内模式下的降级处理
                const targetProvince = FALLBACK_REGIONS.find(p => p.code === this.provinceCode)
                if (targetProvince) {
                    this.provinceArr = [targetProvince]
                    this.cityArr = targetProvince.childs || []
                    this.districtArr = this.cityArr[0]?.childs || []
                } else {
                    // 如果找不到指定省份，使用第一个省份
                    this.provinceArr = FALLBACK_REGIONS.slice(0, 1)
                    this.cityArr = this.provinceArr[0]?.childs || []
                    this.districtArr = this.cityArr[0]?.childs || []
                }
            } else {
                // 全国模式下的降级处理
                this.provinceArr = FALLBACK_REGIONS
                this.cityArr = FALLBACK_REGIONS[0]?.childs || []
                this.districtArr = this.cityArr[0]?.childs || []
            }
            
            uni.showToast({
                title: '网络异常，使用离线数据',
                icon: 'none',
                duration: 2000
            })
        },

        // 处理列变化
        async handleColumnChange(e) {
            this.isInitMultiArray = false
            const col = e.detail.column
            const row = e.detail.value
            this.multiIndex[col] = row

            try {
                switch (col) {
                    case 0: // 省份变化
                        if (this.useFallbackData) {
                            // 降级模式处理
                            if (FALLBACK_REGIONS[this.multiIndex[0]]?.childs?.length === 0) {
                                this.cityArr = this.districtArr = [FALLBACK_REGIONS[this.multiIndex[0]]]
                                break
                            }
                            this.cityArr = FALLBACK_REGIONS[this.multiIndex[0]]?.childs || []
                            this.districtArr = this.cityArr[this.multiIndex[1]]?.childs || []
                        } else {
                            // 动态加载模式
                            const selectedProvince = this.provinceArr[this.multiIndex[0]]
                            if (selectedProvince) {
                                await this.loadCities(selectedProvince.id, this.multiIndex[0])
                                this.multiIndex[1] = 0 // 重置城市索引
                                this.multiIndex[2] = 0 // 重置区县索引
                            }
                        }
                        break
                    case 1: // 城市变化
                        if (this.useFallbackData) {
                            // 降级模式处理
                            this.districtArr = FALLBACK_REGIONS[this.multiIndex[0]]?.childs?.[this.multiIndex[1]]?.childs || []
                        } else {
                            // 动态加载模式
                            const selectedCity = this.cityArr[this.multiIndex[1]]
                            if (selectedCity) {
                                await this.loadDistricts(selectedCity.id, this.multiIndex[1])
                                this.multiIndex[2] = 0 // 重置区县索引
                            }
                        }
                        break
                    case 2: // 区县变化
                        // 区县变化不需要加载新数据
                        break
                }
            } catch (error) {
                console.error('处理列变化失败:', error)
                // 发生错误时，尝试使用降级模式
                if (!this.useFallbackData) {
                    this.useFallbackMode()
                }
            }
        },

        // 处理值变化
        handleValueChange(e) {
            const [index0, index1, index2] = e.detail.value
            const [arr0, arr1, arr2] = this.pickedArr
            
            if (arr0[index0] && arr1[index1] && arr2[index2]) {
                const address = [arr0[index0], arr1[index1], arr2[index2]]
                this.$emit('getRegion', address)
            }
        },

        // 处理默认地区设置
        async handleDefaultRegion(region) {
            const isCode = !Array.isArray(region)
            this.isInitMultiArray = false

            try {
                if (isCode) {
                    // 处理6位地区编码
                    await this.handleDefaultRegionByCode(region)
                } else {
                    // 处理地区名称数组
                    await this.handleDefaultRegionByNames(region)
                }
            } catch (error) {
                console.error('设置默认地区失败:', error)
                // 设置失败时使用初始化状态
                this.isInitMultiArray = true
            }
        },

        // 根据地区编码设置默认值
        async handleDefaultRegionByCode(regionCode) {
            // 解析6位编码：前2位省份，中间2位城市，后2位区县
            const provinceCode = regionCode.slice(0, 2)
            const cityCode = regionCode.slice(0, 4)
            const districtCode = regionCode

            // 确保数据已加载
            if (!this.provinceCode) {
                await this.loadProvinces()
            }

            // 查找并设置省份索引
            const provinceIndex = this.provinceArr.findIndex(p => p.code === provinceCode)
            if (provinceIndex !== -1) {
                this.multiIndex[0] = provinceIndex
                const selectedProvince = this.provinceArr[provinceIndex]
                await this.loadCities(selectedProvince.id, provinceIndex)

                // 查找并设置城市索引
                const cityIndex = this.cityArr.findIndex(c => c.code === cityCode)
                if (cityIndex !== -1) {
                    this.multiIndex[1] = cityIndex
                    const selectedCity = this.cityArr[cityIndex]
                    await this.loadDistricts(selectedCity.id, cityIndex)

                    // 查找并设置区县索引
                    const districtIndex = this.districtArr.findIndex(d => d.code === districtCode)
                    if (districtIndex !== -1) {
                        this.multiIndex[2] = districtIndex
                    }
                }
            }
        },

        // 根据地区名称数组设置默认值
        async handleDefaultRegionByNames(regionNames) {
            if (regionNames.length < 3) return

            const [provinceName, cityName, districtName] = regionNames

            // 确保数据已加载
            if (!this.provinceCode) {
                await this.loadProvinces()
            }

            // 查找并设置省份索引
            const provinceIndex = this.provinceArr.findIndex(p => p.name.includes(provinceName))
            if (provinceIndex !== -1) {
                this.multiIndex[0] = provinceIndex
                const selectedProvince = this.provinceArr[provinceIndex]
                await this.loadCities(selectedProvince.id, provinceIndex)

                // 查找并设置城市索引
                const cityIndex = this.cityArr.findIndex(c => c.name.includes(cityName))
                if (cityIndex !== -1) {
                    this.multiIndex[1] = cityIndex
                    const selectedCity = this.cityArr[cityIndex]
                    await this.loadDistricts(selectedCity.id, cityIndex)

                    // 查找并设置区县索引
                    const districtIndex = this.districtArr.findIndex(d => d.name.includes(districtName))
                    if (districtIndex !== -1) {
                        this.multiIndex[2] = districtIndex
                    }
                }
            }
        }
    },
    
    // 组件销毁时清理缓存
    beforeDestroy() {
        this.dataCache = {
            provinces: null,
            cities: {},
            districts: {},
            provinceNames: {}
        }
    }
}
</script>
