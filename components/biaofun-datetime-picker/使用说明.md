### 组件说明
* 日期时间选择器
* 组件的默认日期有效期范围为："1970-01-01 00:00" - "2300-01-01 00:00"。
* 注意：如果您传递的日期有效范围的结束日期小于开始日期，则日期有效范围的结束日期会自动修正为开始日期+300年，比如，您传递的日期有效范围的开始日期为 "2020-11-11 18:30"，
* 结束日期为 "2018-11-11 18:30"，则此时将会自动修正结束日期为 "2320-11-11 18:30"。
* 注意：如果您传递的默认值不在日期有效范围内，则会自动修正默认值为当前日期时间，如果当前日期时间也不在日期有效范围内，则会再次修正为日期有效范围的开始日期。
* 注意：该组件用到了我自己封装的 utils.js，位置在 '@/common/js/utils.js'。


### 插件 props 属性
* disabled: 是否禁用该组件？Boolean类型；
* placeholder: 组件没有选中值时显示的内容，String类型；
* start: 表示有效日期时间范围的开始，String类型，格式为 "YYYY-MM-DD hh:mm"；
* end: 表示有效日期时间范围的结束，String类型，格式必为 "YYYY-MM-DD hh:mm"；
* fields: 选择器的粒度，String类型，有效值为 year、month、day、hour、minute；
* defaultValue: 默认值，String类型，格式为 "YYYY-MM-DD hh:mm"；

### 插件事件
- change(date)：选择日期时间后的回调事件。
 * date.YYYY: 年；
 * date.M: 月；
 * date.MM: 月（补0）；
 * date.D: 日；
 * date.DD: 日（补0）；
 * date.h: 时；
 * date.hh: 时（补0）；
 * date.m: 分；
 * date.mm: 分（补0）；
 * date.dt: Date对象；
 * ... 还有一些其他的字段，具体看返回值吧！