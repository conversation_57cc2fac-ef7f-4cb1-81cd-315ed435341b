<template>
	<view class="upload-img">
		<view class="ck-img" v-if="videoSrc!=''">
			<video :src="videoSrc"></video>
			<view class="ck-icon-close" @click="handRemove()">
				<uni-icons type="closeempty" size="25" style="color:grey;"></uni-icons>
			</view>
		</view>
		<view v-else class="ck-img-add" :style="iconStatusStyle" @click="chooseVideo" >
		</view>
	</view>
</template>

<script>
	import qiniuUploader from './qiniuUploader.js';
	var config = require('../../common/config.js')
	export default {
		name: 'ck-upload-video',
		components: {},
		data() {
			return {
				imgPath:config.IMG_PATH,
				imgDefault:'img/fileAdd.png',
				videoSrc:"",
			};
		},
		props: {
			initUrl: { //页图初始化图片回显
				type: String,
				default: '',
			},
			token: {
				//七牛云从后台服务器获取的token,不知道的看官方文档，
				type: String,
				default: '',
			},
			tableName: {
				type: String,
				default: "",
			},
		},
		computed: {
			iconStatusStyle(){
				let result="background-image:url('"+this.imgPath+this.imgDefault+"');"
				return result;
			},
		},
		created() {
			//this.init();
		},
		methods: {
			init(url) {
				//传入图片，回显
				console.log("url---",url);
				this.videoSrc=url;
			},
			chooseVideo(){
				// 上传视频
				console.log('上传视频')
				uni.chooseVideo({
					//maxDuration:10,
					//count: 1,
					//camera: this.cameraList[this.cameraIndex].value,
					sourceType: ['album'],
					success: (res) => {
						this.videoSrc = res.tempFilePath;
						let file = res.tempFilePath;
						let index = file.lastIndexOf('/');
						let len = file.length
						let name = file.substring(index + 1, len)
						
						let date = new Date();
						let y = date.getFullYear() + '-';
						let m = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) + '-' : date.getMonth() + 1 + '-';
						let d = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
						let qiniuImageURLPrefix = y+m+d+"/"+this.tableName+"/";
						qiniuUploader.init({qiniuImageURLPrefix:qiniuImageURLPrefix,region:"NCN"});
						qiniuUploader.upload(file,(res) => {
							this.$emit('returnUrl', res.key) //将图片回传父页面
						}, (error) => {
							console.log('error: ' + error);
						}, {
							region: 'NCN',
							key: name,
							uptoken: this.token, // 由其他程序生成七牛 uptoken
						}, (res) => {
							console.log('上传进度', res.progress)
						}, () => {
							// 取消上传
						}, () => {
							// `before` 上传前执行的操作
						}, (err) => {
							// `complete` 上传接受后执行的操作(无论成功还是失败都执行)
						});
						
					}
				})
			},
			handRemove() {
				this.videoSrc = '';
				this.$emit('remove') //将图片回传父页面
			},
		}
	}
</script>

<style>
	.upload-img {
		margin: 5upx 20upx;
	}

	.ck-img {
		float: left;
		margin-right: 20upx;
		border-radius: 20upx;
		/* height: 115upx; */
		display: flex;
		flex-direction:row;
	}

	.ck-img>image {
		border-radius: 20upx;
	}

	.ck-img-add {
		float: left;
		margin-right: 10upx;
		margin-left: 20upx;
		height: 100upx;
		width: 100upx;
		/* background-color: #C8C7CC; */
		text-align: center;
		font-size: 90upx;
		border-radius: 20upx;
		margin-bottom: 10upx;
		/* background-image: url(../../static/img/xj.png); */
		background-size: 100% 100%;
	}

	.ck-img>image {
		height: 100upx;
		width: 100upx;
		margin-left: 20upx;
	}

	.ck-icon-close {
		position: relative;
		/* right: -120upx;
		top: -120upx; */
		top:0;
		color: #DC2929;
	}

	.ck-icon-close>view {
		font-size: 40upx;
	}
	
	
</style>
