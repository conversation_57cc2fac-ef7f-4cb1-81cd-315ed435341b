<template>
	<view>
		<view class="upload-file">
			<view>
				<view class="ck-file" v-for="(item, index) in fileList" :key="index" @click="previewFile(index)">
					<view class="file-info">
						<view class="file-icon">
							<text v-if="isImage(item.name)" class="icon image-icon">📷</text>
							<text v-else-if="isPDF(item.name)" class="icon pdf-icon">📄</text>
							<text v-else class="icon file-icon-default">📎</text>
						</view>
						<view class="file-name">{{item.name || getFileName(item.url)}}</view>
						<view v-if="isPDF(item.name)" class="file-type-badge">PDF</view>
					</view>
					<view class="ck-icon-close" @click.stop="handRemove(index)">
						<uni-icons type="closeempty" size="15" style="color:grey;"></uni-icons>
					</view>
				</view>
				<view class="ck-file-add" @click="chooseFile" v-if="selectNum > fileList.length">
					<view class="add-icon">+</view>
					<view class="add-text">{{addButtonText}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import qiniuUploader from './qiniuUploader.js';
	var config = require('../../common/config.js')
	export default {
		name: 'ck-upload-file',
		components: {},
		data() {
			return {
				imgPath: config.IMG_PATH,
				fileList: [],
				fileKeyList: [],
				num: 0,
			};
		},
		props: {
			initFileList: { //初始化文件回显
				type: Array,
				default () {
					return []
				}
			},
			selectNum: {
				//选择文件的数量
				type: Number,
				default: 1,
			},
			token: {
				//七牛云从后台服务器获取的token
				type: String,
				default: '',
			},
			tableName: {
				type: String,
				default: "",
			},
			// 新增：允许的文件类型
			allowedTypes: {
				type: Array,
				default() {
					return ['pdf','image']; // 默认只允许PDF，保持向后兼容
				}
			},
		},
		computed: {
			// 动态生成添加按钮文本
			addButtonText() {
				const hasImage = this.allowedTypes.includes('image');
				const hasPdf = this.allowedTypes.includes('pdf');

				if (hasImage && hasPdf) {
					return '选择图片或PDF文档';
				} else if (hasImage) {
					return '选择图片';
				} else {
					return '选择PDF文档';
				}
			}
		},
		created() {
			this.num = this.selectNum;
			this.initFile();
		},
		methods: {
			initFile() {
        if (this.initFileList && this.initFileList.length > 0) {
          //传入文件，回显
          this.fileList = this.initFileList.map(item => {
            if (typeof item === 'string') {
              return { url: item, name: this.getFileName(item) };
            }
            // 如果是对象，优先使用fileName字段，如果没有则从url中提取
            return {
              url: item.fileUrl,
              name: item.fileName || this.getFileName(item.fileUrl),
              key: item.filePath
            };
          });
          this.num = this.num - this.fileList.length;
          for (var i = 0; i < this.fileList.length; i++) {
            let name = this.getUrlKey(this.fileList[i].url || this.fileList[i]);
            this.fileKeyList.push(name)
          }
        }
			},
			chooseFile() {
				// 根据允许的文件类型选择相应的方法
				const hasImage = this.allowedTypes.includes('image');
				const hasPdf = this.allowedTypes.includes('pdf');

				if (hasImage && hasPdf) {
					// 同时支持图片和PDF，显示选择菜单
					this.showFileTypeMenu();
				} else if (hasImage) {
					// 只支持图片
					this.chooseImage();
				} else {
					// 只支持PDF（默认行为）
					this.chooseDocument();
				}
			},

			// 显示文件类型选择菜单
			showFileTypeMenu() {
				uni.showActionSheet({
					itemList: ['选择图片', '选择PDF文档'],
					success: (res) => {
						if (res.tapIndex === 0) {
							this.chooseImage();
						} else if (res.tapIndex === 1) {
							this.chooseDocument();
						}
					}
				});
			},

			// 选择图片
			chooseImage() {
				uni.chooseImage({
					count: this.num,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						// 验证文件大小并上传
            console.log("res.tempFilePaths", res.tempFilePaths);
						this.checkFileSizeAndUpload(res.tempFilePaths, null, 'image');
					},
					fail: (err) => {
						console.log('选择图片失败:', err);
						uni.showToast({
							title: '选择图片失败，请重试',
							icon: 'none'
						});
					}
				});
			},

			chooseDocument() {
				// #ifdef MP-WEIXIN
				// 微信小程序支持文档选择
        wx.chooseMessageFile({
					count: this.num,
					type: 'file',
					extension: ['pdf'],
					success: (res) => {
						// 验证选择的文件是否都是PDF格式
						const invalidFiles = res.tempFiles.filter(file => !this.validatePDFFile(file));
						if (invalidFiles.length > 0) {
							uni.showToast({
								title: '只能上传PDF格式文件',
								icon: 'none',
								duration: 2000
							});
							return;
						}
						const filePaths = res.tempFiles.map(file => file.path);
						this.checkFileSizeAndUpload(filePaths, res.tempFiles, 'pdf');
					},
					fail: (err) => {
						console.log('选择文件失败:', err);
						uni.showToast({
							title: '选择文件失败，请重试',
							icon: 'none'
						});
					}
				});
				// #endif

				// #ifndef MP-WEIXIN
				// 其他平台提示用户
				uni.showModal({
					title: '提示',
					content: '当前平台暂不支持PDF文档选择，建议使用微信小程序',
					showCancel: false
				});
				// #endif
			},
			checkFileSizeAndUpload(filePaths, fileInfos = null, fileType = 'pdf') {
				// 检查文件大小，最大5MB
				const maxSize = 5 * 1024 * 1024; // 5MB
				let validFiles = [];
				let validFileInfos = [];

				// 检查文件大小
				let checkPromises = filePaths.map((filePath, index) => {
					return new Promise((resolve) => {
						// 对于图片类型，使用uni.getImageInfo；对于其他文件，使用wx.getFileSystemManager
						if (fileType === 'image') {
							uni.getImageInfo({
								src: filePath,
								success: (res) => {
									// 图片文件大小检查（如果有size属性）
									if (!res.size || res.size <= maxSize) {
										validFiles.push(filePath);
										if (fileInfos && fileInfos[index]) {
											validFileInfos.push(fileInfos[index]);
										}
									} else {
										uni.showToast({
											title: `图片过大，最大支持5MB`,
											icon: 'none',
											duration: 2000
										});
									}
									resolve();
								},
								fail: () => {
									// 如果获取图片信息失败，仍然允许上传（向后兼容）
									validFiles.push(filePath);
									if (fileInfos && fileInfos[index]) {
										validFileInfos.push(fileInfos[index]);
									}
									resolve();
								}
							});
						} else {
							// PDF文件使用原有逻辑
							wx.getFileSystemManager().getFileInfo({
								filePath: filePath,
								success: (res) => {
									if (res.size <= maxSize) {
										validFiles.push(filePath);
										if (fileInfos && fileInfos[index]) {
											validFileInfos.push(fileInfos[index]);
										}
									} else {
										uni.showToast({
											title: `文件过大，最大支持5MB`,
											icon: 'none',
											duration: 2000
										});
									}
									resolve();
								},
								fail: () => {
									// 如果获取文件信息失败，仍然允许上传（向后兼容）
									validFiles.push(filePath);
									if (fileInfos && fileInfos[index]) {
										validFileInfos.push(fileInfos[index]);
									}
									resolve();
								}
							});
						}
					});
				});

				Promise.all(checkPromises).then(() => {
					if (validFiles.length > 0) {
						this.uploadFiles(validFiles, validFileInfos.length > 0 ? validFileInfos : null);
					}
				});
			},
			uploadFiles(filePaths, fileInfos = null) {
				this.num = this.num - filePaths.length;
				for (var i = 0; i < filePaths.length; i++) {
					let file = filePaths[i];
					let fileName = '';
					
					if (fileInfos && fileInfos[i]) {
						fileName = fileInfos[i].name;
					} else {
						let index = file.lastIndexOf('/');
						let len = file.length;
						fileName = file.substring(index + 1, len);
					}
					
					if (this.num >= 0) {
						let date = new Date();
						let y = date.getFullYear() + '-';
						let m = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) + '-' : date.getMonth() + 1 + '-';
						let d = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
						let qiniuImageURLPrefix = y+m+d+"/"+this.tableName+"/";
						
						qiniuUploader.init({qiniuImageURLPrefix:qiniuImageURLPrefix,region:"NCN"});
						qiniuUploader.upload(file, (res) => {
							let inLen = this.selectNum - this.fileList.length;
							if (inLen > 0) {
								this.fileList.push({
									url: file,
									name: fileName,
									key: res.key
								});
								this.fileKeyList.push(res.key);
							}
							// 传递文件信息数组，包含URL、原始文件名和文件类型
							const fileInfoArray = this.fileList.map((item, index) => ({
								url: this.fileKeyList[index] || item.key || item.url,
								fileName: item.name,
								fileType: this.getFileType(item.name)
							}));
							this.$emit('returnFileUrl', this.fileKeyList, fileInfoArray);
						}, (error) => {
							console.log('error: ' + error);
							uni.showToast({
								title: '上传失败',
								icon: 'none'
							});
						}, {
							region: 'NCN',
							key: fileName,
							uptoken: this.token,
						}, (res) => {
							console.log('上传进度', res.progress)
						}, () => {
							// 取消上传
						}, () => {
							// `before` 上传前执行的操作
						}, (err) => {
							// `complete` 上传接受后执行的操作(无论成功还是失败都执行)
						});
					}
				}
			},
			previewFile(index) {
				const file = this.fileList[index];
				if (this.isImage(file.name)) {
					// 预览图片
					let urls = [file.url];
					uni.previewImage({
						urls: urls
					});
				} else if (this.isPDF(file.name)) {
					// PDF文件预览
					this.previewPDF(file);
				} else {
					// 其他文档类型
					uni.showToast({
						title: '点击下载查看文档',
						icon: 'none'
					});
				}
			},

			previewPDF(file) {
				// 显示加载提示
				uni.showLoading({
					title: '正在加载PDF...',
					mask: true
				});

				// 构建完整的文件URL
				let fileUrl = '';
				if (file.url && file.url.startsWith('http')) {
					fileUrl = file.url;
				} else if (file.key) {
					fileUrl = this.imgPath + file.key;
				} else if (file.url) {
					fileUrl = this.imgPath + file.url;
				} else {
					uni.hideLoading();
					uni.showToast({
						title: '文件路径不存在',
						icon: 'none'
					});
					return;
				}

				// 微信小程序环境
				// #ifdef MP-WEIXIN
				uni.downloadFile({
					url: fileUrl,
					success: (res) => {
						uni.hideLoading();
						if (res.statusCode === 200) {
							uni.openDocument({
								filePath: res.tempFilePath,
								fileType: 'pdf',
								success: () => {
									console.log('PDF预览成功');
								},
								fail: (err) => {
									console.error('PDF预览失败:', err);
									uni.showModal({
										title: '预览失败',
										content: '无法预览PDF文件，是否下载到本地查看？',
										success: (modalRes) => {
											if (modalRes.confirm) {
												this.downloadPDF(fileUrl);
											}
										}
									});
								}
							});
						} else {
							uni.showToast({
								title: '下载文件失败',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						uni.hideLoading();
						console.error('下载PDF失败:', err);
						uni.showToast({
							title: '下载失败，请检查网络',
							icon: 'none'
						});
					}
				});
				// #endif

				// H5环境
				// #ifdef H5
				uni.hideLoading();
				// 在新窗口中打开PDF
				window.open(fileUrl, '_blank');
				// #endif

				// APP环境
				// #ifdef APP-PLUS
				uni.downloadFile({
					url: fileUrl,
					success: (res) => {
						uni.hideLoading();
						if (res.statusCode === 200) {
							uni.openDocument({
								filePath: res.tempFilePath,
								success: () => {
									console.log('PDF预览成功');
								},
								fail: (err) => {
									console.error('PDF预览失败:', err);
									uni.showToast({
										title: '无法预览PDF文件',
										icon: 'none'
									});
								}
							});
						}
					},
					fail: (err) => {
						uni.hideLoading();
						console.error('下载PDF失败:', err);
						uni.showToast({
							title: '下载失败，请检查网络',
							icon: 'none'
						});
					}
				});
				// #endif

				// 其他平台
				// #ifndef MP-WEIXIN || H5 || APP-PLUS
				uni.hideLoading();
				uni.showModal({
					title: '提示',
					content: '当前平台不支持PDF预览，是否复制链接到浏览器查看？',
					success: (res) => {
						if (res.confirm) {
							uni.setClipboardData({
								data: fileUrl,
								success: () => {
									uni.showToast({
										title: '链接已复制',
										icon: 'success'
									});
								}
							});
						}
					}
				});
				// #endif
			},

			downloadPDF(fileUrl) {
				// 提供下载功能（主要用于微信小程序）
				// #ifdef MP-WEIXIN
				uni.showModal({
					title: '下载提示',
					content: '请长按保存PDF文件到本地',
					showCancel: false
				});
				// #endif
			},
			handRemove(index) {
				this.num++;
				let fl = [], fkl = [];
				for (var i = 0; i < this.fileList.length; i++) {
					if (i != index) {
						fl.push(this.fileList[i]);
						fkl.push(this.fileKeyList[i]);
					}
				}
				this.fileList = fl;
				this.fileKeyList = fkl;
				this.$emit('removeFile', index);
				// 传递文件信息数组，包含URL、原始文件名和文件类型
				const fileInfoArray = this.fileList.map((item, index) => ({
					url: this.fileKeyList[index] || item.key || item.url,
					fileName: item.name,
					fileType: this.getFileType(item.name)
				}));
				this.$emit('returnFileUrl', this.fileKeyList, fileInfoArray);
			},
			getUrlKey(file) {
				let index = file.lastIndexOf('.com');
				let len = file.length;
				let name = file.substring(index + 5, len);
				return name;
			},
			getFileName(filePath) {
				if (!filePath) return '';
				let index = filePath.lastIndexOf('/');
				return index !== -1 ? filePath.substring(index + 1) : filePath;
			},
			isImage(fileName) {
				if (!fileName) return false;
				const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
				const ext = fileName.toLowerCase().split('.').pop();
				return imageExts.includes(ext);
			},
			isPDF(fileName) {
				if (!fileName) return false;
				return fileName.toLowerCase().endsWith('.pdf');
			},

			// 验证PDF文件（通过文件对象）
			validatePDFFile(file) {
				if (!file) return false;
				// 检查文件名扩展名
				if (file.name && !file.name.toLowerCase().endsWith('.pdf')) {
					return false;
				}
				return true;
			},

			// 验证文件类型是否被允许
			validateFileType(fileName) {
				if (!fileName) return false;

				const ext = fileName.toLowerCase().split('.').pop();

				// 检查是否为图片格式
				if (this.allowedTypes.includes('image')) {
					const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
					if (imageExts.includes(ext)) {
						return true;
					}
				}

				// 检查是否为PDF格式
				if (this.allowedTypes.includes('pdf')) {
					if (ext === 'pdf') {
						return true;
					}
				}

				return false;
			},

			// 根据文件名获取文件类型
			getFileType(fileName) {
				if (!fileName) return 'file';

				// 检查是否为图片
				if (this.isImage(fileName)) {
					return 'photo';
				}

				// 检查是否为PDF
				if (this.isPDF(fileName)) {
					return 'pdf';
				}

				// 其他文件类型
				return 'file';
			},
		}
	}
</script>

<style>
	.upload-file {
		margin: 5upx 20upx;
	}

	.ck-file {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20upx;
		margin-bottom: 20upx;
		border: 2upx solid #e0e0e0;
		border-radius: 10upx;
		background-color: #f9f9f9;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	.ck-file:hover {
		background-color: #f0f0f0;
		border-color: #02994f;
	}

	.ck-file:active {
		transform: scale(0.98);
	}

	.file-info {
		display: flex;
		align-items: center;
		flex: 1;
		position: relative;
	}

	.file-icon {
		margin-right: 20upx;
	}

	.icon {
		font-size: 40upx;
	}

	.image-icon {
		color: #4CAF50;
	}

	.pdf-icon {
		color: #e74c3c;
	}

	.file-icon-default {
		color: #666;
	}

	.file-name {
		font-size: 28upx;
		color: #333;
		word-break: break-all;
		flex: 1;
	}

	.file-type-badge {
		position: absolute;
		right: 0;
		top: 0;
		background-color: #e74c3c;
		color: white;
		font-size: 20upx;
		padding: 4upx 8upx;
		border-radius: 6upx;
		transform: translateY(-50%);
	}

	.ck-file-add {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 120upx;
		border: 2upx dashed #ccc;
		border-radius: 10upx;
		background-color: #fafafa;
	}

	.add-icon {
		font-size: 60upx;
		color: #999;
		margin-bottom: 10upx;
	}

	.add-text {
		font-size: 24upx;
		color: #999;
	}

	.ck-icon-close {
		padding: 10upx;
		color: #DC2929;
	}
</style>
