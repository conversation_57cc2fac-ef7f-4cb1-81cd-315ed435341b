<template>
	<view>
		<view class="upload-img">
			<view>
				<view class="ck-img" v-for="(item, index) in imgList" :key="index" @click="perviewImg(index)">
					<image :src="item" v-if="item" :style="iconStyle"></image>
					<view class="ck-icon-close" @click.stop="handRemove(index)">
						<uni-icons type="closeempty" size="15" style="color:grey;"></uni-icons>
					</view>
				</view>
				<view class="ck-img-add" :style="iconStatusStyle" @click="chooseImage" v-if="selectNum > imgList.length">
					<view class="cuIcon-cameraadd"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import qiniuUploader from './qiniuUploader.js';
	var config = require('../../common/config.js')
	export default {
		name: 'ck-upload-img',
		components: {},
		data() {
			return {
				imgPath:config.IMG_PATH,
				imgList: [],
				imgKeyList: [],
				num: 0,
				imgIdCardFront:'img/idCardFront.png',
				imgIdCardBack:'img/idCardBack.png',
				imgCardNo:'img/cardNo.png',
				imgSeal:'img/seal.png',
				imgDefault:'img/xj.png',
			};
		},
		props: {
			initImgList: { //页图初始化图片回显
				type: Array,
				default () {
					return []
				}
			},
			selectNum: {
				//选择图片的数量
				type: Number,
				default: 1,
			},
			token: {
				//七牛云从后台服务器获取的token,不知道的看官方文档，
				type: String,
				default: '',
			},
			tableName: {
				type: String,
				default: "",
			},
			customIcon: {
				type: String,
				default: "",
			},
			iconWidth: {
				type: String,
				default: "100rpx",
			},
			iconHeight: {
				type: String,
				default: "100rpx",
			},
		},
		computed: {
			iconStyle(){
				let result="";
				result += "height: "+this.iconHeight+" !important;"
				result += "width: "+this.iconWidth+"  !important;"
				return result;
			},
			iconStatusStyle(){
				let result="";
				/* result += "float: left;"
				result += "margin-right: 10rpx;"
				result += "text-align: center;"
				result += "font-size: 90rpx;"
				result += "border-radius: 20rpx;"
				result += "margin-bottom: 10rpx;"
				result += "background-size: 100% 100%;" */
				if(this.customIcon === 'idCardFront'){
					result += "background-image:url('"+this.imgPath+this.imgIdCardFront+"');"
				}else if(this.customIcon === 'idCardBack'){
					result += "background-image:url('"+this.imgPath+this.imgIdCardBack+"');"
				}else if(this.customIcon === 'cardNo'){
					result += "height: 105px !important;"
					result += "width: 200px  !important;"
					result += "background-image:url('"+this.imgPath+this.imgCardNo+"');"
				}else if(this.customIcon === 'seal'){
					result += "background-image:url('"+this.imgPath+this.imgSeal+"');"
				}else{
					result += "background-image:url('"+this.imgPath+this.imgDefault+"');"
				}
				return result;
			},
		},
		created() {
			this.num = this.selectNum;
			this.initImg();
		},
		methods: {
			initImg() {
				//传入图片，回显
				this.imgList = this.initImgList;
				this.num = this.num - this.imgList.length;
				for (var i = 0; i < this.imgList.length; i++) {
					let name = this.getUrlKey(this.imgList[i]);
					this.imgKeyList.push(name)
				}
				console.log('selectNum=' + this.selectNum)
			},
			chooseImage() {
				uni.chooseImage({
					count: this.num, //默认9
					success: (chooseImageRes) => {
						this.num = this.num - chooseImageRes.tempFilePaths.length
						for (var i = 0; i < chooseImageRes.tempFilePaths.length; i++) {
							let file = chooseImageRes.tempFilePaths[i];
							let index = file.lastIndexOf('/');
							let len = file.length
							let name = file.substring(index + 1, len)
							// 交给七牛上传
							//let newLen = this.num-this.imgList.length
							if (this.num >= 0) {
								let date = new Date();
								let y = date.getFullYear() + '-';
								let m = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) + '-' : date.getMonth() + 1 + '-';
								let d = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
								let qiniuImageURLPrefix = y+m+d+"/"+this.tableName+"/";
								console.log("qiniuImageURLPrefix-----",qiniuImageURLPrefix)
								qiniuUploader.init({qiniuImageURLPrefix:qiniuImageURLPrefix,region:"NCN"});
								qiniuUploader.upload(file, (res) => {
									console.log(res);
									let inLen = this.selectNum-this.imgList.length
									if (inLen > 0) {
										this.imgList.push(file);
										this.imgKeyList.push(res.key)
									}
									this.$emit('returnImgUrl', this.imgKeyList) //将图片回传父页面
								}, (error) => {
									console.log('error: ' + error);
								}, {
									region: 'NCN',
									key: name,
									uptoken: this.token, // 由其他程序生成七牛 uptoken
								}, (res) => {
									console.log('上传进度', res.progress)
								}, () => {
									// 取消上传
								}, () => {
									// `before` 上传前执行的操作
								}, (err) => {
									// `complete` 上传接受后执行的操作(无论成功还是失败都执行)
								});
							}
						}
					}
				})
			},
			perviewImg(index) {
				let urls = [];
				if (index != -1) {
					urls[0] = this.imgList[index];
				} else {
					urls = this.imgList;
				}
				uni.previewImage({
					urls: urls
				});
			},
			handRemove(index) {
				this.num++
				console.log(this.num)
				let il = [],
					ibl = [];
				for (var i = 0; i < this.imgList.length; i++) {
					if (i != index) {
						il.push(this.imgList[i]);
						ibl.push(this.imgKeyList[i]);
					}
				}
				this.imgList = il;
				this.imgKeyList = ibl;
				this.$emit('removeImg',index) //将图片回传父页面
			},
			getUrlKey(file) {
				let index = file.lastIndexOf('.com');
				let len = file.length
				let name = file.substring(index + 5, len)
				return name;
			}
		}
	}
</script>

<style>
	.upload-img {
		margin: 5upx 20upx;
	}

	.ck-img {
		float: left;
		margin-right: 20upx;
		border-radius: 20upx;
		height: 115upx;
		display: flex;
		flex-direction:row;
	}

	.ck-img>image {
		border-radius: 20upx;
	}

	.ck-img-add {
		float: left;
		margin-right: 10upx;
		margin-left: 20upx;
		height: 100upx;
		width: 100upx;
		/* background-color: #C8C7CC; */
		text-align: center;
		font-size: 90upx;
		border-radius: 20upx;
		margin-bottom: 10upx;
		/* background-image: url(../../static/img/xj.png); */
		background-size: 100% 100%;
	}

	.ck-img>image {
		height: 100upx;
		width: 100upx;
		margin-left: 20upx;
	}

	.ck-icon-close {
		position: relative;
		/* right: -120upx;
		top: -120upx; */
		top:0;
		color: #DC2929;
	}

	.ck-icon-close>view {
		font-size: 40upx;
	}
</style>
