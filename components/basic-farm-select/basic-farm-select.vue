<template>
	<uni-popup ref="popupRef" type="bottom" :custom="true" :mask-click="false">
		<view class="pop">
			<view class="btns">
				<view class="pop-button cancel" @click="cancel">取消</view>
				<view class="pop-button sure" @click="confirm" style="color: #09BB07;">确定</view>
			</view>
			<view class="pop-content">
				<scroll-view scroll-with-animation scroll-y class="left-aside" :scroll-top="tabScrollTop">
					<select-tree @choose='choose' :selectList='selectList' ref='selectTree'></select-tree>
				</scroll-view>
			</view>
		</view>
	</uni-popup>
</template>

<script>
	import uniPopup from '@/components/uni-popup/uni-popup.vue';
	import selectTree from "@/components/select-tree/select-tree";
	export default {
		components: {
			uniPopup,
			selectTree,
		},
		props: {
			selectList: {
				type: Array,
				default: function() {
					return [];
				}
			}
		},
		data() {
			return {
				vSelect: [],
				tabScrollTop: 100,
			}
		},
		methods: {
			popShow() {
				console.log("show")
				this.$refs.popupRef.open();
			},
			choose(e) {
				this.vSelect = e;
				//this.$emit("choose", e);
			},
			cancel() {
				this.$refs.popupRef.close();
			},
			confirm() {
				this.$emit('confirm', this.vSelect)
				this.cancel();
			},
		}
	}
</script>


<style lang="scss" scoped>
	.pop {
		background: #fff;

		.btns {
			height: 54px;
			line-height: 54px;
			font-size: 16px;
			display: flex;
			justify-content: space-between;
			padding-top: 4upx;

			.pop-button {
				margin: 0px 15px 0px 15px;
			}
		}

		.pop-content {
			height: 100%;
			background-color: #fafafa;
			display: flex;
			height: 800upx;
			margin: 0 -20upx;
		}
	}
</style>
