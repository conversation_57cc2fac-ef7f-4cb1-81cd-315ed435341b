<template>
	<uni-popup ref="popupRef" type="bottom" :custom="true" :mask-click="false">
		<view class="pop">
			<view class="btns">
				<view class="pop-button cancel" @click="cancel()">取消</view>
				<view class="pop-button sure" @click="sure()">确定</view>
			</view>
			<view class="pop-content">
				<view class="content">
					<scroll-view scroll-y class="left-aside">
						<view v-for="item in flist" :key="item.id" class="f-item b-b" :class="{ active: item.id === currentId }" @click="tabtap(item)">
							{{ item.name }}
						</view>
					</scroll-view>
					<scroll-view scroll-with-animation scroll-y class="right-aside" :scroll-top="tabScrollTop">
						<view v-for="item in slist" :key="item.id" class="s-list" :id="'main-' + item.id">
							<view class="s-item">
								<m-icon class="icon" type="double-left-angle" color="#F1F1F1" size="8"></m-icon>
								{{ item.name }}
								<m-icon class="icon" type="double-right-angle" color="#F1F1F1" size="8"></m-icon>
							</view>
							<view class="t-list">
								<view :class="{ active: vSelect.indexOf(titem.id) != -1 }" @click="tapInfo(titem.id)" class="t-item" v-for="titem in item.child"
								 :key="titem.id">
									<text>{{ titem.name }}</text>
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script>
	import uniPopup from '@/components/uni-popup/uni-popup.vue';
	export default {
		components: {
			uniPopup
		},
		data() {
			return {
				show: true,
				vSelect: [],
				currentId: "",
				tabScrollTop: 100,
				flist: [{
						id: 1,
						name: '鱼类'
					},
					{
						id: 2,
						name: '虾类'
					},
					{
						id: 3,
						name: '贝类'
					},
					{
						id: 4,
						name: '其他'
					}
				],
				slist: [{
						id: 5,
						pid: 1,
						name: '海水类',
						child: [{
								id: 8,
								name: '美国红鱼'
							},
							{
								id: 9,
								name: '卵形鲳鲹'
							},
							{
								id: 10,
								name: '大黄鱼'
							},
							{
								id: 11,
								name: '鲆蝶类'
							},
							{
								id: 12,
								name: '石斑鱼'
							}
						]
					},
					{
						id: 6,
						pid: 1,
						name: '淡水类',
						child: [{
								id: 14,
								name: '鲌鱼'
							},
							{
								id: 15,
								name: '淡水白鲳'
							},
							{
								id: 16,
								name: '白斑狗鱼'
							},
							{
								id: 17,
								name: '鲮鱼'
							},
							{
								id: 18,
								name: '罗汉鱼'
							},
							{
								id: 19,
								name: '鲶'
							},
							{
								id: 20,
								name: '草鱼'
							},
							{
								id: 21,
								name: '团头鲂'
							},
							{
								id: 22,
								name: '罗非鱼'
							},
							{
								id: 23,
								name: '斑点叉尾鮰'
							},
							{
								id: 24,
								name: '虹鳟'
							},
							{
								id: 25,
								name: '加州鲈'
							},
							{
								id: 26,
								name: '黄颡鱼'
							},
							{
								id: 27,
								name: '鳗鲡'
							},
							{
								id: 28,
								name: '锦鲤'
							},
							{
								id: 29,
								name: '鲫鱼'
							},
							{
								id: 30,
								name: '鳜鱼'
							},
							{
								id: 31,
								name: '黄鳝'
							},
							{
								id: 32,
								name: '泥鳅'
							}
						]
					}
				],
				slistTmp: [{
						id: 5,
						pid: 1,
						name: '海水类',
						child: [{
								id: 8,
								name: '美国红鱼'
							},
							{
								id: 9,
								name: '卵形鲳鲹'
							},
							{
								id: 10,
								name: '大黄鱼'
							},
							{
								id: 11,
								name: '鲆蝶类'
							},
							{
								id: 12,
								name: '石斑鱼'
							}
						]
					},
					{
						id: 6,
						pid: 1,
						name: '淡水类',
						child: [{
								id: 14,
								name: '鲌鱼'
							},
							{
								id: 15,
								name: '淡水白鲳'
							},
							{
								id: 16,
								name: '白斑狗鱼'
							}
						]
					},
					{
						id: 52,
						pid: 2,
						name: '海水类',
						child: [{
								id: 82,
								name: '虾1'
							},
							{
								id: 92,
								name: '虾2'
							},
							{
								id: 102,
								name: '虾3'
							},
							{
								id: 112,
								name: '虾4'
							},
							{
								id: 122,
								name: '虾5'
							}
						]
					},
					{
						id: 62,
						pid: 2,
						name: '淡水类',
						child: [{
								id: 14,
								name: '虾6'
							},
							{
								id: 15,
								name: '虾7'
							},
							{
								id: 16,
								name: '虾8'
							}
						]
					},
					{
						id: 623,
						pid: 3,
						name: '海水类',
						child: [{
								id: 14,
								name: '贝1'
							},
							{
								id: 15,
								name: '贝2'
							}
						]
					},
					{
						id: 624,
						pid: 4,
						name: '海水类',
						child: [{
							id: 14,
							name: '其它'
						}]
					}
				]
			}
		},
		methods: {
			tabtap(item) {
				if (!this.sizeCalcState) {
					this.calcSize();
				}
				this.currentId = item.id;
				let list = [];
				this.slistTmp.map(sitem => {
					if (sitem.pid === item.id) {
						list.push(sitem);
					}
				});
				this.slist = list;
			},
			//计算右侧栏每个tab的高度等信息
			calcSize() {
				let h = 0;
				this.slist.forEach(item => {
					let view = uni.createSelectorQuery().select('#main-' + item.id);
					view.fields({
							size: true
						},
						data => {
							item.top = h;
							h += data.height;
							item.bottom = h;
						}
					).exec();
				});
				this.sizeCalcState = true;
			},
			tapInfo(e) {
				if (this.vSelect.indexOf(e) == -1) {
					this.vSelect.push(e);
				}
			},
			popShow(){
				console.log("show")
				this.$refs.popupRef.open();
			},
			cancel(){
				this.$refs.popupRef.close();
			},
			sure(){
				this.$emit('sure', this.vSelect)
				this.$refs.popupRef.close();
			}
		},
	}
</script>

<style lang="scss" scoped>
	.pop {
		background: #fff;

		.btns {
			display: flex;
			justify-content: space-between;
			padding-top: 4upx;

			.pop-button {
				padding: 3px 5px;
			}
		}

		.item-box {
			padding: 10px;

			.item {
				display: inline-block;
				border: 1px solid #1550b5;
				color: #1550b5;
				border-radius: 12px;
				padding: 2px 10px;
				margin-right: 10px;

				&.cur {
					color: #fff;
					border: 1px solid #1550b5;
					background-color: #1550b5;
				}
			}
		}

		.pop-content {
			height: 100%;
			background-color: #fafafa;
			display: flex;
			height: 800upx;
			margin: 0 -20upx;

			.content {
				display: flex;
			}

			.left-aside {
				flex-shrink: 0;
				width: 240upx;
				height: 100%;
				background-color: #f7f7f7;
			}

			.f-item {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 100upx;
				font-size: 28upx;
				color: #757575;
				position: relative;

				&.active {
					color: #1550b5;
					background: #f7f7f7;
					font-size: 36upx;
					font-weight: bold;

					&:before {
						content: '';
						position: absolute;
						left: 0;
						top: 50%;
						transform: translateY(-50%);
						height: 34upx;
						width: 8upx;
						background-color: #1550b5;
						border-radius: 0 4upx 4upx 0;
					}
				}
			}

			.right-aside {
				flex: 1;
				overflow: hidden;
				padding: 16upx 32upx 0 20upx;

				/deep/ .uni-scroll-view {
					height: 450px;
					overflow-y: auto;
				}
			}

			.s-item {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 70upx;
				padding-top: 8upx;
				font-size: 28upx;
				color: #757575;
				background: #fff;

				.icon {
					margin: 0 16upx;
					transform: scale(0.5);
				}
			}

			.t-list {
				display: flex;
				flex-wrap: wrap;
				background: #fff;
				padding: 12upx 6upx 0 6upx;
				margin-bottom: 24upx;

				&:after {
					content: '';
					flex: 99;
					height: 0;
				}

				.t-item {
					flex-shrink: 0;
					display: flex;
					justify-content: center;
					align-items: center;
					flex-direction: column;
					min-width: 200upx;
					font-size: 28upx;
					color: #666;
					margin: 0 10upx 40upx 10upx;

					&.active {
						color: #fff;
						background-color: #1550b5;
						border-radius: 24upx;
					}
				}
			}
		}
	}
</style>
