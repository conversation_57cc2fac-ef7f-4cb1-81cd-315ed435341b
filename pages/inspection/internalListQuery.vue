<template>
	<view class="uni-popup-share">
		<view class="uni-share-content">
			<view class="uni-share-content-box">
				<view class="whxx_all">
					<view class="whxx_left">选择上传时间范围</view>
				</view>
				<view class="whxx_all_hist" >
					<biaofun-datetime-picker class="all" ref="startDateRef" placeholder="起始时间" fields="day" @change="changeStartDate"></biaofun-datetime-picker>
				</view>
				<view class="whxx_all_hist" >
					<biaofun-datetime-picker class="all" ref="endDateRef" placeholder="结束时间" fields="day" @change="changeEndDate"></biaofun-datetime-picker>
				</view>
			</view>
		</view>
		<view class="uni-share-button-box">
			<button class="uni-share-button" type="default" size="default" style="color: #000;width: 100px;float:right;height: 40px;line-height: 40px;font-size:14px;" @click="close" >重置</button>
			<button class="uni-share-button" type="primary" size="default" style="color: #fff;width: 100px;float:right;height: 40px;line-height: 40px;font-size:14px;background-color: #02994f;" @click="select">筛选</button>
		</view>
	</view>
</template>

<script>
	
	import BiaofunDatetimePicker from "@/components/biaofun-datetime-picker/biaofun-datetime-picker";
	export default {
		inject: ['popup'],
		data() {
			return {
				form:{
					startDate:null,
					endDate:null,
				},
				date: new Date().toISOString().slice(0, 10),
			}
		},
		props: {
		},
		onLoad: function(option) {
			
		},
		computed: {
		},
		methods: {
			/* 时间选择 */
			changeStartDate(res) {
			    this.form.startDate = res.f1;
			},
			changeEndDate(res) {
			    this.form.endDate = res.f1;
			},
			/**
			 * 选择内容
			 */
			select() {
				this.$emit('select', this.form,
				/* , () => {
					this.popup.close()
				} */)
				//this.clear();
			},
			/**
			 * 关闭窗口
			 */
			close() {
				this.clear();
				//this.popup.close()
			},
			//清空选择项
			clear(){
				this.form={
					startDate:null,
					endDate:null,
				}
				this.$refs.startDateRef.clear();
				this.$refs.endDateRef.clear();
			}
		}
	}
</script>
<style lang="scss" scoped>
	.uni-popup-share {
		background-color: #fff;
	}
	.uni-share-title {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		align-items: center;
		justify-content: center;
		height: 40px;
	}
	.uni-share-title-text {
		font-size: 14px;
		color: #666;
	}
	.uni-share-content {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		justify-content: center;
		padding-top: 10px;
	}
	
	.uni-share-content-box {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		flex-wrap: wrap;
		width: 360px;
	}
	
	.uni-share-content-item {
		width: 90px;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		justify-content: center;
		padding: 10px 0;
		align-items: center;
	}
	
	.uni-share-content-item:active {
		background-color: #f5f5f5;
	}
	
	.uni-share-image {
		width: 30px;
		height: 30px;
	}
	
	.uni-share-text {
		margin-top: 10px;
		font-size: 14px;
		color: #3B4144;
	}
	
	.uni-share-button-box {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		padding: 10px 15px;
	}
	.select{
		background-color: rgb(1, 190, 110);
	}
	.all {
		width: 100%;
		float: left;
		padding:10px 0;
		font-size: 12px;
		color: #888888;
	}
</style>
