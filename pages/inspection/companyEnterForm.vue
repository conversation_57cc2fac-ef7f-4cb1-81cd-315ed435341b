<template>
	<view>
		<form @submit="formSubmit">
			<view v-if="!editModel" class="header_all">
				<view style="padding: 10px 0 10px;">{{inspectionLabel}}</view>
				<view style="font-size: 12px;">主体对产品各项指标进行全面的检验</view>
			</view>
			<div class="cont_all">
				<div class="cont">
					<div class="content_all_new">
						<div v-if="form.inspectionSituation=='is03'" class="whxx_all">
							<div class="whxx_left"> 
								检测单位:
							</div>
							<div class="whxx_right">
                {{form.inspectionCompany}}
							</div>
						</div>
						<div v-if="form.inspectionSituation=='is03'" class="whxx_all">
							<div class="whxx_left">
                <span v-if="!editModel">*</span>受检单位:
							</div>
							<div class="whxx_right" @click="goEntSearch">
                {{form.entName ? form.entName: "请选择受检单位"}}
							</div>
						</div>
            <div class="whxx_all">
              <div class="whxx_left">
                <span v-if="!editModel">*</span>样品名称:
              </div>
              <div class="whxx_right">
                <view v-if="editModel">
                  {{form.productName}}
                </view>
                <view v-else>
                  <picker  @change="changePickerProduct" :value="productIndex" :range="productList" :range-key="'name'">
                    <view class="uni-input">
                      <!-- {{productList[productIndex]?productList[productIndex].name:'请选择检测样品名称'}} -->
                      {{productLabel}}
                    </view>
                  </picker>
                </view>
              </div>
            </div>
            <div v-if="!editModel" style="width: 100%;text-align: center;">
              <span style="color: red;font-size: 12px;">*请注意{{inspectionLabel}}保存后，受检单位和样品名称将不可更改</span>
            </div>
						<div class="whxx_all">
							<div class="whxx_left"> 
								<span>*</span>检测项目:
							</div>
							<div class="whxx_right">
								<input class="uni-input" maxlength="50" v-model="form.inspectionItem" type="text" placeholder="请输入检测项目">
							</div>
						</div>
						<div class="whxx_all">
							<div class="whxx_left"> 
								<span>*</span>检测时间:
							</div>
							<div class="whxx_right">
								<biaofun-datetime-picker placeholder="请选择检测时间" fields="day" :end="endDateFun" :defaultValue="form.inspectionDate" @change="changePickerDatetime"></biaofun-datetime-picker>
							</div>
						</div>
						<div class="whxx_all">
							<div class="whxx_left"> 
								检测标准:
							</div>
							<div class="whxx_right">
								<input class="uni-input" maxlength="50" v-model="form.inspectionStandard" type="text" placeholder="请输入检测标准">
							</div>
						</div>
						<div class="whxx_all">
							<div class="whxx_left"> 
								合格范围:
							</div>
							<div class="whxx_right">
								<input class="uni-input" maxlength="50" v-model="form.acceptableRange" type="text" placeholder="请输入合格范围">
							</div>
						</div>
						<div class="whxx_all">
							<div class="whxx_left"> 
								检测数值:
							</div>
							<div class="whxx_right">
								<input class="uni-input" maxlength="50" v-model="form.inspectionValue" type="text" placeholder="请输入检测数值">
							</div>
						</div>
						<div class="whxx_all">
							<div class="whxx_left"> 
								<span>*</span>检测结果:
							</div>
							<div class="whxx_right">
								<picker @change="changePickerResult" :value="resultIndex" :range="resultList" :range-key="'name'">
									<view class="uni-input">
										<!-- {{resultList[resultIndex]?resultList[resultIndex].name:'请选择检测结果'}} -->
										{{inspectionResultLabel}}
									</view>
								</picker>
							</div>
						</div>
						<div class="whxx_all">
							<div class="whxx_left"> 
								<span>*</span>检测人员:
							</div>
							<div class="whxx_right">
								<input class="uni-input" maxlength="50" v-model="form.inspectionPerson" type="text" placeholder="请输入检测人员姓名">
							</div>
						</div>
						<div class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
							<span class="title_tit" style="padding: 0;"><span style="color: red;">*</span>检测报告(最多上传5张):</span>
						</div>
						<div class="whxx_all_hist" style="margin:10px auto;">
							<div class="pboto_right" style="margin-bottom:10px;width: 100%;">
								<view style="width:100%;">
									<view class="row-img">
										<ck-upload-img v-if="afterGetData" @removeImg="onRemoveImg" @returnImgUrl="onReturnImgUrl" :initImgList="imgList" :selectNum=5 :token="upToken" :tableName="tableName"></ck-upload-img>
										<ck-upload-img v-else @removeImg="onRemoveImg" @returnImgUrl="onReturnImgUrl" :selectNum=5 :token="upToken" :tableName="tableName"></ck-upload-img>
									</view>
								</view>
							</div>
						</div>
					</div>
				</div>
			</div>
			<button form-type="submit" type="primary" style="width:100px;height:35px;line-height:35px;font-size:14px;color: #fff;background-color: #02994f;">保存</button>
      <div style="height: 50px;width: 20px;"></div>
		</form>
	</view>
</template>

<script>
	import api from '@/api/productInspection.js';
	import productApi from '@/api/product.js';
	import commonApi from '@/api/common.js';
	import graceChecker from '@/common/graceui-dataChecker/graceChecker.js';
	import BiaofunDatetimePicker from "@/components/biaofun-datetime-picker/biaofun-datetime-picker";
	import ckUploadImg from '@/components/ck-uploadImg/ck-uploadImg.vue';
	export default {
		components: {BiaofunDatetimePicker,ckUploadImg},
		data: function() {
			return {
				editModel:false,//编辑模式
				form:{
					id:"",
					entId: "",
          entName: "",
          inspectionCompanyId: uni.getStorageSync("ent").id,
					productId:"",
					productName:"",
					inspectionSituation:"is03",
					inspectionItem:"",
					inspectionDate:new Date().toISOString().slice(0, 10),
					inspectionStandard:"",
					inspectionResult:"",
					inspectionPerson:"",
					inspectionCompany:uni.getStorageSync("ent").name,
					inspectionValue:"",
					acceptableRange:"",
					fileList:[], 
				},
				imgList:[],
				productList:[],
				productIndex:'',
				resultList:[{"name":"合格","value":"0"},
							{"name":"不合格","value":"1"}],
				resultIndex:'',
				upToken:'',//七牛token
				tableName:"bas_product_inspection",
				urlsEntOther:[],//企业其他照片路径集合
				afterGetData:false,
				
			}
		},
		onLoad: function(option) {
      /**
       * 判断逻辑
       * 1、判断当前ent缓存，如果不存在，提示需要先维护主体信息
       * 2、判断当前主体信息是否需要补入基础信息采集数据
       * 3、判断主体审核状态，审核中，跳转至主体查看页面
       * 4、判断主体审核状态，如果examineStatus == '90'，那么说明此主体是变更暂存，跳转至变更页面
       * 5、以上逻辑都通过后显示当前页面功能
       */
      let that = this;
      let ent = uni.getStorageSync("ent");
      if (!ent) {
        uni.showModal({
          title: '先维护主体信息',
          content: '确定前往维护主体信息?',
          mask: true,
          success: function(res) {
            if (res.confirm) {
              that.$Router.replaceAll("/pages/ent/chooseType")
            } else if (res.cancel) {
              that.$Router.replaceAll("/pages/index/index")
            }
          }
        });
      } else if (ent.basicFlag == '1' && ent.basicEnterFlag == '0') {
        uni.showModal({
          icon: "none",
          content: '未在基础数据采集系统内查询到您的信息,请前往填写详细信息',
          mask: true,
          showCancel: false,
          success: function(res) {
            that.$Router.replaceAll("/pages/basic/basicEntForm")
          }
        });
      } else if (ent.examineStatus == '0' || ent.examineStatus == '-1') {
        uni.showModal({
          icon: "none",
          content: '主体信息审核中，前往主体信息查看审核状态？',
          mask: true,
          success: function(res) {
            if (res.confirm) {
              that.$Router.replaceAll("/pages/ent/chooseType")
            } else if (res.cancel) {
              that.$Router.replaceAll("/pages/index/index")
            }
          }
        });
      } else if (ent.examineStatus == '90') { //变更暂存状态：跳转至变更页面
        uni.redirectTo({
          url: "/pages/entChange/viewCheck",
        })
        return;
      } else if (ent.frozenFlag == '1') {
        uni.showModal({
          icon: "none",
          content: '您的账号已冻结，请联系技术服务人员取消冻结',
          mask: true,
          showCancel: false,
          success: function(res) {
            that.$Router.replaceAll("/pages/index/index")
          }
        });
      } else {
        let that = this;
        commonApi.get7nToken().then(res => {
          if(res.code == 0){
            that.upToken = res.data;
          }
        });

        if(option.id){
          that.editModel=true;//编辑模式
          that.form.id=option.id;
          //表单赋值
          api.getEnter(that.form).then(res => {
            if (res.code === 1) {
              uni.hideLoading();
              uni.showToast({
                icon: 'none',
                title: res.message,
                mask: true,
                duration: 1500
              });
              return;
            }
            that.form=res.data;
            that.form.fileList.forEach((item) => {
              that.imgList.push(item.fileUrl);
            })
            that.afterGetData=true;
          })
        }
        uni.$on('setEnt', (ent)=>{
          if (ent) {
            this.form.entId = ent.id;
            this.form.entName = ent.name;
            this.productIndex = '';
            this.form.productName = "";
            this.form.productId = "";
            this.getProductList();
          }
        })
      }


		},
    onUnload(){
      uni.$off('setEnt');
    },
		computed: {
			//截止日期计算
			endDateFun(){
				var year=new Date().getFullYear();//年
				if (year< 1900) year = year + 1900;
				var month = new Date().getMonth() + 1;//月
				if (month < 10) month = '0' + month;
				var day = new Date().getDate();//日
				if (day < 10) day = '0' + day;
				var hour = new Date().getHours();//小时
				if (hour < 10) hour = '0' + hour;
				var minute = new Date().getMinutes();//分钟
				if (minute < 10) minute = '0' + minute;
				var second = new Date().getSeconds();//秒
				if (second < 10) second = '0' + second;
				var str=year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
				return str;
			},
			//产品标题
			productLabel() {
			  let label="请选择检测样品名称";
			  if(this.form.productId && this.productList.length>0){
				  let product=this.productList.filter(item => item.id === this.form.productId)[0];
				  label=product.name;
			  }
			  return label;
			},
			//检查结果标题
			inspectionResultLabel() {
			  let label="请选择检测结果";
			  if(this.form.inspectionResult){
				  let result=this.resultList.filter(item => item.value === this.form.inspectionResult)[0];
				  label=result.name;
			  }
			  return label;
			},
			//检查标题
			inspectionLabel() {
			  let label="请选择检测样品名称";
			  if(this.form.inspectionSituation=='is03'){
				 label="委托检测合格依据录入"
			  }else if(this.form.inspectionSituation=='is04'){
				 label="自行检测合格依据录入"
			  }
			  return label;
			},
		},
		methods: {
      goEntSearch() {
        if (this.editModel) {
          return;
        }
        let entName = this.form.entName
        uni.navigateTo({
          url: '/pages/inspection/entSearch',
          success() {
            setTimeout(() => {
              uni.$emit('entSearch', entName)
            }, 500)
          }
        });
      },
			//表单提交
			formSubmit: function(e) {
				let that = this;
				let rule = [{name: "productId",checkType: "notnull",errorMsg: "请选择检测样品名称"},
							{name: "inspectionItem",checkType: "notnull",errorMsg: "请输入检测项目"},
							{name: "inspectionResult",checkType: "notnull",errorMsg: "请选择检测结果"},
							{name: "inspectionPerson",checkType: "notnull",errorMsg: "请输入检测人员姓名"},
						];
				let validateData = {
					productId: this.form.productId,
					inspectionItem: this.form.inspectionItem,
					inspectionResult: this.form.inspectionResult,
					inspectionPerson: this.form.inspectionPerson,
				}
				if(that.form.inspectionSituation=='is03'){
					let inspectionCompanyRule = {name: "inspectionCompany",checkType: "notnull",errorMsg: "请输入检测单位"};
					rule.push(inspectionCompanyRule);
					that.$set(validateData, "inspectionCompany", that.form.inspectionCompany);
				}
				let checkRes = graceChecker.check(validateData, rule);
				if (!checkRes) {
					uni.showToast({
						title: graceChecker.error,
						icon: "none",
						duration: 1500,
						success() {
							setTimeout(() => {
							}, 1500);
						}
					});
					return;
				}
				
				if(this.form.fileList.length===0){
					uni.showToast({
						title: '请上传检测报告',
						icon: "none",
						duration: 1500,
						success() {
							setTimeout(() => {
							}, 1500);
						}
					});
					return;
				}
				uni.showLoading({
					title: '加载中',
					mask: true,
				});
				api.saveEnter(that.form).then(res => {
					if (res.code === 1) {
						uni.hideLoading();
						uni.showToast({
							icon: 'none',
							title: res.message,
							mask: true,
							duration: 1500
						});
						return;
					}
					uni.hideLoading();
					uni.showToast({
						//icon:'none',
						title: '信息提交成功',
						duration: 2000,
						success() {
							setTimeout(() => {
								uni.navigateBack({
									delta:1
								})
							}, 2000);
						}
					});
				})
			},
			//获取产品集合
			getProductList() {
				let that=this;
        uni.showLoading({
          title: '加载中...',
          mask: true,
        });
        productApi.findListByEntId({entId: this.form.entId}).then(res => {
          uni.hideLoading();
          if (res.code === 1) {
            uni.showToast({
              icon: 'none',
              title: res.message,
              mask: true,
              duration: 1500
            });
            return;
          }
          that.productList = res.data;
        })
			},
			//更换产品
			changePickerProduct(e) {
			    this.productIndex = e.target.value;
			    var selected = this.productList[this.productIndex];    //获取选中的数组
			    this.form.productId = selected.id;
				this.form.productName = selected.name;
			},
			//更换日期
			changePickerDatetime(res) {
			    this.form.inspectionDate = res.f1;
			},
			//更换检查结果
			changePickerResult(e) {
			    this.resultIndex = e.target.value;
			    var selected = this.resultList[this.resultIndex];    //获取选中的数组
			    this.form.inspectionResult = selected.value;
			},
			//删除照片
			onRemoveImg(index){
				this.form.fileList.splice(index,1);
			},
			//返回照片
			onReturnImgUrl(urls){
				this.form.fileList = [];
				urls.forEach((url) => {
					let item = {};
					item["fileType"] = 'photo';
					item["tableName"] = this.tableName;
					item["filePath"] = url;
					this.form.fileList.push(item);
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.header_all {
		width: 100%;
		height: 70px;
		background: #02994F;
		text-align: center;
		color: #fff;
	}
	.cont_all {
		width: 100%;
		background: #02994F;
	}
	.content_all_new  {
		    width: 90%;
		    margin: 0px auto 10px;
		    box-shadow: 0;
		    background: #fff;
		    overflow: auto;
	}
	.cont {
		width: 100%;
		border-radius: 20px 20px 0 0 ;
		background: #fff;
	}
</style>
