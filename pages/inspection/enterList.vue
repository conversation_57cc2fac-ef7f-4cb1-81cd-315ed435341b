<template>
	<view>
		<view style="overflow: auto;">
			<view style="float:left;width:80%;">
				<uni-search-bar placeholder="输入样品名称" @confirm="onConfirm" @cancel="onCancel"></uni-search-bar>
			</view>
			<view style="float:left;width:20%;">
				<button type="primary" style="color: #fff; background-color: #02994f;width: 60px;float:right;height: 30px;line-height: 30px;font-size:14px;margin-top: 10px;margin-right: 10px;"
				 @click="queryPopupShow()">筛选</button>
			</view>
		</view>
		
		
		<view class="cpxx_title" >
			<view class="number_all" @click="inspectionDateSort()">
				<view class="cpxx_left" style="margin: 0 6%;" >检测时间</view>
				<view class="cpxx_right" style=" display:inline-block;margin-top:10px;">
					<view class="jt_all">
						<view v-if="inspectionDateAsc" class="triangle-up"></view>
					</view>
					<view class="jt_all">
						<view v-if="inspectionDateDesc" class="triangle-down"></view>
						<view v-if="inspectionDateDescOrder"  style="border-top: 7px solid #fff;" class="triangle-down"></view>
					</view>
				</view>
			</view>
			<view class="number_all" @click="createDateSort()">
				<view class="cpxx_left" style="margin: 0 2%;" >上传时间</view>
				<view class="cpxx_right" style=" display:inline-block;margin-top:10px;">
					<view class="jt_all">
						<view v-if="createDateAsc" class="triangle-up"></view>
					</view>
					<view class="jt_all">
						<view v-if="createDateDesc" class="triangle-down"></view>
						<view v-if="createDateDescOrder"  style="border-top: 7px solid #fff;" class="triangle-down"></view>
					</view>
				</view>
			</view>
			<view class="number_all"  @click="inspectionResultSort()">
				<view class="cpxx_left" style="margin: 0 6%;">检测结果</view>
				<view class="cpxx_right" style=" display:inline-block;margin-top:10px;">
					<view class="jt_all">
						<view v-if="inspectionResultAsc" class="triangle-up"></view>
					</view>
					<view class="jt_all">
						<view v-if="inspectionResultDesc" class="triangle-down"></view>
						<div v-if="inspectionResultDescOrder" class="triangle-down" style="border-top: 7px solid #fff;"></div>
					</view>
				</view>
			</view>
		</view>
		
		<!-- <view class="fixBt" @click="onAdd()">添加</view> -->
		<view class="message_all" :style="{'height':messageHeight}">
			<view class="news-item" v-for="(item,index) in list" :key="index">
				<view class="news-intr" @click="onView(item.id)" style="width: 90%;float:left;">
					<view class="flex-table" style="overflow: auto;">
						<p style="float:left;width:60%;">
							{{item.productName}}
						</p>
						<p style="float:left;width:40%;text-align: right;" :style="{'color':item.inspectionResult=='0'?'#02994f':'red'}">{{result(item.inspectionResult)}}</p>
					</view>
					<view class="news-info" style="width: 100%;margin-top:10px">
						<p style="float:left;width:45%;">
							检测时间:{{item.inspectionDate}}
						</p>
						<p style="float:left;width:55%;text-align: right;">上传时间:{{item.createDate | formatDate}}</p>
					</view>
				</view>
				<uni-icons type="arrowright" size="17" style="float:left;color:grey;width:20px;height:30px;line-height:30px;text-align: right;" @click="onView(item.id)"></uni-icons>
			</view>
			<uni-load-more :status="status" :content-text="contentText" />
		</view>
		<view style="width: 100%;bottom:0px;background: #fff;height: 65px;">
			<div style="height: 30px;margin: 10px auto;">
				<view style="float:right;font-size: 13px;">
					<button type="primary" style="color: #fff; background-color: #02994f;width: 60px;float:right;height: 60px;line-height: 120upx;font-size:14px;margin-right: 10px;border-radius: 50%;"
					 @click="onAdd()">添加</button>
				</view>
			</div>
		</view>
		<uni-popup ref="queryPopup" type="share">
			<enter-list-query @select="onSelect"></enter-list-query>
		</uni-popup>
	</view>
</template>

<script>
	import api from '@/api/productInspection.js';
	import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';
	import enterListQuery from '@/pages/inspection/enterListQuery.vue'
	import {
		formatDate
	} from '@/common/formatDate.js';
	export default {
		components: {
			uniLoadMore,
			enterListQuery,
		},
		data: function() {
			return {
				messageHeight:"",
				form: {
					entId: uni.getStorageSync("ent").id,
					productName: "",
					inspectionSituation: "",
					startDate: null,
					endDate: null,
					inspectionResult: "",
					orderBy:"",
				},
				
				inspectionDateAsc:true,
				inspectionDateDesc:true,
				inspectionDateDescOrder:false,
				createDateAsc:true,
				createDateDesc:true,
				createDateDescOrder:false,
				inspectionResultAsc:true,
				inspectionResultDesc:true,
				inspectionResultDescOrder:false,
				list: [],
				total: 0, //总数
				refreshing: false, //为true表示正在刷新
				fetchPageNum: 1, //当前页数
				status: 'noMore',
				contentText: { // 加载提示
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
			}

		},
		onLoad: function(option) {
			let that=this;
			that.form.inspectionSituation = option.inspectionSituation;
			uni.getSystemInfo({
				success: function(res) {
					that.messageHeight = (res.screenHeight*0.67)+"px";
					console.log("messageHeight:"+that.messageHeight);
				}
			});
			/* uni.setNavigationBarTitle({
				title: '维护产品信息'
			}); */
		},
		onShow: function() {
			this.onSearch();
		},
		onReachBottom() {
			if (this.list.length < this.total) {
				this.status = 'loading';
				this.getData();
			} else {
				this.status = 'noMore'; //没有数据时显示‘没有更多’
			}

		},
		onPullDownRefresh() {
			console.log('下拉刷新');
			this.refreshing = true;
			this.getData();
		},
		computed: {
			result() {
				return function(e) {
					return e == "0" ? "合格" : "不合格";
				}
			},
		},
		methods: {
			onSearch() {
				this.list = [];
				this.fetchPageNum = 1;
				this.refreshing = true;
				this.getData();
			},
			getData: function() {
				let that = this;
				let pageNo = that.refreshing ? 1 : that.fetchPageNum;
				api.findList(pageNo, that.form).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: "请求数据失败请重试"
						})
						return;
					}
					that.total = res.data.count;
					if (that.refreshing) {
						that.refreshing = false;
						uni.stopPullDownRefresh();
						that.list = res.data.list;
						that.fetchPageNum = res.data.next;
					} else {
						that.list = that.list.concat(res.data.list);
						that.fetchPageNum = res.data.next;
					}
				})
			},
			onAdd: function() {
				uni.navigateTo({
					url: '/pages/inspection/enterForm?inspectionSituation=' + this.form.inspectionSituation
				})
			},
			onView: function(id) {
				uni.navigateTo({
					url: "/pages/inspection/enterView?id=" + id,
				})
			},
			onConfirm(e) {
				this.form.productName = e.value;
				this.sortClear();
				this.onSearch()
			},
			onCancel(e) {
				this.form.productName = "";
				this.sortClear();
				this.onSearch()
			},
			queryPopupShow() {
				this.$refs.queryPopup.open();
			},
			onSelect(queryForm) {
				let that = this;
				that.form.startDate = queryForm.startDate;
				that.form.endDate = queryForm.endDate;
				that.form.inspectionResult = queryForm.inspectionResult;
				this.sortClear();
				that.onSearch();
				this.$refs.queryPopup.close()
			},
			//检测时间排序
			inspectionDateSort(){
				this.createDateAsc=true;
				this.createDateDesc=true;
				this.createDateDescOrder=false;
				
				this.inspectionResultAsc=true;
				this.inspectionResultDesc=true;
				this.inspectionResultDescOrder=false;
				
				this.inspectionDateDesc=false;
				if(this.inspectionDateAsc){
					this.inspectionDateAsc=false;
					this.inspectionDateDescOrder=true;
					this.form.orderBy="1";
				}else{
					this.inspectionDateAsc=true;
					this.inspectionDateDescOrder=false;
					this.form.orderBy="2";
				}
				this.onSearch();
			},
			//上传时间排序
			createDateSort(){
				this.inspectionDateAsc=true;
				this.inspectionDateDesc=true;
				this.inspectionDateDescOrder=false;
				
				this.inspectionResultAsc=true;
				this.inspectionResultDesc=true;
				this.inspectionResultDescOrder=false;
				
				this.createDateDesc=false;
				if(this.createDateAsc){
					this.createDateAsc=false;
					this.createDateDescOrder=true;
					this.form.orderBy="3";
				}else{
					this.createDateAsc=true;
					this.createDateDescOrder=false;
					this.form.orderBy="4";
				}
				this.onSearch();
			},
			//检测结果排序
			inspectionResultSort(){
				this.inspectionDateAsc=true;
				this.inspectionDateDesc=true;
				this.inspectionDateDescOrder=false;
				
				this.createDateAsc=true;
				this.createDateDesc=true;
				this.createDateDescOrder=false;
				
				this.inspectionResultDesc=false;
				if(this.inspectionResultAsc){
					this.inspectionResultAsc=false;
					this.inspectionResultDescOrder=true;
					this.form.orderBy="5";
				}else{
					this.inspectionResultAsc=true;
					this.inspectionResultDescOrder=false;
					this.form.orderBy="6";
				}
				this.onSearch();
			},
			sortClear(){
				this.inspectionDateAsc=true;
				this.inspectionDateDesc=true;
				this.inspectionDateDescOrder=false;
				
				this.inspectionResultAsc=true;
				this.inspectionResultDesc=true;
				this.inspectionResultDescOrder=false;
				
				this.createDateAsc=true;
				this.createDateDesc=true;
				this.createDateDescOrder=false;
				
				this.form.orderBy="";
			}
		},
		filters: {
			formatDate(time) {
				let date = new Date(time)
				return formatDate(date, 'yyyy-MM-dd hh:mm') //年-月-日 时分
			}
		}
	}
</script>

<style lang="scss" scoped>
	.fixBt {
		position: fixed;
		width: 120upx;
		height: 120upx;
		text-align: center;
		line-height: 120upx;
		border-radius: 50%;
		background-color: #02994f;
		color: #fff;
		right: 5upx;
		bottom: 40upx;
	}

	.safe-body {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		width: 100%;
	}

	.index {
		flex: 1;
		width: 750rpx;
		flex-direction: column;
		/* 相当于上下居中 */
		justify-content: center;
		/* 相当于水平居中 */
		align-items: center;
	}

	.row {
		flex-direction: row;
	}

	.column {
		flex-direction: column;
	}

	.load-bottom {
		width: 100%;
		text-align: center;
	}

	.loadMore {
		font-size: 30upx;
		color: #555;
		margin-bottom: 20upx;
		line-height: 60rpx;
		margin-left: 170px;
	}

	.emptyData {
		margin-left: 175px;
	}

	.news-item {
		height: 40px;
		margin-left: 30rpx;
		margin-right: 30rpx;
		display: flex;
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		padding-top: 15px;
		padding-bottom: 15px;
		border-bottom: 2rpx #efefef solid;
	}

	.news-item .news-intr {
		flex: 2;
		padding-right: 20rpx;
		/* display: flex; */
		flex-direction: column;
		justify-content: space-between;
	}

	.news-intr .news-title {}

	.news-intr .news-info {
		display: flex;
		font-size: 12px;
		color: #999999;
	}

	.news-info .news-ago {
		text-align: left;
	}

	.news-info .news-type {
		text-align: right;
		flex: 2;
	}

	.news-item .news-image {
		flex: 1;
		max-width: 240rpx;
		max-height: 160rpx;
	}

	.news-item .news-image image {
		max-width: 100%;
		max-height: 100%;
	}

	.left {
		margin-left: 130rpx;
	}
	.triangle-up {
		margin-top: 3px;
		/*display:inline-block;*/
		width: 0;
		height: 0;
		border-left: 5px solid transparent;
		border-right: 5px solid transparent;
		border-bottom: 7px solid #fff;
		/* background: #fff; */
	}
	
	.triangle-down {
		margin-top: 3px;
		/*display:inline-block;*/
		width: 0;
		height: 0;
		border-left: 5px solid transparent;
		border-right: 5px solid transparent;
		border-top: 7px solid #9dd6b7;
		/* background: #9dd6b7; */
	}
	.number_all {
		width: 33%;
	}
	.message_all {
		//height: 300px;
		overflow: auto;
	}
</style>
