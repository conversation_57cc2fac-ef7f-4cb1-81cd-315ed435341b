<template>
	<view>
		<div class="content_all">
			<div class="whxx_all">
				<div class="whxx_left"> 
					样品名称:
				</div>
				<div class="whxx_right">
					{{form.productName}}
				</div>
			</div>
			<div v-if="form.inspectionSituation=='is03'" class="whxx_all">
				<div class="whxx_left"> 
					检测单位:
				</div>
				<div class="whxx_right">
					{{form.inspectionCompany}}
				</div>
			</div>
			<div v-if="form.inspectionSituation=='is03'" class="whxx_all">
				<div class="whxx_left"> 
					受检单位:
				</div>
				<div class="whxx_right">
					{{entName}}
				</div>
			</div>
			<div class="whxx_all">
				<div class="whxx_left"> 
					检测项目:
				</div>
				<div class="whxx_right">
					{{form.inspectionItem}}
				</div>
			</div>
			<div class="whxx_all">
				<div class="whxx_left"> 
					检测时间:
				</div>
				<div class="whxx_right">
					{{form.inspectionDate}}
				</div>
			</div>
			<div class="whxx_all">
				<div class="whxx_left"> 
					检测标准:
				</div>
				<div class="whxx_right">
					{{form.inspectionStandard}}
				</div>
			</div>
			<div class="whxx_all">
				<div class="whxx_left"> 
					合格范围:
				</div>
				<div class="whxx_right">
					{{form.acceptableRange}}
				</div>
			</div>
			<div class="whxx_all">
				<div class="whxx_left"> 
					检测数值:
				</div>
				<div class="whxx_right">
					{{form.inspectionValue}}
				</div>
			</div>
			<div class="whxx_all">
				<div class="whxx_left"> 
					检测结果:
				</div>
				<div class="whxx_right">
					{{result((form.inspectionResult))}}
				</div>
			</div>
			<div class="whxx_all">
				<div class="whxx_left"> 
					检测人员:
				</div>
				<div class="whxx_right">
					{{form.inspectionPerson}}
				</div>
			</div>
			<div v-if="form.inspectionSituation!='is02'" class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
				<span class="title_tit" style="padding: 0;">检测报告:</span>
			</div>
			<view v-if="form.inspectionSituation!='is02'" class="whxx_all_hist" style="margin:10px auto;">
				<view class="pboto_right" style="margin-bottom:10px;width: 90%;margin-left: 5%;">
					<view style="width:100%;" >
						<swiperImg :swiperList="photoList"
								 :indicatorDots="true" 
								 :autoplay="true" 
								 :interval="2000" 
								 :duration="500" 
								 :mode="imageMode"
								 />
					</view>
				</view>
			</view>
		</div>
		<view style="margin-bottom: 50px;" v-if="editFlag && !form.inspectionCompanyId">
			<button @click="gotoEdit" type="primary" style="width:100px;height:35px;line-height:35px;font-size:14px;color: #fff;background-color: #02994f;">修改信息</button>
		</view>
	</view>
</template>

<script>
	import api from '@/api/productInspection.js';
	import swiperImg from '@/components/zhtx-swiper/swiper.vue'
	export default {
		components: {swiperImg},
		data: function() {
			return {
				entName:uni.getStorageSync("ent").name,
				form:{
					entId:"",
					productId:"",
					productName:"",
					inspectionSituation:"",
					inspectionItem:"",
					inspectionDate:"",
					inspectionStandard:"",
					inspectionResult:"",
					inspectionPerson:"",
					fileList:[], 
					acceptableRange:"",
					inspectionValue:"",
				},
				photoList:[],
				editFlag:true,
				imageMode:'aspectFit',
			}
		},
		onLoad: function(option) {
			let that = this;
			that.form.id=option.id;
			
			if(typeof option.editFlag !== 'undefined' && option.editFlag=='false'){
				that.editFlag=false;
			}
			/* uni.setNavigationBarTitle({
				title: '产品添加'
			}); */
		},
    onShow() {
      let that = this;
      api.getEnter(that.form).then(res => {
        if (res.code === 1) {
          uni.hideLoading();
          uni.showToast({
            icon: 'none',
            title: res.message,
            mask: true,
            duration: 1500
          });
          return;
        }
        that.form=res.data;
        if(that.form.inspectionSituation!="is02"){
          that.form.fileList.forEach((item) => {
            let file = {};
            file.img = item.fileUrl;
            that.photoList.push(file);
          })
        }
      })
    },
		computed: {
			result() {
			  return function(e){
				  if(e==''){
					  return ''
				  }else{
					return e=="0"?"合格":"不合格";  
				  }
				  
			  }
			},
		},
		methods: {
			gotoEdit(){
				uni.navigateTo({
					url: "/pages/inspection/enterForm?id="+this.form.id,
				})
			},
		}
	}
</script>

<style>
</style>
