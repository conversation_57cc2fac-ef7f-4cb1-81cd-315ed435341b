<template>
	<view class="index">
		<view class="safe-body" style="width:100%;height: 35px;background: #fff;position: fixed;z-index:999;top:0px;">
			<view class="cpxx_title" v-if="token" style="height: 30px;line-height: 30px;width: 96%;margin: 5px auto 0;">
				<view class="jcxx_center item_left">
					<view style="margin: 0 6%;">样品名称</view>
				</view>
				<view class="jcxx_center item_center">
					<view style="margin: 0 2%;" >样品编号</view>
				</view>
				<view class="jcxx_center item_right">
					<view>操作</view>
				</view>
			</view>
		</view>
		<view style="margin: 35px auto 65px;">
			<evan-checkbox-group max=10 class="block" @change="checkboxChange" ref="cGroup"  >
				<view class="jcxx_item" v-for="(item,index) in list" :key="index" style="position: relative">
					<evan-checkbox shape="square" primaryColor="#02994f" :key="item.id" :label="item" style="position: absolute;top:15px;"></evan-checkbox>
					<view>
						<view class="jcxx_center item_left jcxx_item_detail">{{item.name}}</view>
						<view class="jcxx_center item_center jcxx_item_detail" v-if="item.currentSampleNo">
							<img :src= item.queryCodeUrl style="width: 20px; height: 20px;float: left;margin-top: 15px;margin-left: 5%;" @click="clickQueryCode(item.queryCodeUrl)">
							<view style="float: left;">{{item.currentSampleNo}}</view>
						</view>
						<view class="jcxx_center item_center jcxx_item_detail" v-else>
							----
						</view>
						<view class="jcxx_center item_right jcxx_item_detail" style="color: #02994f;" @click="view(item.id)"  v-if="item.currentSampleNo">查看</view>
						<view class="jcxx_center item_right jcxx_item_detail" style="color: #6e6e6e;" v-else>查看</view>
					</view>
                </view>
			</evan-checkbox-group>
			<view style="position: fixed;width: 100%;bottom:0px;background: #fff;height: 65px;">
				<div style="height: 30px;margin: 10px auto;">
					<view style="float:left;margin-left: 30px;font-size: 13px;height: 30px;line-height: 30px;">
						选择上限为10
					</view>
					<view style="float:right;font-size: 13px;">
						<button type="primary" style="color: #fff; background-color: #02994f;width: 90px;float:right;height: 30px;line-height: 30px;font-size:14px;margin-right: 10px;"
						 @click="applyInspection()">申请检测</button>
						<button v-if="applyList.length>0" style="color: #000;width: 90px;float:right;height: 30px;line-height: 30px;font-size:14px;margin-right: 5px;"
								@click="cancelCheckbox()">取消</button>
					</view>
				</div>
			</view>
		</view>
	</view>
</template>
 
<script>
	import api from '../../api/inspection.js';

	export default {
		components: {
		},
		data: function() {
			return {
				token:uni.getStorageSync("token"),
				list: [],
				applyList:[],
			}

		},
		created() {
			if(!this.token){
				return ;
			}
			let that = this;
			//验证用户是否维护了主体信息
			let ent = uni.getStorageSync("ent");
			if (!ent) {
				uni.showModal({
					title: '先维护主体信息',
					content: '确定前往维护主体信息?',
					mask: true,
					success: function (res) {
						if (res.confirm) {
							that.$Router.replaceAll("/pages/ent/chooseType")
						} else if (res.cancel) {
							that.$Router.replaceAll("/pages/index/index")
						}
					}
				});
			}else if (ent.basicFlag == '1' && ent.basicEnterFlag == '0'){
				uni.showModal({
					icon: "none",
				    content: '未在基础数据采集系统内查询到您的信息,请前往填写详细信息',
					mask: true,
					showCancel:false,
				    success: function (res) {
						that.$Router.replaceAll("/pages/basic/basicEntForm")
				    }
				});
			}else if (ent.frozenFlag == '1'){
				uni.showModal({
					icon: "none",
				    content: '您的账号已冻结，请联系技术服务人员取消冻结',
					mask: true,
					showCancel:false,
				    success: function (res) {
						that.$Router.replaceAll("/pages/index/index")
				    }
				});
			}else {
				this.getData();
			}
		},
		onLoad: function(option) {
			/* uni.setNavigationBarTitle({
				title: '农药兽药检测'
			}); */
		},
		onShow: function() {
			if(!this.token){
				return ;
			}
		},
		methods: {
			//获取数据集
			getData: function() {
				let that = this;
				that.applyList = [];
				that.list = [];
				api.findInspectionList().then(res => {
					if (res.code != 0) {
						uni.showToast({
							title: "请求数据失败请重试"
						})
						return;
					} else {
						that.list = res.data;
					}
				})
			},
			//申请检测
			applyInspection:function(){
				if(!this.applyList.length >0){
					uni.showToast({
						icon: 'none',
						title: "请选择检测样品"
					})
					return;
				}
				uni.showLoading({
				    title: '申请中',
					mask:true,
				});
				api.applyInspection(this.applyList).then(res => {
					if (res.code == 1) {
						uni.hideLoading();
						uni.showToast({
							icon: 'none',
							title: "申请检测失败，请稍后再试"
						})
						return;
					} 
					this.$refs.cGroup.clearAll();
					uni.hideLoading();
					this.getData();
					
				})
			},
			// 查看
			view:function(id){
				uni.navigateTo({
					url: "./view?id="+id,
				})
			},
			//点击二维码查看大图
			clickQueryCode:function(url){
				let urls = [];
				urls[0] = url;
				uni.previewImage({
					urls: urls
				});
			},
			//复选框改变事件
			checkboxChange: function (e) {
				console.log("change")
				this.applyList = [];
				this.applyList = e;
			},
			//取消
			cancelCheckbox:function () {
				this.$refs.cGroup.clearAll();
				this.getData();
			},
		},
	}
</script>

<style scoped lang="scss">
	.index {
		flex: 1;
		width: 750rpx;
		flex-direction: column;
		/* 相当于上下居中 */
		justify-content: center;
		/* 相当于水平居中 */
		align-items: center;
	}
	.safe-body {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		width: 100%;
	}
	.jcxx_center{
		float: left;
		text-align: center;
	}
	.item_left{
		width: 24%;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		padding-left: 6%;
	}
	.item_center{
		width: 50%;
	}
	.item_right{
		width: 20%;
	}
	.jcxx_item{
		width: 96%;
		margin: 0 auto;
		font-size: 13px;
		height: 50px;
		color: #333333;
		border-bottom: 2rpx #efefef solid;
		.jcxx_item_detail{
			line-height: 50px;
		}
	}
	.jcxx_sqjc{
		background-color: #02994f;
		color: #fff;
	}
	/deep/uni-checkbox .uni-checkbox-input {
		width: 15px;
		height: 15px;
	}
</style>
