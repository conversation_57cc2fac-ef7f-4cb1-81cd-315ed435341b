<template>
	<view class="index">
		<view class="content-item" v-for="(item,index) in list" :key="index">
			<view class="item-middle">
				<view class="big-title">
					<view style="float: left;width:85%;">检测编号 {{item.recordNo}}</view>
					<view style="float: right;width:15%;color: #02994f;" v-if="item.testResult == 0 ">合格</view>
					<view style="float: right;width:15%;color: red;" v-else>不合格</view>
				</view>
				<view class="big-title">检测项目 {{item.testItemLabel}}</view>
				<view class="big-title">检测时间 {{item.testDate | dateFilter}}</view>
				<view class="big-title">检测人员 {{item.testMan}}</view>
				<view class="big-title">检测数值 {{item.testNumericalUnit}}</view>
				<view class="big-title">限量标准 {{item.limitStandard}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	import api from '../../api/inspection.js';

	export default {
		components: {},
		data: function() {
			return {
				token: uni.getStorageSync("token"),
				list: [],
				sampleNo: "",
			}

		},
		onLoad: function(option) {
			console.log("onLoad")
			uni.setNavigationBarTitle({
				title: '农药兽药检测'
			});
			this.sampleNo = option.sampleNo;
			this.getData();
		},
		onShow: function() {
			console.log("onShow")
			if (!this.token) {
				return;
			}
		},
		methods: {
			getData: function() {
				let that = this;
				that.list = [];
				// this.sampleNo = 'hgz2020091700001'
				api.getInspectionDetail(this.sampleNo).then(res => {
					if (res.code == 1) {
						uni.showToast({
							icon: 'none',
							title: "请求数据失败请重试"
						})
						return;
					} 
					if (!res.data) {
						uni.showToast({
							icon: 'none',
							title: "样品检测中，请稍后查看检测结果。",
							mask: true,
							duration: 3000
						})
						setTimeout(() => {
							uni.navigateBack({
								
							})
						}, 3000);
					}
					that.list = res.data;
				})
			}
		},
		filters: {
			dateFilter: function(time) {
				time = time.replace("-", "/").replace("-", "/");
				var date = new Date(time),
					year = date.getFullYear(),
					month = date.getMonth() + 1,
					day = date.getDate();
				return year + '-' + month + '-' + day;
			},
		}
	}
</script>

<style scoped lang="scss">
	.index {
		flex: 1;
		width: 750rpx;
		flex-direction: column;
		/* 相当于上下居中 */
		justify-content: center;
		/* 相当于水平居中 */
		align-items: center;
	}

	.content-item {
		width: 90%;
		margin-left: 5%;
		display: inline-block;
		border-bottom: 1px solid #e0e0e0;
		padding-bottom: 10px;
		padding-top: 10px;
		.item-middle {
			width: 100%;
			.big-title {
				font-size: 14px;
			}
		}
	}
</style>
