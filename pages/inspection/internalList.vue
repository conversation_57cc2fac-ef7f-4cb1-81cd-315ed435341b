<template>
	<view>
		<view style="overflow: auto;">
			<view style="float:left;width:80%;">
				<uni-search-bar placeholder="输入样品名称" @confirm="onConfirm" @cancel="onCancel"></uni-search-bar>
			</view>
			<view style="float:left;width:20%;">
				<button type="primary" style="color: #fff; background-color: #02994f;width: 60px;float:right;height: 30px;line-height: 30px;font-size:14px;margin-top: 10px;margin-right: 10px;"
				 @click="queryPopupShow()">筛选</button>
			</view>
		</view>
		<view class="cpxx_title" >
			<view class="number_all">
				<view style="text-align: center;" >样品名称</view>
			</view>
			<view class="number_all">
				<view style="text-align: center;" >控制名称</view>
			</view>
			<view class="number_all">
				<view style="text-align: center;">上传时间</view>
			</view>
		</view>
		<view class="message_all" :style="{'height':messageHeight}">
			<view class="news-item" v-for="(item,index) in list" :key="index">
				<view @click="onView(item.id)" style="width: 100%;">
					<view style="text-align: center;width:33%;float:left;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">{{item.productName}}</view>
					<view style="text-align: center;width:34%;float:left;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">{{item.inspectionItem}}</view>
					<view style="text-align: center;width:33%;float:left;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">{{item.inspectionDate | formatDate}}</view>
				</view>
			</view>
			<uni-load-more :status="status" :content-text="contentText" />
		</view>
		<view style="width: 100%;bottom:0px;background: #fff;height: 65px;">
			<div style="height: 30px;margin: 10px auto;">
				<view style="float:right;font-size: 13px;">
					<button type="primary" style="color: #fff; background-color: #02994f;width: 60px;float:right;height: 60px;line-height: 120upx;font-size:14px;margin-right: 10px;border-radius: 50%;"
					 @click="onAdd()">添加</button>
				</view>
			</div>
		</view>
		<uni-popup ref="queryPopup" type="share">
			<internal-list-query @select="onSelect"></internal-list-query>
		</uni-popup>
	</view>
</template>

<script>
	import api from '@/api/productInspection.js';
	import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';
	import internalListQuery from '@/pages/inspection/internalListQuery.vue'
	import {
		formatDate
	} from '@/common/formatDate.js';
	export default {
		components: {
			uniLoadMore,
			internalListQuery,
		},
		data: function() {
			return {
				messageHeight:"",
				form: {
					entId: uni.getStorageSync("ent").id,
					productName: "",
					inspectionSituation: "",
					startDate: null,
					endDate: null,
					inspectionResult: "",
					orderBy:"",
				},
				list: [],
				total: 0, //总数
				refreshing: false, //为true表示正在刷新
				fetchPageNum: 1, //当前页数
				status: 'noMore',
				contentText: { // 加载提示
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
			}

		},
		onLoad: function(option) {
			let that=this;
			that.form.inspectionSituation = option.inspectionSituation;
			uni.getSystemInfo({
				success: function(res) {
					that.messageHeight = (res.screenHeight*0.63)+"px";
					console.log("messageHeight:"+that.messageHeight);
				}
			});
			/* uni.setNavigationBarTitle({
				title: '维护产品信息'
			}); */
		},
		onShow: function() {
			this.onSearch();
		},
		onReachBottom() {
			if (this.list.length < this.total) {
				this.status = 'loading';
				this.getData();
			} else {
				this.status = 'noMore'; //没有数据时显示‘没有更多’
			}

		},
		onPullDownRefresh() {
			console.log('下拉刷新');
			this.refreshing = true;
			this.getData();
		},
		computed: {
			result() {
				return function(e) {
					return e == "0" ? "合格" : "不合格";
				}
			},
		},
		methods: {
			onSearch() {
				this.list = [];
				this.fetchPageNum = 1;
				this.refreshing = true;
				this.getData();
			},
			getData: function() {
				let that = this;
				let pageNo = that.refreshing ? 1 : that.fetchPageNum;
				api.findList(pageNo, that.form).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: "请求数据失败请重试"
						})
						return;
					}
					that.total = res.data.count;
					if (that.refreshing) {
						that.refreshing = false;
						uni.stopPullDownRefresh();
						that.list = res.data.list;
						that.fetchPageNum = res.data.next;
					} else {
						that.list = that.list.concat(res.data.list);
						that.fetchPageNum = res.data.next;
					}
				})
			},
			onAdd: function() {
				uni.navigateTo({
					url: '/pages/inspection/internalForm?inspectionSituation=' + this.form.inspectionSituation
				})
			},
			onView: function(id) {
				uni.navigateTo({
					url: "/pages/inspection/internalView?id=" + id,
				})
			},
			onConfirm(e) {
				this.form.productName = e.value;
				this.onSearch()
			},
			onCancel(e) {
				this.form.productName = "";
				this.onSearch()
			},
			queryPopupShow() {
				this.$refs.queryPopup.open();
			},
			onSelect(queryForm) {
				let that = this;
				that.form.startDate = queryForm.startDate;
				that.form.endDate = queryForm.endDate;
				that.form.inspectionResult = queryForm.inspectionResult;
				that.onSearch();
				this.$refs.queryPopup.close()
			},
		},
		filters: {
			formatDate(time) {
				let date = new Date(time)
				return formatDate(date, 'yyyy-MM-dd') //年-月-日 时分
			}
		}
	}
</script>

<style lang="scss" scoped>
	.fixBt {
		position: fixed;
		width: 120upx;
		height: 120upx;
		text-align: center;
		line-height: 120upx;
		border-radius: 50%;
		background-color: #02994f;
		color: #fff;
		right: 5upx;
		bottom: 40upx;
	}

	.safe-body {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		width: 100%;
	}

	.index {
		flex: 1;
		width: 750rpx;
		flex-direction: column;
		/* 相当于上下居中 */
		justify-content: center;
		/* 相当于水平居中 */
		align-items: center;
	}

	.row {
		flex-direction: row;
	}

	.column {
		flex-direction: column;
	}

	.load-bottom {
		width: 100%;
		text-align: center;
	}

	.loadMore {
		font-size: 30upx;
		color: #555;
		margin-bottom: 20upx;
		line-height: 60rpx;
		margin-left: 170px;
	}

	.emptyData {
		margin-left: 175px;
	}

	.news-item {
		margin-left: 30rpx;
		margin-right: 30rpx;
		display: flex;
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		padding-top: 15px;
		padding-bottom: 15px;
		border-bottom: 2rpx #efefef solid;
	}

	.news-item .news-intr {
		flex: 2;
		padding-right: 20rpx;
		/* display: flex; */
		flex-direction: column;
		justify-content: space-between;
	}

	.news-intr .news-title {}

	.news-intr .news-info {
		display: flex;
		font-size: 12px;
		color: #999999;
	}

	.news-info .news-ago {
		text-align: left;
	}

	.news-info .news-type {
		text-align: right;
		flex: 2;
	}

	.news-item .news-image {
		flex: 1;
		max-width: 240rpx;
		max-height: 160rpx;
	}

	.news-item .news-image image {
		max-width: 100%;
		max-height: 100%;
	}

	.left {
		margin-left: 130rpx;
	}
	.triangle-up {
		margin-top: 3px;
		/*display:inline-block;*/
		width: 0;
		height: 0;
		border-left: 5px solid transparent;
		border-right: 5px solid transparent;
		border-bottom: 7px solid #fff;
		/* background: #fff; */
	}
	
	.triangle-down {
		margin-top: 3px;
		/*display:inline-block;*/
		width: 0;
		height: 0;
		border-left: 5px solid transparent;
		border-right: 5px solid transparent;
		border-top: 7px solid #9dd6b7;
		/* background: #9dd6b7; */
	}
	.number_all {
		width: 33%;
	}
	.message_all {
		//height: 300px;
		overflow: auto;
	}
</style>
