<template>
	<view class="uni-popup-share">
		<view class="uni-share-content">
			<view class="uni-share-content-box">
				<view class="whxx_all">
					<view class="whxx_left">选择承诺依据</view>
				</view>
				<view class="whxx_all_hist">
					<picker  @change="changePickerInspectionSituation" :range="inspectionSituationList" :range-key="'label'">
						<view class="all">
							{{inspectionSituationLabel}}
						</view>
					</picker>
				</view>
				<view class="whxx_all">
					<view class="whxx_left">选择检测时间范围</view>
				</view>
				<view class="whxx_all_hist" >
					<biaofun-datetime-picker class="all" ref="startDateRef" placeholder="起始时间" fields="day" @change="changeStartDate"></biaofun-datetime-picker>
				</view>
				<view class="whxx_all_hist" >
					<biaofun-datetime-picker class="all" ref="endDateRef" placeholder="结束时间" fields="day" @change="changeEndDate"></biaofun-datetime-picker>
				</view>
				<view class="whxx_all">
					<view class="whxx_left">选择检测结果</view>
				</view>
				<view class="whxx_all_hist">
					<picker  @change="changePickerInspectionResult" :range="inspectionResultList" :range-key="'name'">
						<view class="all">
							{{inspectionResultLabel}}
						</view>
					</picker>
				</view>
			</view>
		</view>
		<view class="uni-share-button-box">
			<button class="uni-share-button" type="default" size="default" style="color: #000;width: 100px;float:right;height: 40px;line-height: 40px;font-size:14px;" @click="close" >重置</button>
			<button class="uni-share-button" type="primary" size="default" style="color: #fff;width: 100px;float:right;height: 40px;line-height: 40px;font-size:14px;background-color: #02994f;" @click="select">筛选</button>
		</view>
	</view>
</template>

<script>
	
	import BiaofunDatetimePicker from "@/components/biaofun-datetime-picker/biaofun-datetime-picker";
	export default {
		inject: ['popup'],
		data() {
			return {
				form:{
					inspectionSituation:"",
					startDate:null,
					endDate:null,
					inspectionResult:"",
				},
				inspectionResultList: [
					{"name":"合格","value":"0"},
					{"name":"不合格","value":"1"},
				],
				date: new Date().toISOString().slice(0, 10),
			}
		},
		props: {
			inspectionSituationList: {
				type: Array,
			},
		},
		onLoad: function(option) {
			
		},
		computed: {
			//检测情况标题
			inspectionSituationLabel() {
			  let label="请选择承诺依据";
			  if(this.form.inspectionSituation){
				  let item=this.inspectionSituationList.filter(item => item.value === this.form.inspectionSituation)[0];
				  label=item.label;
			  }
			  return label;
			},
			//检测结果标题
			inspectionResultLabel() {
			  let label="请选择检测结果";
			  if(this.form.inspectionResult){
				  let item=this.inspectionResultList.filter(item => item.value === this.form.inspectionResult)[0];
				  label=item.name;
			  }
			  return label;
			},
		},
		methods: {
			//更换检测情况
			changePickerInspectionSituation(e) {
			    let index = e.target.value;
			    var selected = this.inspectionSituationList[index];    //获取选中的数组
				this.form.inspectionSituation = selected.value;
			},
			//更换检测结果
			changePickerInspectionResult(e) {
			    let index = e.target.value;
			    var selected = this.inspectionResultList[index];    //获取选中的数组
				this.form.inspectionResult = selected.value;
			},
			/* 时间选择 */
			changeStartDate(res) {
			    this.form.startDate = res.f1;
				if(this.form.startDate && this.form.endDate){
					let stDate = new Date(this.form.startDate).getTime();
					let enDate = new Date(this.form.endDate).getTime();
					if(stDate>enDate){
						this.form.startDate=''
						this.$refs.startDateRef.dateStr=''
						uni.showToast({
							icon: 'none',
							title: '起始时间不能大于结束时间'
						});
					}
				}
			},
			changeEndDate(res) {
			    this.form.endDate = res.f1;
				if(this.form.startDate && this.form.endDate){
					let stDate = new Date(this.form.startDate).getTime();
					let enDate = new Date(this.form.endDate).getTime();
					if(stDate>enDate){
						this.form.endDate = ''
						this.$refs.endDateRef.dateStr=''
						uni.showToast({
							icon: 'none',
							title: '结束时间不能小于起始时间'
						});
					}
				}
			},
			/**
			 * 选择内容
			 */
			select() {
				this.$emit('select', this.form,
				/* , () => {
					this.popup.close()
				} */)
				//this.clear();
			},
			/**
			 * 关闭窗口
			 */
			close() {
				this.clear();
				//this.popup.close()
			},
			//清空选择项
			clear(){
				this.form={
					inspectionSituation:"",
					startDate:null,
					endDate:null,
					inspectionResult:"",
				}
				this.$refs.startDateRef.clear();
				this.$refs.endDateRef.clear();
			}
		}
	}
</script>
<style lang="scss" scoped>
	.uni-popup-share {
		background-color: #fff;
	}
	.uni-share-title {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		align-items: center;
		justify-content: center;
		height: 40px;
	}
	.uni-share-title-text {
		font-size: 14px;
		color: #666;
	}
	.uni-share-content {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		justify-content: center;
		padding-top: 10px;
	}
	
	.uni-share-content-box {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		flex-wrap: wrap;
		width: 360px;
	}
	
	.uni-share-content-item {
		width: 90px;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		justify-content: center;
		padding: 10px 0;
		align-items: center;
	}
	
	.uni-share-content-item:active {
		background-color: #f5f5f5;
	}
	
	.uni-share-image {
		width: 30px;
		height: 30px;
	}
	
	.uni-share-text {
		margin-top: 10px;
		font-size: 14px;
		color: #3B4144;
	}
	
	.uni-share-button-box {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		padding: 10px 15px;
	}
	.select{
		background-color: rgb(1, 190, 110);
	}
	.all {
		width: 100%;
		float: left;
		padding:10px 0;
		font-size: 12px;
		color: #888888;
	}
</style>
