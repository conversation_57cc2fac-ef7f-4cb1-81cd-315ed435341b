<template>
	<view>
		<view style="overflow: auto;">
			<view style="float:left;width:100%;">
				<uni-search-bar placeholder="输入单位名称" @input="onConfirm" v-model="searchValue" @cancel="onCancel"></uni-search-bar>
			</view>
		</view>
    <view class="message_all" :style="{'height':messageHeight}" v-if="form.name === ''">
      <view class="search-msg">
        请输入单位名称进行搜索
      </view>
    </view>
		<view class="message_all" :style="{'height':messageHeight}" v-if="form.name !== ''">
			<view class="news-item" v-for="(item,index) in list" :key="index" >
				<view class="news-intr" @click="selectItem(item)" style="width: 90%;float:left;">
					<view class="flex-table" style="overflow: auto;">
						<p style="width:100%;">
							{{item.name}}
						</p>
					</view>
          <view class="flex-table" style="overflow: auto;margin-top: 10px;">
            <p style="width:100%;">
              {{item.entType ==='0' || item.entType === '2' ? '统一社会信用代码：' + item.socialCode : '身份证号：' + maskIdCard(item.cardNo)}}
            </p>
          </view>
				</view>
				<uni-icons type="arrowright" size="17" style="float:left;color:grey;width:20px;height:30px;line-height:30px;text-align: right;" @click="selectItem(item)"></uni-icons>
			</view>
      <uni-load-more :status="status" :content-text="contentText" />
		</view>
	</view>
</template>

<script>
	import entApi from '@/api/ent.js';
	import {
		formatDate
	} from '@/common/formatDate.js';
	import { maskIdCard } from '@/common/dataUtils.js';
	export default {
		components: {
		},
		data: function() {
			return {
				messageHeight:"",
				form: {
          name: "",
				},
				list: [],
        status: 'noMore',
        contentText: { // 加载提示
          contentrefresh: "正在加载...",
          contentnomore: "没有更多数据了"
        },
        total: 0, //总数
        refreshing: false, //为true表示正在刷新
        fetchPageNum: 1, //当前页数
        searchValue: ""
			}

		},
		onLoad: function(option) {
			let that=this;
			uni.getSystemInfo({
				success: function(res) {
					that.messageHeight = (res.screenHeight*0.8)+"px";
					console.log("messageHeight:"+that.messageHeight);
				}
			});
      uni.$once('entSearch', (data)=>{
        if (data) {
          this.searchValue = data;
        }
      })
		},
		onShow: function() {
			this.onSearch();
		},
    onReachBottom() {
      if (this.list.length < this.total) {
        this.status = 'loading';
        this.getData();
      } else {
        this.status = 'noMore'; //没有数据时显示‘没有更多’
      }

    },
		computed: {
			result() {
				return function(e) {
					return e == "0" ? "合格" : "不合格";
				}
			},
		},
		methods: {
			// 身份证号脱敏处理
			maskIdCard,
			onSearch() {
				this.list = [];
        this.fetchPageNum = 1;
        this.refreshing = true;
				this.getData();
			},
			getData: function() {
        if (this.form.name === "") {
          return;
        }
				let that = this;
        let pageNo = that.refreshing ? 1 : that.fetchPageNum;
				entApi.findEntListPageQuick(pageNo, that.form).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: "请求数据失败请重试"
						})
						return;
					}
          that.total = res.data.count;
          if (that.refreshing) {
            that.refreshing = false;
            that.list = res.data.list;
            that.fetchPageNum = res.data.next;
          } else {
            that.list = that.list.concat(res.data.list);
            that.fetchPageNum = res.data.next;
          }
				})
			},
      selectItem(ent) {
        uni.navigateBack({
          delta: 1,
          success() {
            setTimeout(() => {
              uni.$emit('setEnt', ent)
            }, 500)
          }
        });
			},
			onConfirm(e) {
				this.form.name = this.searchValue;
				this.onSearch()
			},
			onCancel(e) {
				this.form.name = "";
				this.onSearch()
			},
		},
		filters: {
			formatDate(time) {
				let date = new Date(time)
				return formatDate(date, 'yyyy-MM-dd hh:mm') //年-月-日 时分
			}
		}
	}
</script>

<style lang="scss" scoped>
	.fixBt {
		position: fixed;
		width: 120upx;
		height: 120upx;
		text-align: center;
		line-height: 120upx;
		border-radius: 50%;
		background-color: #02994f;
		color: #fff;
		right: 5upx;
		bottom: 40upx;
	}

	.safe-body {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		width: 100%;
	}

	.index {
		flex: 1;
		width: 750rpx;
		flex-direction: column;
		/* 相当于上下居中 */
		justify-content: center;
		/* 相当于水平居中 */
		align-items: center;
	}

	.row {
		flex-direction: row;
	}

	.column {
		flex-direction: column;
	}

	.load-bottom {
		width: 100%;
		text-align: center;
	}

	.loadMore {
		font-size: 30upx;
		color: #555;
		margin-bottom: 20upx;
		line-height: 60rpx;
		margin-left: 170px;
	}

	.emptyData {
		margin-left: 175px;
	}

	.news-item {
		height: 50px;
		margin-left: 30rpx;
		margin-right: 30rpx;
		display: flex;
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		padding-top: 15px;
		padding-bottom: 15px;
		border-bottom: 2rpx #efefef solid;
	}

	.news-item .news-intr {
		flex: 2;
		padding-right: 20rpx;
		/* display: flex; */
		flex-direction: column;
		justify-content: space-between;
	}

	.news-intr .news-title {}

	.news-intr .news-info {
		display: flex;
		font-size: 12px;
		color: #999999;
	}

	.news-info .news-ago {
		text-align: left;
	}

	.news-info .news-type {
		text-align: right;
		flex: 2;
	}

	.news-item .news-image {
		flex: 1;
		max-width: 240rpx;
		max-height: 160rpx;
	}

	.news-item .news-image image {
		max-width: 100%;
		max-height: 100%;
	}

	.left {
		margin-left: 130rpx;
	}
	.triangle-up {
		margin-top: 3px;
		/*display:inline-block;*/
		width: 0;
		height: 0;
		border-left: 5px solid transparent;
		border-right: 5px solid transparent;
		border-bottom: 7px solid #fff;
		/* background: #fff; */
	}
	
	.triangle-down {
		margin-top: 3px;
		/*display:inline-block;*/
		width: 0;
		height: 0;
		border-left: 5px solid transparent;
		border-right: 5px solid transparent;
		border-top: 7px solid #9dd6b7;
		/* background: #9dd6b7; */
	}
	.number_all {
		width: 33%;
	}
	.message_all {
		//height: 300px;
		overflow: auto;
	}
  .search-msg {
    text-align: center;
    width: 100%;
    padding-top: 15vh;
    font-size: 15px;
    color: #777777;
  }
</style>
