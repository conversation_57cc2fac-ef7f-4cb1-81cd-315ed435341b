<template>
	<view class="index">
		<view class="content-item" v-for="(item,index) in list" :key="index">
            <view>
                <view class="item-left">
                    <img :src=item.queryCodeUrl @click="clickQueryCode(item.queryCodeUrl)" style="width: 60px; height: 60px;">
                </view>
                <view class="item-middle"  @click="viewDetail(item.sampleNo)">
                    <view class="big-title">样品名称 {{item.sampleName}} </view>
                    <view class="big-title">样品编号 {{item.sampleNo}}</view>
                </view>
            </view>
		</view>
        <uni-load-more :status="status" :content-text="contentText" />
	</view>
</template>
 
<script>
	import api from '../../api/inspection.js';
    import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';

	export default {
		components: {
            uniLoadMore
		},
		data: function() {
			return {
				token:uni.getStorageSync("token"),
				list: [],
                productId:'',
                //分页相关
                total: 0, //总数
                refreshing: false, //为true表示正在刷新
                fetchPageNum: 1, //当前页数
                loading: true, // 为true表示加载中
                status: 'noMore',
                contentText: { // 加载提示
                    contentrefresh: "正在加载...",
                    contentnomore: "没有更多数据了"
                },
			}

		},
		onLoad: function(option) {
			uni.setNavigationBarTitle({
				title: '农药兽药检测'
			});
			this.productId=option.id;
            this.list=[];
            this.fetchPageNum=1;
            this.refreshing = true;
			this.getData();
		},
		onShow: function() {
			if(!this.token){
				return ;
			}
		},
        onReachBottom() {
            if (this.list.length < this.total) {
                this.status = 'loading';
                this.getData();
            } else {
                this.status = 'noMore'; //没有数据时显示‘没有更多’
            }
        },
        onPullDownRefresh() {
            console.log('下拉刷新');
            this.refreshing = true;
            this.getData();
        },
		methods: {
			getData: function() {
				let that = this;
                that.loading = true;
				var productRecord ={
				    productId : this.productId
                }
                let pageNo = that.refreshing ? 1 : that.fetchPageNum;
				api.findInspectionRecordList(pageNo,productRecord).then(res => {
					if (res.code != 0) {
						uni.showToast({
							icon: 'none',
							title: "请求数据失败请重试"
						})
						return;
					} else {
                        that.total = res.data.count;
                        if (that.refreshing) {
                            that.list=[];
                            that.refreshing = false;
                            uni.stopPullDownRefresh();
                            if(res.data.list){
                                that.list = res.data.list;
                            }
                            that.fetchPageNum = res.data.next;
                        } else {
                            that.list = that.list.concat(res.data.list);
                            that.fetchPageNum = res.data.next;
                        }
					}
				})
			},
			viewDetail:function (sampleNo) {
				uni.navigateTo({
					url: "./viewDetail?sampleNo="+sampleNo,
				})
			},
            //点击二维码查看大图
            clickQueryCode:function(url){
                let urls = [];
                urls[0] = url;
                uni.previewImage({
                    urls: urls
                });
            },
		},
	}
</script>

<style scoped lang="scss">
	.index {
		flex: 1;
		width: 750rpx;
		flex-direction: column;
		/* 相当于上下居中 */
		justify-content: center;
		/* 相当于水平居中 */
		align-items: center;
	}
	.content-item{
		width: 90%;
		margin-left: 5%;
		display: inline-block;
		border-bottom: 1px solid #e0e0e0;
		.item-left{
			float: left;
		}
		.item-middle{
			float: left;
			.big-title{
				font-size: 16px;
                padding-top: 5px;
			}
		}
	}
</style>
