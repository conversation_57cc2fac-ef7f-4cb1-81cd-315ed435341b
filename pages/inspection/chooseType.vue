<template>
	<view v-show="show">
		<view class="choose-header">
			<view class="choose-header-title">
				承诺依据
			</view>
			<view class="choose-header-tit">
				<view style="font-size: 12px;float: left;color: #ccc;">
					可根据承诺依据选择以下方式
				</view>
				<view style="font-size: 12px;float: right;color:blue;">
					<view @click="goRecordPage"><a href="javascript:;">历史承诺记录>></a></view>
				</view>
			</view>
		</view>
		<view class="whztxx_all" style="margin-bottom: 50px;">
			<div v-if="showFastReq" class="whztxx_cont" @click="setType('is02')" style="height: 100px;">
			        <div class="whztxx_img" >
			            <image :src="imgPath+'img/img29.png'" alt="" style="width: 70px;height: 70px;margin-top: 17px;">  
			        </div>
			        <div class="whztxx_message">
						<div class="whztxx_mess">
							<p>快检申请</p>
							<p style="font-size:12px;font-weight: normal;color: #ccc;">主体使用快速检测设备对产品各项指标进行全面检验<!-- <br />(可用于证明自检合格) --></p>
						</div>
			            <uni-icons type="arrowright" size="25" style="color:grey;float: right;"></uni-icons>
			        </div>
			</div>
			<div class="whztxx_cont" @click="setType('is04')" style="height: 100px;">
			        <div class="whztxx_img" >
			            <image :src="imgPath+'img/img30.png'" alt="" style="width: 70px;height: 70px;margin-top: 17px;">
			        </div>
			        <div class="whztxx_message">
						<div class="whztxx_mess">
							<p>自行检测合格依据录入</p>
							<p style="font-size:12px;font-weight: normal;color: #ccc;">主体使用自持设备对产品各项指标进行的全面检验</p>
						</div>
			            <uni-icons type="arrowright" size="25" style="color:grey;float: right;"></uni-icons>
			        </div>
			</div>
			<div class="whztxx_cont" @click="setType('is03')" style="height: 100px;">
			        <div class="whztxx_img" >
			            <image :src="imgPath+'img/img31.png'" alt="" style="width: 70px;height: 70px;margin-top: 17px;">
			        </div>
			        <div class="whztxx_message">
						<div class="whztxx_mess">
							<p>委托检测合格依据录入</p>
							<p style="font-size:12px;font-weight: normal;color: #ccc;">主体委托具有法定检验资格的检验机构进行检验</p>
						</div>
			            <uni-icons type="arrowright" size="25" style="color:grey;float: right;"></uni-icons>
			        </div>
			</div>
			<div class="whztxx_cont" @click="setType('is05')" style="height: 100px;">
			        <div class="whztxx_img" >
			            <image :src="imgPath+'img/img33.png'" alt="" style="width: 70px;height: 70px;margin-top: 17px;">
			        </div>
			        <div class="whztxx_message">
						<div class="whztxx_mess">
							<p>质量安全控制符合要求依据录入</p>
							<p style="font-size:12px;font-weight: normal;color: #ccc;">主体自身的内部质量控制的证明文件</p>
						</div>
			            <uni-icons type="arrowright" size="25" style="color:grey;float: right;"></uni-icons>
			        </div>
			</div>
		</view>
	</view>
</template>

<script>
	import uniIcons from "@/components/uni-icons/uni-icons.vue"
	var config = require('../../common/config.js')
	export default {
		components: {uniIcons},
		data() {
			return {
				imgPath:config.IMG_PATH,
				show:false,
				token:uni.getStorageSync("token"),
				showFastReq:config.SHOW_FAST_REQ,
			}
		},
		onShow: function(option) {
			/**
			 * 判断逻辑
			 * 1、判断token是否存在
			 * 2、判断当前ent缓存，如果不存在，提示需要先维护主体信息
			 * 3、判断当前主体信息是否需要补入基础信息采集数据
			 * 4、判断主体审核状态，审核中，跳转至主体查看页面
			 * 5、判断主体审核状态，如果examineStatus == '90'，那么说明此主体是变更暂存，跳转至变更页面
			 * 6、以上逻辑都通过后显示当前页面功能
			 */
			if (!this.token) {
				return;
			}
			let that = this;
			//验证用户是否维护了主体信息
			let ent = uni.getStorageSync("ent");
			if (!ent) {
				uni.showModal({
					title: '先维护主体信息',
					content: '确定前往维护主体信息?',
					mask: true,
					success: function(res) {
						if (res.confirm) {
							that.$Router.replaceAll("/pages/ent/chooseType")
						} else if (res.cancel) {
							that.$Router.replaceAll("/pages/index/index")
						}
					}
				});
				return;
			} else if (ent.basicFlag == '1' && ent.basicEnterFlag == '0') {
				uni.showModal({
					icon: "none",
					content: '未在基础数据采集系统内查询到您的信息,请前往填写详细信息',
					mask: true,
					showCancel: false,
					success: function(res) {
						that.$Router.replaceAll("/pages/basic/basicEntForm")
					}
				});
				return;
			} else if (ent.examineStatus === '0' || ent.examineStatus === '-1') {
				uni.showModal({
					icon: "none",
					content: '主体信息审核中，前往主体信息查看审核状态？',
					mask: true,
					success: function(res) {
						if (res.confirm) {
							that.$Router.replaceAll("/pages/ent/chooseType")
						} else if (res.cancel) {
							that.$Router.replaceAll("/pages/index/index")
						}
					}
				});
				return;
			} else if (ent.examineStatus == '90') { //变更暂存状态：跳转至变更页面
				uni.redirectTo({
					url: "/pages/entChange/viewCheck",
				})
				return;
			} else if (ent.frozenFlag == '1'){
				uni.showModal({
					icon: "none",
				    content: '您的账号已冻结，请联系技术服务人员取消冻结',
					mask: true,
					showCancel:false,
				    success: function (res) {
						that.$Router.replaceAll("/pages/index/index")
				    }
				});
				return ;
			}else {
				that.show = true;
			}
		},
		methods: {
			setType: function(type) {
				if('is02'===type){
					uni.navigateTo({
						url: "/pages/inspection/list"
					})
				}else if('is05'===type){
					uni.navigateTo({
						url: "/pages/inspection/internalList?inspectionSituation="+type
					})
				}else{
					uni.navigateTo({
						url: "/pages/inspection/enterList?inspectionSituation="+type
					})
				}
			},
			goRecordPage(){
				uni.navigateTo({
					url: "/pages/inspection/recordList"
				})
			}
		},
		
	}
</script>

<style lang="scss" scoped>
	.choose-header {
		width: 80%;
		margin: 20px auto;
		overflow: auto;
		.choose-header-title {
			font-size: 18px;
			font-weight: bold;
			color: #000;
		}
		.choose-header-tit {
			width: 100%;
			margin: 15px 0;
		}
	}
	.whztxx_message {
	    width: 60%;
		height: 100px;
	    float: left;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.whztxx_message p {
	    font-size: 16px;
	    font-weight: bold;
	    color: #333;
	    float: left;
	}
	.whztxx_message image {
	    float: right;
	    width: 8px;
	    margin-top: 52px;
	} 
	.whztxx_mess {
		width: 85%;
	}
</style>
