<template>
	<view>
		<view style="width: 100%;overflow: auto;position: fixed;top: 0;background: #fff;">
			<view style="float:left;width:80%;">
				<uni-search-bar placeholder="输入样品名称" @confirm="onConfirm" @cancel="onCancel"></uni-search-bar>
			</view>
			<view style="float:left;width:20%;">
				<button type="primary" style="color: #fff; background-color: #02994f;width: 60px;float:right;height: 30px;line-height: 30px;font-size:14px;margin-top: 10px;margin-right: 10px;" @click="queryPopupShow()">筛选</button>
			</view>
		</view>
		<view class="content-all">
			<view class="news-item" v-for="(item,index) in list" :key="index" >
				<view class="news-intr" @click="onView(item)" style="width: 90%;float:left;">
					<view class="flex-table" style="overflow: auto;">
						<p style="float:left;width:60%;">
							{{item.productName}}
						</p>
						<p style="float:left;width:40%;text-align: right;"></p>
					</view>
          <view class="flex-table" style="overflow: auto;margin-top: 10px;">
            <p style="float:left;width:60%;">
              {{item.entName ? item.entName : '--'}}
            </p>
            <p style="float:left;width:40%;text-align: right;"></p>
          </view>
					<view class="news-info" style="width: 100%;margin-top: 10px;">
						<p style="float:left;width:50%;">
							{{item.inspectionSituation=='is05'?"上报时间":"检测时间"}}:{{item.inspectionDate}}
						</p>
						<p style="float:left;text-align: left;color: #02994f; display: flex;">{{inspectionSituation(item.inspectionSituation)}}</p>
						<!-- <p v-if="item.inspectionSituation!='is05'" style="float:left;width:20%;text-align: center;" :style="{'color':item.inspectionResult=='0'?'#02994f':'red'}">{{inspectionResult(item.inspectionResult)}}</p> -->
					</view>
				</view>
				<uni-icons type="arrowright" size="17" style="float:left;color:grey;width:30px;height:30px;line-height:30px;text-align: right;" @click="onView(item)"></uni-icons>
			</view>
			<uni-load-more :status="status" :content-text="contentText" />
		</view>
		<uni-popup ref="queryPopup" type="share">
			<record-list-query :inspectionSituationList="inspectionSituationList" @select="onSelect"></record-list-query>
		</uni-popup>
	</view>
</template>

<script>
	import api from '@/api/productInspection.js';
	import dictApi from "@/api/dict.js";
	import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';
	import recordListQuery from '@/pages/inspection/companyInspectRecordQuery.vue'
	import {
		formatDate
	} from '@/common/formatDate.js';
	export default {
		components: {
			uniLoadMore,
			recordListQuery,
		},
		data: function() {
			return {
				form: {
          inspectionCompanyId: uni.getStorageSync("ent").id,
					productName:"",
					inspectionSituation:"",
					startDate:null,
					endDate:null,
					inspectionResult:"",
				},
				inspectionSituationList: [],
				list: [],
				total: 0, //总数
				refreshing: false, //为true表示正在刷新
				fetchPageNum: 1, //当前页数
				status: 'noMore',
				contentText: { // 加载提示
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
			}

		},
		onLoad: function(option) {
			let that = this;
			that.getInspectionDict();
			/* uni.setNavigationBarTitle({
				title: '维护产品信息'
			}); */
		},
		onShow: function() {
			this.onSearch()
		},
		onReachBottom() {
			if (this.list.length < this.total) {
				this.status = 'loading';
				this.getData();
			} else {
				this.status = 'noMore'; //没有数据时显示‘没有更多’
			}

		},
		onPullDownRefresh() {
			console.log('下拉刷新');
			this.refreshing = true;
			this.getData();
		},
		computed: {
		    inspectionResult() {
			  return function(e){
				  return e=="0"?"合格":"不合格";
			  }
		    },
			inspectionSituation() {
			  return function(e){
				  let result="";
				  if(e=='is02'){
					  result='快检合格';
				  }else if(e=='is03'){
					  result='委托检测合格';
				  }else if(e=='is04'){
					  result='自行检测合格';
				  }else if(e=='is05'){
					  result='质量安全控制符合要求';
				  }
				  return result;
			  }
			},
		  },
		methods: {
			//获取字典项：检测情况
			getInspectionDict() {
			    dictApi.findDict({
			        type: 'inspection_situation_code'
			    }).then(res => {
					if (res.code === 1) {
						uni.showToast({
							icon: 'none',
							title: res.message,
							mask: true,
							duration: 1500
						});
						return;
					}
					//去掉“无检测信息”
					this.inspectionSituationList=res.data.filter(item => item.value !== 'is01');
			    })
			},
			onSearch(){
				this.list = [];
				this.fetchPageNum = 1;
				this.refreshing = true;
				this.getData();
			},
			getData: function() {
				let that = this;
				let pageNo = that.refreshing ? 1 : that.fetchPageNum;
				api.findList(pageNo, that.form).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: "请求数据失败请重试"
						})
						return;
					}
					that.total = res.data.count;
					if (that.refreshing) {
						that.refreshing = false;
						uni.stopPullDownRefresh();
						that.list = res.data.list;
						that.fetchPageNum = res.data.next;
					} else {
						that.list = that.list.concat(res.data.list);
						that.fetchPageNum = res.data.next;
					}
				})
			},
			onView: function(item) {
        uni.navigateTo({
          url: "/pages/inspection/companyEnterView?id=" + item.id+"&editFlag=false",
        })
			},
			onConfirm(e){
				this.form.productName=e.value;
				this.onSearch()
			},
			onCancel(e){
				this.form.productName="";
				this.onSearch()
			},
			queryPopupShow(){
				this.$refs.queryPopup.open();
			},
			onSelect(queryForm) {
				let that =this;
				that.form=queryForm;
				that.form.inspectionCompanyId=uni.getStorageSync("ent").id;
				that.onSearch();
				this.$refs.queryPopup.close()
			},
		},
		filters: {
			formatDate(time) {
				let date = new Date(time)
				return formatDate(date, 'yyyy-MM-dd hh:mm') //年-月-日 时分
			}
		}
	}
</script>

<style lang="scss" scoped>

	.safe-body {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		width: 100%;
	}

	.index {
		flex: 1;
		width: 750rpx;
		flex-direction: column;
		/* 相当于上下居中 */
		justify-content: center;
		/* 相当于水平居中 */
		align-items: center;
	}

	.row {
		flex-direction: row;
	}

	.column {
		flex-direction: column;
	}

	.load-bottom {
		width: 100%;
		text-align: center;
	}

	.loadMore {
		font-size: 30upx;
		color: #555;
		margin-bottom: 20upx;
		line-height: 60rpx;
		margin-left: 170px;
	}

	.emptyData {
		margin-left: 175px;
	}

	.news-item {
		height: 65px;
		margin-left: 30rpx;
		margin-right: 30rpx;
		display: flex;
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		padding-top: 15px;
		padding-bottom: 15px;
		border-bottom: 2rpx #efefef solid;
	}

	.news-item .news-intr {
		flex: 2;
		padding-right: 20rpx;
		/* display: flex; */
		flex-direction: column;
		justify-content: space-between;
	}

	.news-intr .news-title {}

	.news-intr .news-info {
		display: flex;
		font-size: 12px;
		color: #999999;
	}

	.news-info .news-ago {
		text-align: left;
	}

	.news-info .news-type {
		text-align: right;
		flex: 2;
	}

	.news-item .news-image {
		flex: 1;
		max-width: 240rpx;
		max-height: 160rpx;
	}

	.news-item .news-image image {
		max-width: 100%;
		max-height: 100%;
	}

	.left {
		margin-left: 130rpx;
	}

	.triangle-up {
		margin-top: 3px;
		/*display:inline-block;*/
		width: 0;
		height: 0;
		border-left: 5px solid transparent;
		border-right: 5px solid transparent;
		border-bottom: 7px solid #fff;
		/* background: #fff; */
	}

	.triangle-down {
		margin-top: 3px;
		/*display:inline-block;*/
		width: 0;
		height: 0;
		border-left: 5px solid transparent;
		border-right: 5px solid transparent;
		border-top: 7px solid #9dd6b7;
		/* background: #9dd6b7; */
	}
	.content-all {
		width: 100%;
		margin-top: 50px;
	}
</style>
