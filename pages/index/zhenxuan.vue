<template>
    <view>
        <div>
            <image :src="useImg" mode="widthFix" style="width: 100%;" @click="toPage()"></image>
        </div>
    </view>
</template>

<script>
    import api from '../../api/img.js';
    export default {
        data: function() {
            return {
                useImg: ''
            }

        },
        onLoad: function(option) {
            this.getImg()
        },
        methods: {
            getImg() {
                let param = {
                    key: "shouye.zhenxuan.url"
                };
                api.getShouyeImgUrl(param).then(res => {
                    if (res.code != 0) {
                        uni.showToast({
                            title: "请求数据失败请重试"
                        })
                        return;
                    } else {
                        console.log(res.data.url)
                        this.useImg = res.data.url
                    }
                })
            },
            toPage() {
                uni.navigateToMiniProgram({
                    appId: 'wx806e5bc25572cfa0', //跳转的小程序的aooId
                    path: 'pages/index/index', //如果这里不填，默认是跳转到对方小程序的主页面
                    success(res) {
                        // 打开成功
                    }
                })
            }
        }
    }
</script>

<style>
</style>