<template>
    <view style="position: relative;height: calc(100vh - 60px - env(safe-area-inset-bottom));overflow: auto;">
        <!-- <image :src="imgPath+'img/index/img1.png'" alt="" class="index_bg_img" style="z-index: 0;height: 100%;"> -->
        <image :src="imgPath+'img/index/img16.png'" alt=""
            style="position: absolute;top: 60px;right: 20px;width: 140px;height:130px;z-index: 999;" />
        <view class="_index_cont_all"
            style="position: absolute;z-index: 99;">
            <image :src="imgPath+'img/index/img1.png'" alt="" class="index_bg_img" style="height: 100%;">
                <view class="header_tit">
                    <p>承诺达标合格证</p>
                </view>
                <view class="header_list">
                    <ul>
                        <li>
                            <image :src="imgPath+'img/index/img2.png'" alt="" />
                            <p>合格证电子化</p>
                        </li>
                        <li>
                            <image :src="imgPath+'img/index/img2.png'" alt="" />
                            <p>留存有记录</p>
                        </li>
                        <li>
                            <image :src="imgPath+'img/index/img2.png'" alt="" />
                            <p>产品可追溯</p>
                        </li>
                    </ul>
                </view>
                <view class="banner" v-if="loadComplete && (!ent || ent.businessType !== '2')">
                    <view class="banner_left" @click="gotoCertificate()">
                        <image :src="imgPath+'img/index/img4.png'" alt=""
                            style="width: 80px;height:80px;margin-top: 10px;" />
                        <p class="banner_tit">合格证开具</p>
                    </view>
                    <view class="banner_right">
                        <view class="sblj" @click="gotoBluetoothDevice()">
                            <image :src="imgPath+'img/index/img5.png'" class="fl">
                                <p class="banner_tit fl">设备连接</p>
                        </view>
                        <view class="kjjl" @click="gotoCertificateList()">
                            <image :src="imgPath+'img/index/img6.png'" class="fl">
                                <p class="banner_tit fl">开具记录</p>
                        </view>
                    </view>
                </view>

                <view class="cpxx_all" v-if="loadComplete" :style="{'margin-top: 60px;': ent && ent.businessType === '2'}">
                    <view class="title_all" >
                        <image :src="imgPath+'img/index/img9.png'" alt="" />
                        <p>农产品信息</p>
                    </view>
                    <view class="cpxx_list" v-if="loadComplete && (!ent || ent.businessType !== '2')">
                        <ul id="module">
                            <li style="margin-right: 1.5%" @click="gotoEntType()">
                                <image :src="imgPath+'img/index/img10.png'" alt="" />
                                <p>主体信息</p>
                            </li>
                            <li @click="gotoProduct()">
                                <image :src="imgPath+'img/index/img11.png'" alt="" />
                                <p>产品信息</p>
                            </li>
                            <li style="margin-right: 1.5%" @click="gotoInspection()">
                                <image :src="imgPath+'img/index/img15.png'" alt="" />
                                <p>承诺依据证明</p>
                            </li>
                            <li @click="gotoDirection()">
                                <image :src="imgPath+'img/index/img14.png'" alt="" />
                                <p>使用说明</p>
                            </li>
                            <li style="margin-right: 1.5%" @click="gotoGuidance()">
                                <image :src="imgPath+'img/index/img12.png'" alt="" />
                                <p>农业技术指导</p>
                            </li>
                            <!-- <li @click="gotoVideo()">
								<image :src="imgPath+'img/index/img13.png'" alt="" />
								<p>视频培训</p>
							</li> -->
                            <li v-if="shopEnbale && token==''">
                                <image :src="imgPath+'img/index/img21.png'" alt="" />
                                <button type="default" @click="gotoShop"
                                    style="float: left;width: 50%;background: none;border: none;height:70px;font-size: 12px;text-align: left;line-height: 70px;padding-left: 0;">耗材商城</button>
                            </li>
                            <li v-if="shopEnbale && token!=''">
                                <image :src="imgPath+'img/index/img21.png'" alt="" />
                                <button type="default" open-type="getUserInfo" @getuserinfo="onGetAuth"
                                    style="float: left;width: 50%;background: none;border: none;height:70px;font-size: 12px;text-align: left;line-height: 70px;padding-left: 0;">耗材商城</button>
                            </li>
                        </ul>
                    </view>
                    <view class="cpxx_list" v-if="loadComplete && ent && ent.businessType === '2'">
                      <ul id="module">
<!--                        <li style="margin-right: 1.5%" @click="gotoEntType()">
                          <image :src="imgPath+'img/index/img10.png'" alt="" />
                          <p>主体信息</p>
                        </li>
                        <li @click="gotoInspectionList()">
                          <image :src="imgPath+'img/index/img11.png'" alt="" />
                          <p>上传记录</p>
                        </li>
                        <li style="margin-right: 1.5%" @click="gotoInspectionEnter()">
                          <image :src="imgPath+'img/index/img15.png'" alt="" />
                          <p>承诺依据证明</p>
                        </li>-->
                        <li style="width: 98%;" @click="gotoInspectionReport()">
                          <image :src="reportPath" alt="" />
                          <p style="font-weight: bold; margin-bottom: 4px;">检测报告管理</p>
<!--                          <p style="font-size: 11px; color: #666;">维护受检单位送检样品的检验检测报告</p>-->
                        </li>
                      </ul>
                    </view>
                </view>
                <view :style="{width: '100%', textAlign: 'center', fontSize: '12px', color: '#ACADAD', marginTop: (ent && ent.businessType === '2') ? '240px' : '20px'}">
                    <view>
                        技术支持单位:{{powerBy}}
                    </view>
                    <view>
                        服务电话:{{serviceTel}}
                    </view>
                </view>
        </view>
        <view class="index-go-btn" @click="toAssisting()" v-if="loadComplete && (!ent || ent.businessType !== '2')">
            <image src="../../static/img/icon-hy.png" mode="widthFix" style="width: 100%;"></image>
        </view>

        <tarbar ref="tarbar"></tarbar>

    </view>

</template>

<script>
    import entApi from '../../api/ent.js';
    import loginApi from '../../api/login.js';
    import userApi from '../../api/user.js';
    import Tarbar from "../../components/tabbar/tarbar.vue";
    var config = require('../../common/config.js')
    export default {
      components: {Tarbar},
        data() {
            return {
              reportPath:"../../static/img/report.png",
                imgPath: config.IMG_PATH,
                inspectionSituationEnbale: config.INSPECTION_SITUATION_ENABLED,
                shopEnbale: config.SHOP_ENABLED,
                powerBy: config.POWER_BY,
                serviceTel: config.SERVICE_TEL,
                title: '',
                token: uni.getStorageSync("token"),
                openId: uni.getStorageSync("openId"),
                screenHeight: "",
                ent: {},
                loadComplete: false
            }
        },
        onLoad() {

        },
        onShow() {
            this.loadComplete = false;
            let that = this;
            uni.getSystemInfo({
                success: function(res) {
                    that.screenHeight = res.screenHeight;
                }
            });
            if (that.token) {
                that.getEnt();
            } else {
              this.loadComplete = true;
            }
        },
        onTabItemTap(e) {
            // tab 点击时执行，此处直接接收单击事件
            console.log(e)
        },
        methods: {
            //跳转助农服务
            toAssisting: function() {
                uni.navigateTo({
                    url: '/pages/index/assisting'
                })
            },
            //主体信息跳转
            gotoEntType: function() {
                this.checkToken("/pages/ent/chooseType", "replaceAll")
            },
            //产品信息跳转
            gotoProduct: function() {
                this.checkToken("/pages/product/product-list", "replaceAll")
            },
            //农业技术指导跳转
            gotoGuidance: function() {
                this.$Router.push("/pages/guidance/list")
            },
            //视频培训跳转
            gotoVideo: function() {
                this.$Router.push("/pages/video/list")
            },
            //使用说明跳转
            gotoDirection: function() {
                this.$Router.push("/pages/direction/direction")
                //模拟小程序跳转
                /*uni.navigateToMiniProgram({
                    appId: 'wxef85757ba4006c16',
                    path: 'pages/index/indexCloud?authCode=950ade3a952446a592ec97e0f4a214bd',
                    //envVersion:'trial',
                    success(res) {
                        console.log(res);
                        // 打开成功
                    },
                    fail(res){
                        console.log(res);
                    }
                })*/
                //模拟祥云跳转
                /*uni.navigateTo({
                    url: '/pages/index/indexCloud?authCode=0d7b53f75c21481b965c73e3e31a7b78'
				});*/
                //this.$Router.push("/pages/certificate/showCertificateBaishan");
            },
            //农药兽药检测跳转
            gotoInspection: function() {
                if (this.inspectionSituationEnbale) {
                    //this.checkToken("/pages/inspection/list", "push")
                    this.checkToken("/pages/inspection/chooseType", "push")
                } else {
                    uni.showToast({
                        icon: 'none',
                        title: '功能正在努力开发中，敬请期待',
                        duration: 2000
                    });
                }
            },
            gotoCheck: function() {
                uni.showToast({
                    icon: 'none',
                    title: '功能正在努力开发中，敬请期待',
                    duration: 2000
                });
            },
            //设备连接
            gotoBluetoothDevice() {
                this.checkToken("/pages/device/searchDevice", "push")
            },
            //合格证开证
            gotoCertificate() {
                this.checkToken("/pages/certificate/certificate", "push")
            },
            //开具记录
            gotoCertificateList: function() {
                this.checkToken("/pages/certificate/list", "push")
            },
            gotoInspectionList() {
              this.checkToken("/pages/inspection/companyInspectRecordList", "push")
            },
            gotoInspectionEnter() {
              this.checkToken("/pages/inspection/companyEnterForm", "push")
            },
          gotoInspectionReport() {
            if (!this.token) {
                this.gotoLogin();
            } else {
                uni.navigateTo({
                    url: '/inspectionReport/index/inspectionReportList'
                })
            }
          },
            gotoShop() {
                if (!this.token) {
                    this.gotoLogin();
                }
            },
            onGetAuth() {
                uni.showLoading({
                    title: '跳转中',
                    mask: true,
                });
                const that = this;
                uni.getProvider({
                    service: "oauth",
                    success: (res) => {
                        //支持微信
                        if (~res.provider.indexOf('weixin')) {
                            uni.login({
                                provider: "weixin",
                                success: (login_res) => {
                                    uni.getUserInfo({
                                        success(info_res) {
                                            uni.setStorageSync('wechatUser', JSON.parse(
                                                info_res.rawData));
                                            let param = {
                                                code: login_res.code,
                                                rawData: info_res.rawData,
                                            }
                                            loginApi.auth(param).then(auth_res => {
                                                if (auth_res.code === 1) {
                                                    uni.hideLoading();
                                                    uni.showToast({
                                                        icon: 'none',
                                                        title: auth_res
                                                            .message,
                                                        mask: true,
                                                        duration: 1500
                                                    });
                                                    return;
                                                }
                                                that.openId = auth_res.data
                                                    .openId;
                                                let sessionKey = auth_res.data
                                                    .sessionKey;
                                                uni.setStorageSync('openId',
                                                    that.openId);
                                                uni.setStorageSync('sessionKey',
                                                    sessionKey);

                                                userApi.saveWechatUser(that
                                                    .openId).then(
                                                    saveWechatUser_res => {
                                                        if (saveWechatUser_res
                                                            .code === 1) {
                                                            uni
                                                                .hideLoading();
                                                            uni.showToast({
                                                                icon: 'none',
                                                                title: saveWechatUser_res
                                                                    .message,
                                                                mask: true,
                                                                duration: 1500
                                                            });
                                                            return;
                                                        }
                                                        uni.hideLoading();
                                                        uni.reLaunch({
                                                            url: '/pages/shop/shop?param=0'
                                                        })
                                                    })
                                            })
                                        },
                                        fail: () => {
                                            uni.hideLoading();
                                            uni.showToast({
                                                title: "微信登录授权失败",
                                                icon: "none"
                                            });
                                        }
                                    })
                                },
                                fail: () => {
                                    uni.hideLoading();
                                    uni.showToast({
                                        title: "微信登录授权失败",
                                        icon: "none"
                                    });
                                },

                            })
                        } else {
                            uni.hideLoading();
                            uni.showToast({
                                title: '请先安装微信或升级版本',
                                icon: "none"
                            });
                        }
                    },

                })
            },
            //获取主体信息
            getEnt() {
                uni.showLoading({
                  title: '数据加载中'
                });
                entApi.getEnt().then(res => {
                    if (res.code == 1) {
                        uni.showToast({
                            icon: 'none',
                            title: res.message
                        });
                        return;
                    }
                    uni.setStorageSync('ent', res.data);
                    this.ent = res.data;
                    this.$refs.tarbar.refresh();
                }).catch(() => {
                  console.log("catch")
                }).finally(() => {
                  console.log("finally")
                  this.loadComplete = true;
                  uni.hideLoading();
                })
            },
            checkToken(url, type) {
                if (!this.token) {
                    this.gotoLogin();
                } else {
                    if ("pushTab" === type) {
                        this.$Router.pushTab(url)
                    } else if ("replaceAll" === type) {
                        this.$Router.replaceAll(url)
                    } else {
                        this.$Router.push(url)
                    }
                }
            },
            gotoLogin() {
                uni.showModal({
                    title: '未登录',
                    content: '您未登录，需要登录后才能继续',
                    mask: true,
                    success: (res) => {
                        if (res.confirm) {
                            uni.navigateTo({
                                url: '../login/index'
                            });
                            /* if (this.forcedLogin) {
                            	uni.reLaunch({
                            		url: '../login/login'
                            	});
                            } else {
                            	uni.navigateTo({
                            		url: '../login/login'
                            	});
                            } */
                        }
                    }
                });
            }
        }
    }
</script>

<style lang="scss" scoped>
    button::after {
        border: none;
    }
</style>