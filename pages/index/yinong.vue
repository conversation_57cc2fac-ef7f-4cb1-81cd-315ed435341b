<template>
    <view>
        <div>
            <image :src="useImg" mode="widthFix" style="width: 100%;"></image>
        </div>
    </view>
</template>

<script>
    import api from '../../api/img.js';
    export default {
        data: function() {
            return {
                useImg: ''
            }

        },
        onLoad: function(option) {
            this.getImg()
        },
        methods: {
            getImg() {
                let param = {
                    key: "shouye.product.url"
                };
                api.getShouyeImgUrl(param).then(res => {
                    if (res.code != 0) {
                        uni.showToast({
                            title: "请求数据失败请重试"
                        })
                        return;
                    } else {
                        console.log(res.data.url)
                        this.useImg = res.data.url
                    }
                })
            }
        }
    }
</script>

<style>
</style>