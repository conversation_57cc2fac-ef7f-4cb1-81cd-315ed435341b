<template>
    <view>
    </view>
</template>

<script>
	import entApi from '../../api/ent.js';
    import loginApi from '../../api/login.js';
    var config = require('../../common/config.js')
    export default {
        data() {
            return {
				pathRoute:config.PATH_ROUTE,
            }
        },
        onLoad(param) {
            //首次进入取不到param，用此方式获取参数
            let pages = getCurrentPages();
            let page = pages[pages.length - 1];
            if(!page.options||!page.options.authCode){
                this.$Router.replaceAll("/pages/index/index")
                return
            }
            this.loginXiangYun(page.options.authCode,page.options.toPath);
        },
        onShow() {
        },
        methods: {
            loginXiangYun(authCode,toPath) {
                uni.showLoading({
                    title: '自动登录中',
                    mask: true,
                });
                let form = {
                    authCode: authCode,
                }
                loginApi.login(form).then(res => {
                    if (res.code === 1) {
                        uni.hideLoading();
                        uni.showToast({
                            icon: 'none',
                            title: res.message,
                            mask: true,
                            duration: 1500
                        });
                        this.$Router.replaceAll("/pages/index/index")
                        return;
                    }
                     uni.setStorageSync('token', res.data.token);
                     uni.setStorageSync('userInfo', res.data.userInfo);
                    //uni.setStorageSync('ent', res.data.ent);
                    uni.hideLoading();
                    uni.showToast({
                        icon: 'none',
                        title: '登录成功',
                        mask: true,
                        duration: 1500
                    });

					this.getEnt(toPath)
                }).catch(e=>{
                    console.log(e);
                    uni.hideLoading();
                    this.$Router.replaceAll("/pages/index/index");
                })
            },
			gotoPage(url, type) {
			
				if ("pushTab" === type) {
					this.$Router.pushTab(url)
				} else if ("replaceAll" === type) {
					this.$Router.replaceAll(url)
				} else {
					this.$Router.push(url)
				}
			    
			},
			//获取主体信息
			getEnt: function(toPath) {
			    entApi.getEnt().then(res => {
					console.log('res= ',res);
			        if (res.code == 1) {
			            // uni.showToast({
			            //     icon: 'none',
			            //     title: res.message
			            // });
						this.$Router.replaceAll("/pages/index/index");
			            return;
			        }
			        uni.setStorageSync('ent', res.data);
					if(toPath){
					  let path = decodeURIComponent(toPath)
					  if(path){
						  
						  let route = this.pathRoute[path];
						  console.log('path ',path);
						  console.log('route ',route);
						  if(route){
							  this.gotoPage(path,route)
						  }else{
							   this.$Router.replaceAll("/pages/index/index")
						  }
					  
					  }else{
					    this.$Router.replaceAll("/pages/index/index")
					  }
					
					}else{
					  this.$Router.replaceAll("/pages/index/index")
					}
			    })
			},
        }
    }
</script>

<style lang="scss" scoped>
    button::after {
        border: none;
    }
</style>
