<template>
	<view>
		<div class="whxx_tit">
			<ul>
				<li>
					<p>
						主体类型：{{info.businessType == '1' ? "养殖":"种植"}}
					</p>
				</li>
				<li>
					<p>
						主体性质：个人
					</p>
				</li>
			</ul>
		</div>

		<!--审核状态图片-->
		<image v-if="info.examineStatus == '0'" style="position:absolute;right:5%;top:2%;width:80px;height:60px;" :src="imgPath+'img/shz.png'" />
		<image v-if="info.examineStatus == '1'" style="position:absolute;right:5%;top:2%;width:80px;height:60px;" :src="imgPath+'img/shtg.png'" />
		<image v-if="info.examineStatus == '-1'" style="position:absolute;right:5%;top:2%;width:80px;height:60px;" :src="imgPath+'img/shsb.png'" />

		<div class="content_all">
			<div v-if="info.examineStatus == '-1'" class="whxx_all">
				<div class="whxx_left">
					驳回原因:
				</div>
				<div class="whxx_right">
					{{info.examineOpinion}}
				</div>
			</div>

			<div class="whxx_all">
				<div class="whxx_left">
					姓名:
				</div>
				<div class="whxx_right">
					{{info.name}}
				</div>
			</div>
			<view class="whxx_all">
				<view class="whxx_left">
					主体类别:
				</view>
				<view class="whxx_right">
					{{info.mainType|mainTypeFormat}}
				</view>
			</view>
			<div class="whxx_all">
				<div class="whxx_left">
					所在区域:
				</div>
				<div class="whxx_right">
					{{info.address}}
				</div>
			</div>

			<div class="whxx_all" style="border-bottom: none;">
				<div class="whxx_left">
					详细地址:
				</div>
				<div class="whxx_right">
					{{info.detail}}
				</div>
			</div>

			<div class="whxx_all_hist" style="border-bottom: none;">
				<view style="width:100%;">
					<map id="map1" ref="map1" style="width:100%;height:150px;" :latitude="latitude" :longitude="longitude" :markers="marker"></map>
				</view>
			</div>

			<div class="whxx_all">
				<div class="whxx_left">
					联系方式:
				</div>
				<div class="whxx_right">
					{{info.contactsPhone}}
				</div>
			</div>

			<div class="whxx_all">
				<div class="whxx_left">
					身份证号:
				</div>
				<div class="whxx_right">
					{{info.cardNo.substring(0,6) + "********" + info.cardNo.substring(14)}}
				</div>
			</div>

			<div class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
				<span class="title_tit" style="padding: 0px;">
					第二代身份证人像面照片:
				</span>
			</div>
			<div class="whxx_all_hist" style="margin:10px auto;">
				<div class="pboto_right" style="margin-bottom:10px;">
					<view style="width:200px;height: 105px;position: relative;">
						<image :src="idCardFrontUrl" style="height: 105px;width: 200px;">
					</view>
				</div>
			</div>

			<div class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
				<span class="title_tit" style="padding: 0px;">
					第二代身份证国徽面照片:
				</span>
			</div>
			<div class="whxx_all_hist" style="margin:10px auto;">
				<div class="pboto_right" style="margin-bottom:10px;">
					<view style="width:200px;height: 105px;position: relative;">
						<image :src="idCardBackUrl" style="height: 105px;width: 200px;">
					</view>
				</div>
			</div>

			<div class="whxx_all_hist" style="border-bottom: none;">
				<span class="title_tit" style="padding: 0px;">
					电子签名:
				</span>
			</div>
			<div class="whxx_all_hist" style="border-bottom: none;">
				<image :src="autographImg" style="width:100%;height:200px;" />
			</div>
		</div>
	</view>
</template>

<script>
	var config = require('../../common/config.js')
	import entChangeApi from '@/api/entChange.js';
	export default {
		data() {
			return {
				imgPath: config.IMG_PATH,
				info: {
					id: "",
					name: "",
					address: "",
					detail: "", //详细地址,
					contactsPhone: "", //联系方式
					cardNo: "",
					mainType: "",
				},
				latitude: 0,
				longitude: 0,
				marker: [{
					id: 0,
					latitude: 0, //纬度
					longitude: 0 //经度       
				}],
				idCardFrontShow: false,
				idCardFrontUrl: "",
				idCardBackShow: false,
				idCardBackUrl: "",
				autographImg: ""
			}
		},
		onLoad: function(options) {
			/**
			 * 个人主体包含签名数据通过路由传送对象转换json对象出错，所以暂时只能到当前页面去后台服务请求获取数据
			 */
			let that = this;
			
			let ent = uni.getStorageSync('ent');
			let param = {entId:ent.id};
			entChangeApi.getChange(param).then(res => {
				if(res.code==1){
					uni.showToast({
						icon: 'none',
						title: res.message
					});
					return;
				}
				that.info = res.data;
				that.latitude = that.info.lat;
				that.longitude = that.info.lng;
				that.marker[0].latitude = that.info.lat;
				that.marker[0].longitude = that.info.lng;
				that.autographImg = "data:image/png;base64," + that.info.autograph;
				if (that.info.fileList) {
					that.info.fileList.forEach((item) => {
						if (item.fileType == 'idCardFront') {
							that.idCardFrontShow = true;
							that.idCardFrontUrl = item.fileUrl;
						} else if (item.fileType == 'idCardBack') {
							that.idCardBackShow = true;
							that.idCardBackUrl = item.fileUrl;
						}
					})
				}
			})
			
		},
		methods: {
		},
		filters: {
			mainTypeFormat(value) {
				if (value === "1") {
					return `小农户`
				} else if (value === "5") {
					return `农产品生产企业`
				} else if (value === "10") {
					return `农民专业合作社`
				} else if (value === "15") {
					return `种养大户`
				} else if (value === "20") {
					return `家庭农场`
				}
				return ""
			},
		},
	}
</script>

<style>
</style>
