<template>
	<view >
	</view>
</template>

<script>
	var config = require('../../common/config.js')
	import entChangeApi from '@/api/entChange.js';
	export default {
		components: {},
		data() {
			return {
				
			}
		},
		onShow: function(option) {
			this.check();
		},
		methods: {
			check(){
				/**
				 * 通过entId获取变更申请未完成状态的数据
				 * 第一步：先判断examineStatus状态，等于99表示之前变更时需要补充基础信息采集数据，此时需要继续补充完整
				 * 第二步：根据entType展示不同的变更记录信息
				 */
				let ent = uni.getStorageSync('ent');
				let param = {entId:ent.id};
				entChangeApi.getChange(param).then(res => {
					if(res.code==1){
						uni.showToast({
							icon: 'none',
							title: res.message
						});
						return;
					}
					let entChange=res.data;
					//examineStatus:99 表示需要补充基础信息采集数据
					if (entChange.examineStatus == '99'){
						uni.showModal({
							icon: "none",
							content: '未在基础数据采集系统内查询到您的信息,请前往填写详细信息',
							mask: true,
							showCancel: false,
							success: function(res) {
								uni.redirectTo({
									url: "/pages/basic/basicEntForm?tableName=bas_ent_change",
								})
							}
						});
						return ;
					}
					
					uni.showModal({
						icon: "none",
						content: '主体信息变更审核中，前往主体信息查看审核状态？',
						mask: true,
						success: function(res) {
							if (res.confirm) {
								if (entChange.entType == '0' || entChange.entType == '2'){//查看变更企业信息
									uni.redirectTo({
										url: "/pages/entChange/enterpriseInfoShow?item="+JSON.stringify(entChange)
									})
									return ;
								}else if (entChange.entType == '1'){//查看变更个人信息
									uni.redirectTo({
										url: "/pages/entChange/personInfoShow?id="+entChange.id,
									})
									return ;
								}
							} else if (res.cancel) {
								uni.reLaunch({
									url: "/pages/index/index"
								})
								return ;
							}
						}
					});
					
				})
			},
		}
	}
</script>

<style>
</style>
