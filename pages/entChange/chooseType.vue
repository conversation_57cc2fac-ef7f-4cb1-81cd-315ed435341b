<template>
	<view v-show="show">
		<view class="whztxx_all">
			<div class="whztxx_cont" @click="setBusinessType(1)">
			        <div class="whztxx_img">
			            <image :src="imgPath+'img/img23.png'" alt="">  
			        </div>
			        <div class="whztxx_tit">
			            <p>养殖主体</p>
			            <uni-icons type="arrowright" size="25" style="color:grey;float: right;"></uni-icons>
			        </div>
			</div>
			<div class="whztxx_cont" @click="setBusinessType(0)">
			        <div class="whztxx_img">
			            <image :src="imgPath+'img/img24.png'" alt="">
			        </div>
			        <div class="whztxx_tit">
			            <p>种植主体</p>
			            <uni-icons type="arrowright" size="25" style="color:grey;float: right;"></uni-icons>
			        </div>
			</div>
			<div class="whztxx_title">
			        <span style="color: #ff0000">*</span>在正式维护主体信息前，请先选择主体类型
			</div>
			<div class="whztxx_title">
			        <span style="color: #ff0000">*</span>请注意主体信息维护保存后，主体类型将不可更改
			</div>
		</view>
	</view>
</template>

<script>
	import uniIcons from "@/components/uni-icons/uni-icons.vue"
	var config = require('../../common/config.js')
	export default {
		components: {uniIcons},
		data() {
			return {
				imgPath:config.IMG_PATH,
				show:false,
				token:uni.getStorageSync("token")
			}
		},
		onShow: function(option) {
			let that = this;
			if(!that.token){
				that.show = true;
				return ;
			}
			that.show = true;
		},
		methods: {
			setBusinessType: function(value) {
				if(!this.token){
					this.gotoLogin();
				}else{
					if(1===value){
						uni.navigateTo({
							url: "/pages/entChange/chooseFarm?businessType="+value
						})
					}else{
						uni.navigateTo({
							url: "/pages/entChange/chooseNature?businessType="+value
						})
					}
					
				}
			},
			gotoLogin() {
				uni.showModal({
					title: '未登录',
					content: '您未登录，需要登录后才能继续',
					mask: true,
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/login/index'
							});
						}
					}
				});
			}
		}
	}
</script>

<style>
</style>
