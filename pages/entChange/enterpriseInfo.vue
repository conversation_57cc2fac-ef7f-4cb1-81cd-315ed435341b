<template>
	<view>
		<form @submit="formSubmit">
			<div class="whxx_tit">
				<ul>
					<li>
						<p>
							主体类型：{{businessTypeShow}}
						</p>
					</li>
					<li>
						<p>
							主体性质：企业
						</p>
					</li>
				</ul>
			</div>
			<div class="content_all">
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>企业名称:
					</div>
					<div class="whxx_right" style="height: 21px;">
						<input class="uni-input" placeholder="通过识别工商营业执照获取" id="name" name="name" :value="name" @input="nameChange" maxlength="30" :disabled="licensePicDisabled"/>
					</div>
				</div>
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>统一社会信用代码:
					</div>
					<div class="whxx_right"  style="height: 21px;">
						<input class="uni-input" maxlength="20" placeholder="通过识别工商营业执照获取" id="socialCode" name="socialCode" :value="socialCode"
						 @input="socialCodeChange" :disabled="licensePicDisabled"/>
					</div>
				</div>
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>企业法人:
					</div>
					<div class="whxx_right"  style="height: 21px;">
						<input class="uni-input" maxlength="20" placeholder="通过识别工商营业执照获取" id="legalPerson" name="legalPerson" :value="legalPerson"
						 @input="legalPersonChange" :disabled="licensePicDisabled"/>
					</div>
				</div>
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>主体类别:
					</div>
					<div class="whxx_right"  style="height: 21px;">
						<!--下拉选-->
						<view class="uni-list-cell-db">
							<picker name="mainType" @change="bindMainTypePickerChange" :value="mainTypeIndex" :range="mainTypeArray" :range-key="'value'">
								<view class="uni-input">{{mainTypeArray[mainTypeIndex]?mainTypeArray[mainTypeIndex].value:'请选择主体类别'}}</view>
							</picker>
						</view>
					</div>
				</div>
				
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>企业联系人:
					</div>
					<div class="whxx_right"  style="height: 21px;">
						<input class="uni-input" maxlength="20" placeholder="请输入企业联系人" id="contacts" name="contacts" />
					</div>
				</div>
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>联系电话:
					</div>
					<div class="whxx_right"  style="height: 21px;">
						<input class="uni-input" maxlength="20" placeholder="请输入联系电话" id="contactsPhone" name="contactsPhone" :value="contactsPhone" />
					</div>
				</div>
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>所在区域:
					</div>
					<div class="whxx_right"  style="height: 21px;">
						<view class="uni-list-cell-db">
							<picker mode="multiSelector" @columnchange="bindMultiPickerColumnChange" :value="multiIndex" :range="multiArray">
								<view class="uni-input">{{multiArray[0][multiIndex[0]]}}{{multiArray[1][multiIndex[1]]}}{{multiArray[2][multiIndex[2]]}}</view>
							</picker>
						</view>
					</div>
				</div>
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>详细地址:
					</div>
					<div class="whxx_right"  style="height: 21px;">
						<input class="uni-input" placeholder="请输入或点击右侧图标" id="detail" :value="detail" name="detail" @input="inputChangeDetail"
						 maxlength="100" style="width: 80%;float: left;" />
						<view style="float: left;width: 20%;" @click="clickMap">
							<image :src="imgPath+'img/location.png'" alt="" style="width: 20px;height: 20px;" />
						</view>
					</div>
				</div>
				<!-- <div class="whxx_all" style="border-bottom: none;">
					<view style="width:100%;">
						<map id="map1" ref="map1" style="width: 100%;height:200px;" :latitude="latitude" :longitude="longitude" :markers="marker" @tap="tapMap"></map>
					</view>
				</div> -->
				<div class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
					<span class="title_tit" style="padding: 0;"><span style="color:#ff0000;">*</span>请上传工商营业执照正面照片:</span>
				</div>
				<div class="whxx_all_hist" style="margin:10px auto;height:120px;">
					<div class="pboto_right" style="margin-bottom:10px;">
						<view style="width:60px;height: 60px;position: relative;">
							<view class="row-img" style="width: 200px;height: 105px;">
								<ck-upload-img @removeImg="removeCardNo" @returnImgUrl="getCardNoImgUrl" :initImgList="urlsCardNo" :selectNum=1
								 :token="upToken" :tableName="tableName"  :customIcon="'cardNo'" :iconWidth="'200px'" :iconHeight="'105px'" ></ck-upload-img>
							</view>
						</view>
					</div>
				</div>
				<div class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
					<span class="title_tit" style="padding: 0;">请设置企业公章:</span>
				</div>
				<div class="whxx_all_hist" style="margin:10px auto;">
					<div class="pboto_right" style="margin-bottom:10px;">
						<view style="width:60px;height: 60px;position: relative;">
							<view class="row-img">
								<ck-upload-img @removeImg="removeSeal" @returnImgUrl="getSealImgUrl" :initImgList="urlsSeal" :selectNum=1
								 :token="upToken" :tableName="tableName"></ck-upload-img>
							</view>
						</view>
					</div>
				</div>
				<div class="whxx_all_hist" style="border-bottom: none;">
					<span class="title_tit" style="padding: 0;">企业简介:</span>
				</div>
				<div class="whxx_all_hist">
					<textarea style="width: 96%;margin: 10px auto;border: 1px solid #e0e0e0;" placeholder="请输入企业简介" name="companyIntroduction"
					 maxlength="255" />
					</div>
				<div class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
					<span class="title_tit" style="padding: 0;">企业照片(最多上传5张):</span>
				</div>
				<div class="whxx_all_hist" style="margin:10px auto;">
					<div class="pboto_right" style="margin-bottom:10px;width: 100%;">
						<view style="width:100%;">
							<view class="row-img">
								<ck-upload-img @removeImg="removeEntOther" @returnImgUrl="getEntOtherImgUrl" :initImgList="urlsEntOther" :selectNum=5 :token="upToken" :tableName="tableName"></ck-upload-img>
							</view>
						</view>
					</div>
				</div>
				<div class="whxx_all_hist" style="border-bottom: none;">
					<span class="title_tit" style="padding: 0;">荣誉简介:</span>
				</div>
				<div class="whxx_all_hist">
					<textarea style="width: 96%;margin: 10px auto;border: 1px solid #e0e0e0;" placeholder="请输入荣誉简介" name="entHonor" maxlength="255"/>
				</div>
				<div class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
					<span class="title_tit" style="padding: 0;">荣誉照片(最多上传5张):</span> 
				</div>
				<div class="whxx_all_hist" style="margin:10px auto;">
					<div class="pboto_right" style="margin-bottom:10px;width: 100%;">
						<view style="width:100%;">
							<view class="row-img">
								<ck-upload-img @removeImg="removeHonor" @returnImgUrl="getHonorImgUrl" :initImgList="urlsHonor" :selectNum=5 :token="upToken" :tableName="tableName"></ck-upload-img>
							</view>
						</view>
					</div>
				</div>
				<div class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
					<span class="title_tit" style="padding: 0;">企业视频:</span> 
				</div>
				<div class="whxx_all_hist" style="margin:10px auto;">
					<div class="pboto_right" style="margin-bottom:10px;width: 100%;">
						<view style="width:100%;">
							<view class="row-img">
								<ck-upload-video ref="videoRef" @remove="removeVideo" @returnUrl="getVideoUrl" :token="upToken" :tableName="tableName" ></ck-upload-video>
							</view>
						</view>
					</div>
				</div>
				<div>
					<button form-type="submit" type="primary" style="width:100px;height:35px;line-height:35px;font-size:14px;color: #fff;background-color: #02994f;" :disabled="submitDisabled">提交</button>
				</div>
				<div class="footer_tit">
					<view>提交后由当地业务主管部门进行审核</view> 
				</div>
			</div>
		</form>
	</view>
</template>

<script>
	import entApi from '@/api/ent.js';
	import entChangeApi from '@/api/entChange.js';
	import commonApi from '@/api/common.js';
	import ckUploadImg from '@/components/ck-uploadImg/ck-uploadImg.vue';
	import ckUploadVideo from '@/components/ck-uploadImg/ck-uploadVideo.vue';
	var  graceChecker = require("../../common/graceui-dataChecker/graceChecker.js");
	var config = require('../../common/config.js')
	const cardScanEnabled = config.CARD_SCAN_ENABLED; //证件识别开关
	export default {
		components:{
			ckUploadImg,
			ckUploadVideo,
		},
    computed: {
      businessTypeShow() {
        if (this.businessType === '0') {
          return "种植"
        }
        if (this.businessType === '1') {
          return "养殖"
        }
        if (this.businessType === '2') {
          return "检测"
        }
        return ""
      }
    },
		data() {
			return {
				imgPath:config.IMG_PATH,
				province:22,
				city:'',
				county:'',
				businessType:"",
				farmType:"",
				detail:'',
				contactsPhone:'',
				socialCode:'',
				name:'',
				legalPerson:'',
				multiIndex: [0, 0, 0],
				multiArray: [[""],[""],[""]],
				multiArrayCode: [[],[],[]],
				multiList : [],
				secondIds:[],
				latitude: 43.886841,
				longitude: 125.3245,
				firstChooseLocation:true,
				cardNoFileList:[],
				seelFileList:[],
				entOtherFileList:[],
				entHonorFileList:[],
				fileList:[], 
				isRotate: false,//七牛
				upToken:'',//七牛token
				urlsCardNo:[],//工商营业执照路径集合
				urlsSeal:[],//企业公章路径集合
				urlsEntOther:[],//企业其他照片路径集合
				urlsHonor:[],//企业荣誉照片路径集合
				videoAttachment:{
					fileType: '',
					tableName: '',
					filePath: '',
				},//视频介绍对象
				submitDisabled:false,//防止重复提交
				tableName:"bas_ent_change",
				licenseDistinguish:true,//工商营业执照识别结果,
				mainTypeArray: [],
				mainTypeIndex: '',
				mainType: '',
				licensePicDisabled:true,//禁用状态,
			}
		},
		onLoad: function(option) {
			this.businessType = option["businessType"];
			this.farmType = option["farmType"];
			
			let userInfoStorage = uni.getStorageSync('userInfo');
			this.contactsPhone = userInfoStorage.phoneNumber;
			let that = this;
			commonApi.get7nToken().then(res => {
				if(res.code == 0){
					that.upToken = res.data;
				}
			});
			//获取所属区域
			this.getArea();
		},
		onShow:function(){
			this.findMainTypeList();
		},
		methods: {
			bindMultiPickerColumnChange: function(e) {	
				this.multiIndex[e.detail.column] = e.detail.value
				if(e.detail.column == 1){					
					let chooseSecondId = this.secondIds[parseInt(e.detail.value)];
					this.multiArray[2] = [];
					this.multiArrayCode[2] = [];
					let third = [];
					let thirdCode = [];
					this.multiList.forEach((item) => {
						if(item.parentId == chooseSecondId){
							third.push(item.name)
							thirdCode.push(item.code)	
						}
					})
					this.multiArray[2] = third;
					this.multiArrayCode[2] = thirdCode;
					this.multiIndex[2]=0;
				}
				this.$forceUpdate()
			},
			inputChangeDetail(e){
			    this.detail = e.detail.value
			},
			formSubmit: function(e) {
				var rule = [
				    { name: "name", checkType: "string", checkRule: "1,30", errorMsg: "企业名称应为1-30位" },
					{ name: "mainType", checkType: "string", checkRule: "1,2", errorMsg: "请选择主体类别" },
					{ name: "socialCode", checkType: "socialNum", checkRule: "1,18", errorMsg: "统一社会信用代码填写不正确" },
					{ name: "legalPerson", checkType: "string", checkRule: "1,20", errorMsg: "企业法人为1-20位" },
					{ name: "contacts", checkType: "string", checkRule: "1,20", errorMsg: "企业联系人为1-20位" },
					{ name: "contactsPhone", checkType: "phoneno", checkRule: "", errorMsg: "请填写正确的联系电话" },
					{ name: "detail", checkType: "string", checkRule: "1,100", errorMsg: "请填写详细地址" },
				];
				let that = this;
				let ent = e.detail.value;
				ent.entId = uni.getStorageSync('ent').id;
				ent.detail = that.detail;
				ent.lng = that.longitude;
				ent.lat = that.latitude;
				ent.businessType = that.businessType;
				ent.farmType = that.farmType;
				
				ent.entType = that.businessType === '2'? 2: 0;
				ent.province = that.multiArrayCode[0][that.multiIndex[0]];
				ent.city = that.multiArrayCode[1][that.multiIndex[1]];
				ent.county = that.multiArrayCode[2][that.multiIndex[2]];
				ent.address = that.multiArray[0][that.multiIndex[0]]+that.multiArray[1][that.multiIndex[1]]+that.multiArray[2][that.multiIndex[2]];
				ent.examineStatus = 0;
				ent.mainType=that.mainType;
				var checkRes = graceChecker.check(ent, rule);
				if(!checkRes){
					uni.showToast({ title: graceChecker.error, icon: "none" });
					return;
				}
				if(null==that.cardNoFileList || that.cardNoFileList.length==0){
					uni.showToast({ title: '请上传工商营业执照正面照片', icon: "none" });
					return;
				}
				if(!that.licenseDistinguish){
					uni.showToast({ title: '工商营业执照照片不符合要求', icon: "none" });
					return;
				}
				/* if(null==that.seelFileList || that.seelFileList.length==0){
					uni.showToast({ title: '请上传企业公章照片', icon: "none" });
					return;
				} */
				if(ent.detail.indexOf(ent.address)<0){
					uni.showModal({
						title: '提示',
						content: '详细地址不在所选区域之内，是否继续操作？',
						showCancel: true,
						success: (res) => {
							if (res.confirm) {
								let param = {'socialCode':ent.socialCode,'id':ent.entId};
								entApi.checkSocialCode(param).then(res => {
									if(res.code == 0){
										if(res.data){
											that.fileList = that.cardNoFileList;
											if(that.seelFileList!=null && that.seelFileList.length>0){
												for (let i = 0; i < that.seelFileList.length; i++) {
													 that.fileList.push(that.seelFileList[i]);
												}
											}
											if(that.entOtherFileList!=null && that.entOtherFileList.length>0){
												for (let i = 0; i < that.entOtherFileList.length; i++) {
													 that.fileList.push(that.entOtherFileList[i]);
												}
											}
											if(that.entHonorFileList!=null && that.entHonorFileList.length>0){
												for (let i = 0; i < that.entHonorFileList.length; i++) {
													 that.fileList.push(that.entHonorFileList[i]);
												}
											}
											if(that.videoAttachment.filePath!=""){
												that.fileList.push(that.videoAttachment);
											}
											ent.fileList = that.fileList;
											that.submitDisabled = true;
											uni.showLoading({
												title: '信息提交中',
												mask:true,
											});
											entChangeApi.saveEnt(ent).then(res => {
												if(res.code == 1){
													uni.hideLoading();
													uni.showToast({
														icon: 'none',
														title: res.message,
														mask: true,
														duration: 3000
													});
													return;
												}
												uni.setStorageSync('entChange', res.data);
												if(res.data.basicFlag==='1'){
													uni.hideLoading();
													uni.showModal({
														content: '未在基础数据采集系统内查询到您的信息,请前往填写详细信息',
														mask: true,
														showCancel:false,
														success: (res) => {
															uni.reLaunch({
																url: "/pages/basic/basicEntForm?tableName=bas_ent_change"
															})
														}
													});
												}else{
													uni.showToast({
														//icon:'none',
														title: '信息提交成功',
														duration: 2000
													});
													setTimeout(()=>{
														uni.hideLoading();
														uni.reLaunch({
															url: "/pages/index/index"
														})
													},2000);
												}
											})
										}else{
											that.submitDisabled = false;
											uni.hideLoading();
											uni.showToast({
												icon:'none',
											    title: '统一社会信用代码已存在',
											    duration: 2000
											});
										}
									}				
								})
							}
						},
					})
				}else{
					let param = {'socialCode':ent.socialCode,'id':ent.entId};
					entApi.checkSocialCode(param).then(res => {
						if(res.code == 0){
							if(res.data){
								that.fileList = that.cardNoFileList;
								if(that.seelFileList!=null && that.seelFileList.length>0){
									for (let i = 0; i < that.seelFileList.length; i++) {
										 that.fileList.push(that.seelFileList[i]);
									}
								}
								if(that.entOtherFileList!=null && that.entOtherFileList.length>0){
									for (let i = 0; i < that.entOtherFileList.length; i++) {
										 that.fileList.push(that.entOtherFileList[i]);
									}
								}
								if(that.entHonorFileList!=null && that.entHonorFileList.length>0){
									for (let i = 0; i < that.entHonorFileList.length; i++) {
										 that.fileList.push(that.entHonorFileList[i]);
									}
								}
								if(that.videoAttachment.filePath!=""){
									that.fileList.push(that.videoAttachment);
								}
								ent.fileList = that.fileList;
								that.submitDisabled = true;
								uni.showLoading({
									title: '信息提交中',
									mask:true,
								});
								entChangeApi.saveEnt(ent).then(res => {
									if(res.code == 0){
										uni.setStorageSync('entChange', res.data);
										if(res.data.basicFlag==='1'){
											uni.hideLoading();
											uni.showModal({
												content: '未在基础数据采集系统内查询到您的信息,请前往填写详细信息',
												mask: true,
												showCancel:false,
												success: (res) => {
													uni.reLaunch({
														url: "/pages/basic/basicEntForm?tableName=bas_ent_change"
													})
												}
											});
										}else{
											uni.showToast({
												//icon:'none',
												title: '信息提交成功',
												duration: 2000
											});
											setTimeout(()=>{
												uni.hideLoading();
												uni.reLaunch({
													url: "/pages/index/index"
												})
											},2000);
										}
									}
								})
							}else{
								that.submitDisabled = false;
								uni.hideLoading();
								uni.showToast({
									icon:'none',
									title: '统一社会信用代码已存在',
									duration: 2000
								});
							}
						}				
					})
				}
			},
			//删除工商营业执照照片
			removeCardNo(index){
				this.cardNoFileList = [];
			},
			//返回工商营业执照照片
			getCardNoImgUrl(urls){
				let that = this;
				this.cardNoFileList = [];
				urls.forEach((url) => {
					let item = {};
					item["fileType"] = 'licensePic';
					item["tableName"] = 'bas_ent_change';
					item["filePath"] = url;
					this.cardNoFileList.push(item);
					var attachment = {"filePath" :url};
					if(!cardScanEnabled){
						return ;
					}
					uni.showLoading({
						title: '图片文字识别中',
						mask:true,
					});
					
					var url = url.substring(url.lastIndexOf("/tmp")+1,url.length);
					let base64Code = "";
					console.log("返回微信图片=="+"wxfile://"+url);
					// 转base64
					wx.getFileSystemManager().readFile({
						filePath: "wxfile://"+url,
						encoding: "base64",
						success: function(res) {
							base64Code = res.data
							var json = {
								"image":base64Code,
								"configure":"{\'side\':\'face\'}"
							}
							commonApi.ocrLicensePicJs(json).then(res => {
								console.log("res00000==="+JSON.stringify(res))
								var result = res;
								if(null!=result){
									console.log("result.socialCode=="+result.reg_num)
									that.socialCode = result.reg_num;
									that.name = result.name;
									that.legalPerson = result.person;
									that.licensePicDisabled = false;
									that.licenseDistinguish = true;
								}else{
									uni.showToast({ title: '工商营业执照正面照片不符合要求', icon: "none" });
									that.licensePicDisabled = true;
									that.licenseDistinguish = false;
								}
								uni.hideLoading();
							})
						}
					})
					/* api.ocrLicensePic(attachment).then(res => {
						if(null!=res.data){
							this.socialCode = res.data.socialCode;
							this.name = res.data.name;
							this.legalPerson = res.data.legalPerson;
							this.licensePicDisabled = false;
							this.licenseDistinguish = true;
						}else{
							uni.showToast({ title: '工商营业执照正面照片不符合要求', icon: "none" });
							this.licensePicDisabled = true;
							this.licenseDistinguish = false;
						}
						uni.hideLoading();
					}) */
				})
			},
			//删除企业公章照片
			removeSeal(index){
				this.seelFileList = [];
			},
			//返回企业公章照片
			getSealImgUrl(urls){
				this.seelFileList = [];
				urls.forEach((url) => {
					let item = {};
					item["fileType"] = 'sealPic';
					item["tableName"] = 'bas_ent_change';
					item["filePath"] = url;
					this.seelFileList.push(item);
				})
			},
			//删除企业其他照片
			removeEntOther(index){
				this.entOtherFileList = [];
			},
			//返回企业其他照片
			getEntOtherImgUrl(urls){
				this.entOtherFileList = [];
				urls.forEach((url) => {
					let item = {};
					item["fileType"] = 'entPic';
					item["tableName"] = 'bas_ent_change';
					item["filePath"] = url;
					this.entOtherFileList.push(item);
				})
			},
			//删除企业荣誉照片
			removeHonor(index){
				this.entHonorFileList = [];
			},
			//返回企业荣誉照片
			getHonorImgUrl(urls){
				this.entHonorFileList = [];
				urls.forEach((url) => {
					let item = {};
					item["fileType"] = 'entHonorPic';
					item["tableName"] = 'bas_ent_change';
					item["filePath"] = url;
					this.entHonorFileList.push(item);
				})
			},
			//删除视频介绍
			removeVideo(){
				this.videoAttachment.fileType="";
				this.videoAttachment.tableName="";
				this.videoAttachment.filePath="";
			},
			//返回视频介绍url
			getVideoUrl(url){
				this.videoAttachment.fileType="video";
				this.videoAttachment.tableName=this.tableName;
				this.videoAttachment.filePath=url;
			},
			clickMap(){
				let that = this;
				if(that.firstChooseLocation){
					uni.chooseLocation({
						success: function (res) {
							that.detail = res.address;
							that.latitude = res.latitude;
							that.longitude = res.longitude;
							that.firstChooseLocation = false;
						}
					});
				}else{
					uni.chooseLocation({
						latitude:that.latitude,
						longitude:that.longitude,
						success: function (res) {
							that.detail = res.address;
							that.latitude = res.latitude;
							that.longitude = res.longitude;
						}
					});
				}
			},
			nameChange(event){
				this.name = event.target.value;
			},
			socialCodeChange(event){
				this.socialCode = event.target.value;
			},
			legalPersonChange(event){
				this.legalPerson = event.target.value;
			},
			//获取所属区域
			getArea(){
				entApi.getArea(4).then(res => {
					if(res.code == 0){
						this.multiList = res.data;
						let first = [];
						let second = [];
						let third = [];
						let firstCode = [];
						let secondCode = [];
						let thirdCode = [];
						res.data.forEach((item) => {
							if(item.type == '2'){
								first.push(item.name)
								firstCode.push(item.code)
							}else if(item.type == '3'){
								second.push(item.name)
								secondCode.push(item.code)
								this.secondIds.push(item.id);
							}
						})
						if(this.secondIds.length != 0){
							res.data.forEach((item) => {
								if(item.parentId == this.secondIds[0]){
									third.push(item.name)
									thirdCode.push(item.code)
								}
							})
						}
						this.multiArray = [];
						this.multiArrayCode = [];
						this.multiArray.push(first);
						this.multiArray.push(second);
						this.multiArray.push(third);
						this.multiArrayCode.push(firstCode);
						this.multiArrayCode.push(secondCode);
						this.multiArrayCode.push(thirdCode);
					}
				});
			},
			findMainTypeList() {
				let that = this;
				entApi.findMainTypeEntList().then(res => {
					if (res.code == 1) {
						uni.showToast({
							icon: 'none',
							title: res.message
						});
						return;
					}
					that.mainTypeArray = res.data;
				})
			},
			bindMainTypePickerChange(e) {
				this.mainTypeIndex = e.target.value
				var selected = this.mainTypeArray[this.mainTypeIndex]           //获取选中的数组
				this.mainType = selected.key          //选中的code
			},
		}
	}
</script>

<style>
	.view-table{
		width:100%;margin-top:20px;margin-left:5%;margin-right:5%;
	}
	.view-tr{
		display: flex;flex-direction:row;height:30px;
	}
	.view-lable{
		width:45%;display: flex;flex-direction:row;
	}
	.view-font{
		font-size:16px;color:#01BE6E;height:30px;line-height:30px;font-size:14px;
	}
	.view-input{
		width:50%;display: flex;flex-direction:row;
	}
	.view-line{
		height:0.5px;border:0.5px solid grey;margin-left:5%;width:90%;
	}
</style>
