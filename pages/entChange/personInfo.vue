<template>
	<view>
		<view v-show="!isShow">
			<div class="whxx_tit">
				<ul>
					<li>
						<p>
							主体类型：{{info.businessType == '1' ? "养殖":"种植"}}
						</p>
					</li>
					<li>
						<p>
							主体性质：个人
						</p>
					</li>
				</ul>
			</div>
			<form @submit="formSubmit">
				<div class="content_all">
					<div class="whxx_all">
						<div class="whxx_left">
							<span>*</span>姓名:
						</div>
						<div class="whxx_right" style="height: 21px;">
							<input class="uni-input" maxlength="20" placeholder="通过识别身份证获取"  :value="info.name" name="name" @input="nameChange" :disabled="picDisabled"/>
						</div>
					</div>
					<div class="whxx_all">
						<div class="whxx_left">
							<span>*</span>主体类别:
						</div>
						<div class="whxx_right" style="height: 21px;">
							<!--下拉选-->
							<view class="uni-list-cell-db">
								<picker name="mainType" @change="bindMainTypePickerChange" :value="mainTypeIndex" :range="mainTypeArray" :range-key="'value'">
									<view class="uni-input">{{mainTypeArray[mainTypeIndex]?mainTypeArray[mainTypeIndex].value:'请选择主体类别'}}</view>
								</picker>
							</view>
						</div>
					</div>
					<div class="whxx_all">
						<div class="whxx_left">
							<span>*</span>所在区域:
						</div>
						<div class="whxx_right" style="height: 21px;">
							<!--下拉选-->
							<view class="uni-list-cell-db">
								<picker mode="multiSelector" @columnchange="bindMultiPickerColumnChange" :value="multiIndex" :range="multiArray">
									<view class="uni-input">{{multiArray[0][multiIndex[0]]}}{{multiArray[1][multiIndex[1]]}}{{multiArray[2][multiIndex[2]]}}</view>
								</picker>
							</view>
						</div>
					</div>
					<div class="whxx_all">
						<div class="whxx_left">
							<span>*</span>详细地址:
						</div>
						<div class="whxx_right" style="height: 21px;">
							<input class="uni-input" placeholder="请输入或点击右侧图标"  :value="info.detail" name="detail" maxlength="100" style="width: 80%;float: left;" id="detail"/>
							<view style="float: left;width: 20%;" @click="clickMap"><image :src="imgPath+'img/location.png'" alt="" style="width: 20px;height: 20px;"/></view>	
						</div>
					</div>
					<!--
					<div class="whxx_all" style="border-bottom: none;">
						<view style="width:100%;">
							<map id="map1" ref="map1" style="width:100%;height:200px;" :latitude="latitude" :longitude="longitude" :markers="marker" @tap="tapMap"></map>
						</view>
					</div>
					-->
					<div class="whxx_all">
						<div class="whxx_left">
							<span>*</span>联系电话:
						</div>
						<div class="whxx_right" style="height: 21px;">
							<input class="uni-input" maxlength="20" placeholder="请输入联系电话" :value="info.contactsPhone" name="contactsPhone"/>
						</div>
					</div>
					<div class="whxx_all">
						<div class="whxx_left">
							<span>*</span>身份证号:
						</div>
						<div class="whxx_right" style="height: 21px;">
							<input class="uni-input" maxlength="20" placeholder="通过识别身份证获取" :value="info.cardNo" name="cardNo" @input="cardNoChange" :disabled="picDisabled"/>
						</div>
					</div>

					
					
					<div class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
						<span style="color:#ff0000;">*</span>请上传第二代身份证人像面照片:
					</div>
					<div class="whxx_all_hist" style="margin:10px auto;">
						<div class="pboto_right" style="margin-bottom:10px;">
							<view style="width:200px;height: 105px;position: relative;">
								<view class="row-img">
									<ck-upload-img  @removeImg="removeIdCardFront"   @returnImgUrl="getIdCardFrontImgUrl" :initImgList="urls" :selectNum=1 :token="upToken" :tableName="tableName" :customIcon="'idCardFront'"></ck-upload-img>
								</view>
							</view>
						</div>
					</div>
					
					<div class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
						<span style="color:#ff0000;">*</span>请上传第二代身份证国徽面照片:
					</div>
					<div class="whxx_all_hist" style="margin:10px auto;">
						<div class="pboto_right" style="margin-bottom:10px;">
							<view style="width:200px;height: 105px;position: relative;">
								<view class="row-img">
									<ck-upload-img @removeImg="removeIdCardBack" @returnImgUrl="getIdCardBackImgUrl" :initImgList="urls2" :selectNum=1 :token="upToken" :tableName="tableName" :customIcon="'idCardBack'"></ck-upload-img>
								</view>
							</view>
						</div>
					</div>
					

					<div class="whxx_all_hist" style="border-bottom: none;">
						<span style="color: #ff0000;">*</span>请设置电子签名:
					</div>
					<div class="whxx_all_hist" style="border-bottom: none;">
						<image :src="autographImg == ''? imgPath+'img/qm-v2.png': autographImg" style="width:100%;height:200px;" @tap="doss"/>
					</div>
					<div>
						<button form-type="submit" type="primary" style="width:100px;height:35px;line-height:35px;font-size:14px;background: #2da871;color: #fff;background-color: #02994f;" :disabled="submitDisabled">提交</button>
					</div>
					<div class="footer_tit">
						<view>提交后由当地业务主管部门进行审核</view>
					</div>
				</div>
			</form>
		</view>
			
	
		<view v-show="isShow" style="height:100vh;overflow:hidden;">
			<catSignature canvasId="canvas1"  @close="close" @save="save" :visible="isShow" />
		</view>
	</view>
	
</template>

<script>
	import catSignature from "@/components/cat-signature/cat-signature-cross.vue"
	import entApi from '@/api/ent.js';
	import entChangeApi from '@/api/entChange.js';
	import commonApi from '@/api/common.js';
	var  graceChecker = require("../../common/graceui-dataChecker/graceChecker.js");
	import ckUploadImg from '@/components/ck-uploadImg/ck-uploadImg-single.vue';
	var config = require('../../common/config.js')
	const cardScanEnabled = config.CARD_SCAN_ENABLED; //证件识别开关
	export default {
		components:{catSignature,ckUploadImg},
		data() {
			return {
				imgPath:config.IMG_PATH,
				info:{
					businessType:"",
					farmType:"",
					name:"",//姓名
					detail:"",//详细地址,
					contactsPhone:"",//联系方式
					cardNo:"",
					idCardFrontFileList:[],
					idCardBackFileList:[],
					autograph:""
				},
				multiIndex: [0, 0, 0],
				multiArray: [
					[""],
					[""],
					[""]
				],
				multiList : [],
				secondIds:[],
				latitude: 43.886841,
				longitude: 125.3245,
				firstChooseLocation:true,
				marker: [{
				   id:0,
				   latitude: 0,//纬度
				   longitude: 0//经度       
				}],
				scale:16,//地图缩放程度
				center: {lng: 0, lat: 0},
				zoom: 3,
				idCardFrontShow:false,
				idCardFrontUrl:"",
				idCardBackShow:false,
				idCardBackUrl:"",
				autographImg:'',
				isShow:false,
				isRotate: false,//七牛
				upToken:'',//七牛token
				urls:[],//七牛
				urls2:[],//七牛,
				submitDisabled:false,//防止重复提交,
				tableName:"bas_ent_change",
				faceDistinguish:true,//身份证国徽面识别结果
				backDistinguish:true,//身份证人像面识别结果
				mainTypeArray: [],
				mainTypeIndex: '',
				mainType: '',
				picDisabled:true,//禁用状态,
			}
		},
		onLoad: function(option) {
			let userInfoStorage = uni.getStorageSync('userInfo');
			this.info.contactsPhone = userInfoStorage.phoneNumber;
			this.info.businessType = option["businessType"];
			this.info.farmType = option["farmType"];
			let that = this;
			entApi.getArea(4).then(res => {
				if(res.code == 0){
					this.multiList = res.data;
					let first = [];
					let second = [];
					let third = [];
					res.data.forEach((item) => {
						if(item.type == '2'){
							first.push(item.name)
						}else if(item.type == '3'){
							second.push(item.name)
							this.secondIds.push(item.id);
						}
					})
					if(this.secondIds.length != 0){
						res.data.forEach((item) => {
							if(item.parentId == this.secondIds[0]){
								third.push(item.name)
							}
						})
					}
					this.multiArray = [];
					this.multiArray.push(first);
					this.multiArray.push(second);
					this.multiArray.push(third);
					console.log(this.multiArray)
				}
			});
		
			commonApi.get7nToken().then(res => {
				if(res.code == 0){
					that.upToken = res.data;
				}
			});	
		},onShow:function(){
			this.findMainTypeList();
		}
		,methods: {
			onKeyInput4Name: function(event) {
				this.info.name = event.target.value;
			},
			bindMultiPickerColumnChange: function(e) {
				this.multiIndex[e.detail.column] = e.detail.value
				if(e.detail.column == 1){
					let chooseSecondId = this.secondIds[parseInt(e.detail.value)];
					console.log(chooseSecondId);
					this.multiArray[2] = [];
					let third = [];
					this.multiList.forEach((item) => {
						if(item.parentId == chooseSecondId){
							third.push(item.name)
						}
					})
					this.multiArray[2] = third;
					this.multiIndex[2]=0;
				}
				this.$forceUpdate()
			},
			tapMap(e) {
				var that = this;
				uni.request({
					url: "https://api.map.baidu.com/reverse_geocoding/v3/?ak=NEXIhGPsRgKDcGa1Ox2OWj61ifdFhnCy&output=json&coordtype=gcj02&location="+e.detail.latitude+","+e.detail.longitude,
					method: 'GET',
					success: function(res) {
						that.info.detail = res.data.result.formatted_address
					},
					error: function(e) {
					}
				});
				that.marker[0].latitude = e.detail.latitude
				that.marker[0].longitude = e.detail.longitude
			},
			chooseIdCardFront(){
				let that = this;
				uni.chooseImage({
				    count: 1,
				    sizeType: ['compressed'],
				    //sourceType: ['album'],
				    success: function(res) {
						that.idCardFrontUrl = res.tempFilePaths[0];
						that.idCardFrontShow = true;
						
						const tempFilePaths = res.tempFilePaths;
						commonApi.upload(tempFilePaths[0],"bas_ent_change").then(res => {
							let jsonObj = JSON.parse(res);
							if(jsonObj.code == 0){
								let item = {};
								for(let key in jsonObj.data){
								  item[key] = jsonObj.data[key];
								}
								item["fileType"] = 'idCardFront';
								that.info.idCardFrontFileList = [];
								that.info.idCardFrontFileList.push(item);
							}
						});
						
				    }
				});
			},
			chooseIdCardBack(){
				let that = this;
				uni.chooseImage({
				    count: 1,
				    sizeType: ['compressed'],
				    //sourceType: ['album'],
				    success: function(res) {
						that.idCardBackUrl = res.tempFilePaths[0];
						that.idCardBackShow = true;
						
						const tempFilePaths = res.tempFilePaths;
						commonApi.upload(tempFilePaths[0],"bas_ent_change").then(res => {
							let jsonObj = JSON.parse(res);
							if(jsonObj.code == 0){
								let item = {};
								for(let key in jsonObj.data){
								  item[key] = jsonObj.data[key];
								}
								item["fileType"] = 'idCardBack';
								that.info.idCardBackFileList = [];
								that.info.idCardBackFileList.push(item);
							}
						});
				    }
				});
			},
			delIdCardFront(){
				this.idCardFrontUrl = "";
				this.idCardFrontShow = false;
				this.info.idCardFrontFileList = [];
			},
			delIdCardBack(){
				this.idCardBackUrl = "";
				this.idCardBackShow = false;
				this.info.idCardBackFileList = [];
			},
			formSubmit: function(e) {
				uni.showLoading({
					title: '上传中',
					mask:true,
				});
				
				var rule = [
				    { name: "name", checkType: "string", checkRule: "1,20", errorMsg: "请填写正确的姓名"},
					{ name: "mainType", checkType: "string", checkRule: "1,2", errorMsg: "请选择主体类别" },
					{ name: "detail", checkType: "string", checkRule: "1,100", errorMsg: "请填写详细地址" },
					{ name: "contactsPhone", checkType: "phoneno",errorMsg: "请填写正确的手机号"},
					{ name: "cardNo", checkType: "idCard",errorMsg: "请填写正确的身份证"}
				];
				let that = this;
				let ent = {};
				for(let key in e.detail.value){
					ent[key] = e.detail.value[key];
				}
				ent.entId = uni.getStorageSync('ent').id;
				ent.mainType=that.mainType;
				let provinceValue = that.multiArray[0][that.multiIndex[0]];
				let cityValue = that.multiArray[1][that.multiIndex[1]];
				let countyValue = that.multiArray[2][that.multiIndex[2]];
				that.multiList.forEach((item) => {
					if(item.name == provinceValue){
						ent["province"] = item.code;
					}else if(item.name == cityValue){
						ent["city"] = item.code;
					}else if(item.name == countyValue){
						ent["county"] = item.code;
					}
				})
				ent["address"] = provinceValue + cityValue + countyValue;
				ent["lng"] = that.longitude;
				ent["lat"] = that.latitude;
				
				let fileList = [];
				that.info.idCardFrontFileList.forEach((item) => {
					fileList.push(item);
				})
				that.info.idCardBackFileList.forEach((item) => {
					fileList.push(item);
				})
				ent["fileList"] = fileList;
				ent["entType"] = 1;
				ent["businessType"] = that.info.businessType;
				ent["farmType"] = that.info.farmType;
				ent["examineStatus"] = 0;
				ent["autograph"] = that.info.autograph;
				
				var checkRes = graceChecker.check(ent, rule);
				if(!checkRes){
					uni.hideLoading();
					uni.showToast({ title: graceChecker.error, icon: "none" });
					return;
				}
				
				if(that.info.idCardFrontFileList == null || that.info.idCardFrontFileList.length==0){
					uni.hideLoading();
					uni.showToast({ title: '请上传第二代身份证人像面照片', icon: "none" });
					return;
				}
				if(!that.faceDistinguish){
					uni.hideLoading();
					uni.showToast({ title: '第二代身份证人像面照片不符合要求', icon: "none" });
					return;
				}
				if(that.info.idCardBackFileList == null || that.info.idCardBackFileList.length==0){
					uni.hideLoading();
					uni.showToast({ title: '请上传第二代身份证国徽面照片', icon: "none" });
					return;
				}
				if(!that.backDistinguish){
					uni.hideLoading();
					uni.showToast({ title: '第二代身份证国徽面照片不符合要求', icon: "none" });
					return;
				}
				if(that.info.autograph == null || that.info.autograph == ''){
					uni.hideLoading();
					uni.showToast({ title: '请设置签名', icon: "none" });
					return;
				}
				if(ent.detail.indexOf(ent.address)<0){
					uni.hideLoading();
					uni.showModal({
						title: '提示',
						content: '详细地址不在所选区域之内，是否继续操作？',
						showCancel: true,
						success: (res) => {
							if (res.confirm) {
								let param = {cardNo:ent["cardNo"],id:ent.entId};
								entApi.checkIdCard(param).then(res => {
									if(res.code == 0){
										if(res.data){
											entChangeApi.saveEnt(ent).then(res => {
												if(res.code == 0){
													uni.setStorageSync('entChange', res.data);
													if(res.data.basicFlag==='1'){
														uni.hideLoading();
														uni.showModal({
															content: '未在基础数据采集系统内查询到您的信息,请前往填写详细信息',
															mask: true,
															showCancel:false,
															success: (res) => {
																uni.reLaunch({
																	url: "/pages/basic/basicEntForm?tableName=bas_ent_change"
																})
															}
														});
													}else{
														uni.showToast({
															//icon:'none',
															title: '保存成功',
															duration: 2000
														});
														setTimeout(()=>{
															uni.reLaunch({
																url: "/pages/index/index"
															})
														},2000);
													}	
													
												}
											});
										}else{
											uni.hideLoading();
											uni.showToast({
												icon:'none',
												title: '身份证已存在',
												duration: 2000
											});
										}
									}
								});
							}
						},
					})
				}else{
					let param = {cardNo:ent["cardNo"],id:ent.entId};
					entApi.checkIdCard(param).then(res => {
						if(res.code == 0){
							if(res.data){
								entChangeApi.saveEnt(ent).then(res => {
									if(res.code == 0){
										uni.setStorageSync('entChange', res.data);
										if(res.data.basicFlag==='1'){
											uni.hideLoading();
											uni.showModal({
												content: '未在基础数据采集系统内查询到您的信息,请前往填写详细信息',
												mask: true,
												showCancel:false,
												success: (res) => {
													uni.reLaunch({
														url: "/pages/basic/basicEntForm?tableName=bas_ent_change"
													})
												}
											});
										}else{
											uni.showToast({
												//icon:'none',
												title: '保存成功',
												duration: 2000
											});
											setTimeout(()=>{
												uni.reLaunch({
													url: "/pages/index/index"
												})
											},2000);
										}	
										
									}
								});
							}else{
								uni.hideLoading();
								uni.showToast({
									icon:'none',
									title: '身份证已存在',
									duration: 2000
								});
							}
						}
					});
				}
			},
			doss(){//开启签名
				this.isShow = true;
			},
			close(){//关闭签名
				this.isShow = false;
				setTimeout(()=>{
					uni.pageScrollTo({
						scrollTop:1000
					});
				},300);
			},
			save(val){//保存签名
				this.isShow = false;
				//this.autographImg = val;
				let that = this;
				commonApi.getBase64(val).then(res => {
					let jsonObj = JSON.parse(res);
					if(jsonObj.code == 0){
						that.info.autograph = jsonObj.data;
						that.autographImg = "data:image/png;base64," + jsonObj.data;
					}
					setTimeout(()=>{
						uni.pageScrollTo({
							scrollTop:1000
						});
						uni.hideLoading();
					},300);
				});
			},
			getIdCardFrontImgUrl(urls){
				let that = this;
				this.info.idCardFrontFileList = [];
				urls.forEach((url) => {
					let item = {};
					item["fileType"] = 'idCardFront';
					item["tableName"] = 'bas_ent_change';
					item["filePath"] = url;
					this.info.idCardFrontFileList.push(item);
					let attachment = {"filePath" :url,"type":"face"};
					if(!cardScanEnabled){
						return ;
					}
					uni.showLoading({
						title: '图片文字识别中',
						mask:true,
					});
				 
					var url = url.substring(url.lastIndexOf("/tmp")+1,url.length);					
					let base64Code = "";
					console.log("返回微信图片=="+"wxfile://"+url);
					// 转base64
					wx.getFileSystemManager().readFile({
						filePath: "wxfile://"+url,
						encoding: "base64",
						success: function(res) {
							base64Code = res.data
							var json = {
								"image":base64Code,
								"configure":"{\'side\':\'face\'}"
							}
							commonApi.ocrIdCardPicJs(json).then(res => {
								var result = res;
								uni.hideLoading();
								if(null!=result){
									console.log("result.num=="+result.num)
									that.info.cardNo = result.num;
									that.info.name = result.name;
									that.picDisabled = false;
									that.faceDistinguish = true;
								}else{
									uni.showToast({ title: '第二代身份证人像面照片不符合要求', icon: "none" });
									that.picDisabled = true;
									that.faceDistinguish = false;
								}
							})
						}
					})
					/*api.ocrIdCardPic(attachment).then(res => {
						uni.hideLoading();
						if(null!=res.data){
							this.info.cardNo = res.data.num;
							this.info.name = res.data.name;
							this.picDisabled = false;
							this.faceDistinguish = true;
						}else{
							uni.showToast({ title: '第二代身份证人像面照片不符合要求', icon: "none" });
							this.picDisabled = true;
							this.faceDistinguish = false;
						}
					})*/
				})
			},
			getIdCardBackImgUrl(urls){
				let that = this;
				this.info.idCardBackFileList = [];
				urls.forEach((url) => {
					let item = {};
					item["fileType"] = 'idCardBack';
					item["tableName"] = 'bas_ent_change';
					item["filePath"] = url;
					this.info.idCardBackFileList.push(item);
					let attachment = {"filePath" :url,"type":"back"};
					if(!cardScanEnabled){
						return ;
					}
					uni.showLoading({
						title: '图片文字识别中',
						mask:true,
					});

					var url = url.substring(url.lastIndexOf("/tmp")+1,url.length);					
					let base64Code = "";
					console.log("返回微信图片=="+"wxfile://"+url);
					// 转base64
					wx.getFileSystemManager().readFile({
						filePath: "wxfile://"+url,
						encoding: "base64",
						success: function(res) {
							base64Code = res.data
							var json = {
								"image":base64Code,
								"configure":"{\'side\':\'back\'}"
							}
							commonApi.ocrIdCardPicJs(json).then(res => {
								var result = res;
								console.log("back=="+result)
								uni.hideLoading();
								if(null!=result){
									that.backDistinguish = true;
								}else{
									uni.showToast({ title: '第二代身份证国徽面照片不符合要求', icon: "none" });
									that.backDistinguish = false;
								}
							})
						}
					})
					/* api.ocrIdCardPic(attachment).then(res => {
						uni.hideLoading();
						if(null!=res.data){
							this.backDistinguish = true;
						}else{
							uni.showToast({ title: '第二代身份证国徽面照片不符合要求', icon: "none" });
							this.backDistinguish = false;
						}
					}) */
				})
			},
			removeIdCardFront(index){
				this.info.idCardFrontFileList = [];
			},
			removeIdCardBack(index){
				this.info.idCardBackFileList = [];
			},clickMap(){
				let that = this;
				if(that.firstChooseLocation){
					uni.chooseLocation({
						success: function (res) {
							that.info.detail = res.address;
							that.latitude = res.latitude;
							that.longitude = res.longitude;
							that.firstChooseLocation = false;
						}
					});
				}else{
					uni.chooseLocation({
						latitude:that.latitude,
						longitude:that.longitude,
						success: function (res) {
							that.info.detail = res.address;
							that.latitude = res.latitude;
							that.longitude = res.longitude;
						}
					});
				}
			},
			cardNoChange(event){
				this.info.cardNo = event.target.value;
			},
			nameChange(event){
				this.info.name = event.target.value;
			},
			findMainTypeList() {
				let that = this;
				entApi.findMainTypePersonList().then(res => {
					if (res.code == 1) {
						uni.showToast({
							icon: 'none',
							title: res.message
						});
						return;
					}
					that.mainTypeArray = res.data;
				})
			},
			bindMainTypePickerChange(e) {
				this.mainTypeIndex = e.target.value
				var selected = this.mainTypeArray[this.mainTypeIndex]           //获取选中的数组
				this.mainType = selected.key          //选中的code
			},
		}
	}
</script>

<style>
	.row-img{
		width: 100%;
		height: 140rpx;
		background-color: #FFF;
	}
</style>
