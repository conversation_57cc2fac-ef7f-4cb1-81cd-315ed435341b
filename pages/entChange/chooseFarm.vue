<template>
	<view class="whztxx_all">
		<div class="whztxx_cont" @click="setFarmType(0)">
		        <div class="whztxx_img">
		            <image :src="imgPath+'img/img27.png'" alt="">  
		        </div>
		        <div class="whztxx_tit">
		            <p>牧业主体</p>
		            <uni-icons type="arrowright" size="25" style="color:grey;float: right;"></uni-icons>
		        </div>
		</div>
		<div class="whztxx_cont" @click="setFarmType(1)">
		        <div class="whztxx_img">
		            <image :src="imgPath+'img/img28.png'" alt="">
		        </div>
		        <div class="whztxx_tit">
		            <p>渔业主体</p>
		            <uni-icons type="arrowright" size="25" style="color:grey;float: right;"></uni-icons>
		        </div>
		</div>
		<div class="whztxx_title">
		        <span style="color: #ff0000">*</span>在正式维护主体信息前，请先选择主体类型
		</div>
		<div class="whztxx_title">
		        <span style="color: #ff0000">*</span>请注意主体信息维护保存后，主体类型将不可更改
		</div>
	</view>
</template>

<script>
	import uniIcons from "@/components/uni-icons/uni-icons.vue"
	var config = require('../../common/config.js')
	export default {
		components: {uniIcons},
		data() {
			return {
				imgPath:config.IMG_PATH,
				info:{
					businessType:"",
					farmType:""
				}
			}
		},
		methods: {
			setFarmType: function(value) {
				uni.navigateTo({
					url: "/pages/entChange/chooseNature?businessType="+this.info.businessType+"&farmType="+value
				})
			},
		},
		onLoad: function (option) { //option为object类型，会序列化上个页面传递的参数
			this.info.businessType = option.businessType
		}
	}
</script>

<style>
</style>
