<template>
	<view style="margin-bottom:30px;">		
		<view class="whxx_tit">
			<ul>
				<li>
					<p>
						主体类型：{{businessTypeShow}}
					</p>
				</li>
				<li>
					<p>
						主体性质：企业
					</p>
				</li>
			</ul>
		</view>
		<!--审核状态图片-->		
		<image v-if="info.examineStatus == '0'" style="position:absolute;right:5%;top:2%;width:80px;height:60px;" :src="imgPath+'img/shz.png'" />
		<image v-if="info.examineStatus == '1'" style="position:absolute;right:5%;top:2%;width:80px;height:60px;" :src="imgPath+'img/shtg.png'"/>
		<image v-if="info.examineStatus == '-1'" style="position:absolute;right:5%;top:2%;width:80px;height:60px;" :src="imgPath+'img/shsb.png'" />
		<view v-if= "info.examineStatus == '-1'" class="whxx_all">
			<view class="whxx_left">
				驳回原因:
			</view>
			<view class="whxx_right">
				{{info.examineOpinion}}
			</view>
		</view>
		<view class="whxx_all">
			<view class="whxx_left">
				企业名称:
			</view>
			<view class="whxx_right">
				{{info.name}}
			</view>
		</view>
		<view class="whxx_all">
			<view class="whxx_left">
				统一社会信用代码:
			</view>
			<view class="whxx_right">
				{{info.socialCode}}
			</view>
		</view>
		<view class="whxx_all">
			<view class="whxx_left">
				企业法人:
			</view>
			<view class="whxx_right">
				{{info.legalPerson}}
			</view>
		</view>
		<view class="whxx_all">
			<view class="whxx_left">
				主体类别:
			</view>
			<view class="whxx_right">
				{{info.mainType|mainTypeFormat}}
			</view>
		</view>
		<view class="whxx_all">
			<view class="whxx_left">
				企业联系人:
			</view>
			<view class="whxx_right">
				{{info.contacts}}
			</view>
		</view>
		<view class="whxx_all">
			<view class="whxx_left">
				联系电话:
			</view>
			<view class="whxx_right">
				{{info.contactsPhone}}
			</view>
		</view>
		<view class="whxx_all">
			<view class="whxx_left">
				所在区域:
			</view>
			<view class="whxx_right">
				{{info.address}}
			</view>
		</view>
		<view class="whxx_all" style="border-bottom: none;">
			<view class="whxx_left">
				详细地址:
			</view>
			<view class="whxx_right">
				{{info.detail}}
			</view>
		</view>
		<view class="whxx_all_hist" style="border-bottom: none;">
			<view style="width:100%;">
				<map id="map1" ref="map1" style="width: 100%;height:200px;" :latitude="latitude" :longitude="longitude" :markers="marker"></map>
			</view>
		</view>
		<view class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
			<span class="title_tit" style="padding: 0px;">
				工商营业执照正面照片:
			</span>
		</view>
		<view class="whxx_all_hist" style="margin:10px auto;height:120px;">
			<view class="pboto_right" style="margin-bottom:10px;">
				<view style="width:60px;height: 60px;position: relative;">
					<view class="ck-img" v-for="(item, index) in cardNoFileList" :key="index">
						<image :src="item.fileUrl" v-if="item" style="width: 200px;height: 105px;" @click="perviewImg(index,'cardNo')"></image>
					</view>
				</view>
			</view>
		</view>
		<view class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
			<span class="title_tit" style="padding: 0px;">
				企业公章:
			</span>
		</view>
		<view class="whxx_all_hist" style="margin:10px auto;">
			<view class="pboto_right" style="margin-bottom:10px;">
				<view style="width:60px;height: 60px;position: relative;">
					<view class="ck-img" v-for="(item, index) in sealFileList" :key="index">
						<image :src="item.fileUrl" v-if="item" style="width: 50px;height: 50px;" @click="perviewImg(index,'seal')"></image>
					</view>
				</view>
			</view>
		</view>
		<view class="whxx_all_hist" style="border-bottom: none;">
			<span class="title_tit" style="padding: 0px;">
				企业简介:
			</span>
		</view>
		<view class="whxx_all_hist" style="font-size: 14px;">
			{{info.companyIntroduction}}
		</view>
		<view class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
			<span class="title_tit" style="padding: 0px;">
				企业照片:
			</span>
		</view>
		<view class="whxx_all_hist" style="margin:10px auto;">
			<view class="pboto_right" style="margin-bottom:10px;width: 90%;margin-left: 5%;">
				<view style="width:100%;">
					<swiperImg :swiperList="swiperList"
					         :indicatorDots="true" 
					         :autoplay="true"
					         :interval="2000" 
					         :duration="500" 
					         />
				</view>
			</view>
		</view>
		<view class="whxx_all_hist" style="border-bottom: none;">
			<span class="title_tit" style="padding: 0px;">
				荣誉简介:
			</span>
		</view>
		<view class="whxx_all_hist" style="font-size: 14px;">
			{{info.entHonor}}
		</view>
		<view class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
			<span class="title_tit" style="padding: 0px;">
				荣誉照片:
			</span>
		</view>
		<view class="whxx_all_hist" style="margin:10px auto;">
			<view class="pboto_right" style="margin-bottom:10px;width: 90%;margin-left: 5%;">
				<view style="width:100%;">
					<swiperImg :swiperList="honorList"
					         :indicatorDots="true" 
					         :autoplay="true" 
					         :interval="2000" 
					         :duration="500" 
					         />
				</view>
			</view>
		</view>
		<view class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
			<span class="title_tit" style="padding: 0px;">
				企业视频:
			</span>
		</view>
		<view class="whxx_all_hist" style="margin:10px auto;">
			<view class="pboto_right" style="margin-bottom:10px;width: 100%;text-align: center;;">
				<view v-if="videoAttachment.fileUrl" style="width:100%;">
					<video :src="videoAttachment.fileUrl"></video>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	
	import swiperImg from '@/components/zhtx-swiper/swiper.vue'
	var config = require('../../common/config.js')
	export default {
		components:{swiperImg},
    computed: {
      businessTypeShow() {
        if (this.info.businessType === '0') {
          return "种植"
        }
        if (this.info.businessType === '1') {
          return "养殖"
        }
        if (this.info.businessType === '2') {
          return "检测"
        }
        return ""
      }
    },
		data() {
			return {
				imgPath:config.IMG_PATH,
				info:{
					id:"",
					examineOpinion:"",
					name:"",
					socialCode:"",
					legalPerson:"",
					contacts:"",
					contactsPhone:"",
					address:"",
					detail:"",
					entHonor:"",
					companyIntroduction:"",
					mainType:"",
				},
				latitude: 0,
				longitude: 0,
				marker: [{
				   id:0,
				   latitude: 0,//纬度
				   longitude: 0,//经度
				}],
				cardNoFileList:[],
				sealFileList:[],
				swiperList:[],
				honorList:[],
				videoAttachment:{},
				currentImg:0,  //当前默认选中
			}
		},
		onLoad:function(options){
			/**
			 * 企业主体数据由上层通过路由参数传递，当前页面直接获取
			 */
			let that = this;
			uni.showLoading({
			    title: '数据加载中'
			}); 
			that.info = JSON.parse(options.item);
			that.latitude = that.info.lat;
			that.longitude = that.info.lng;
			that.marker[0].latitude = that.info.lat;
			that.marker[0].longitude = that.info.lng;
			if(null!=that.info.fileList && that.info.fileList.length>0){
				that.info.fileList.forEach((item) => {							
					if(item.fileType == 'licensePic'){
						that.cardNoFileList.push(item);
					}else if(item.fileType == 'sealPic'){
						that.sealFileList.push(item);
					}
					else if(item.fileType == 'entPic'){
						let swiper = {};
						swiper.img = item.fileUrl;
						that.swiperList.push(swiper);
					}
					else if(item.fileType == 'entHonorPic'){
						let honor = {};
						honor.img = item.fileUrl;
						that.honorList.push(honor);
					}
					else if(item.fileType == 'video'){
						that.videoAttachment=item;
					}
				})
			}
			uni.hideLoading();
		},
		methods: {
			perviewImg(index,type) {
				let imgList = [];
				if('cardNo'==type){
					imgList = this.cardNoFileList;
				}
				if('seal'==type){
					imgList = this.sealFileList;
				}
				let urls = [];
				if (index != -1) {
					urls[0] = imgList[index].fileUrl;
				} else {
					urls = this.imgList;
				}
				uni.previewImage({
					urls: urls
				});
			},
		},
		filters: {
			mainTypeFormat(value) {
				if(value==="1"){
					return `小农户`
				}else if(value==="5"){
					return `农产品生产企业`
				}else if(value==="10"){
					return `农民专业合作社`
				}else if(value==="15"){
					return `种养大户`
				}else if(value==="20"){
					return `家庭农场`
				}
				return "";
			},
		},
		
	}
	
</script>

<style>
</style>