<template>
	<view class="content">
		<button type="primary" @tap="doss">点击签名1</button>
		<view class="imgs">
			<image class="img" :src="img1" mode="widthFix"></image>
		</view>
		<button type="primary" @tap="getSi">获取签名</button>
		<catSignature canvasId="canvas1"  @close="close" @save="save" :visible="isShow" />
	</view>
</template>

<script>
	import catSignature from "@/components/cat-signature/cat-signature.vue"
	export default {
			components:{catSignature},
			data() {
				return {
					img1:'',
					isShow:false,
				}
			},
			onLoad() {
	
			},
			methods: {
				doss(){
					this.isShow = true;
				},
				close(){
					this.isShow = false;
				},
				save(val){
					this.isShow = false;
					this.img1 = val
				},
				getSi(){
					uni.showToast({
					    title: this.img1,
					    duration: 2000
					});
				},
			}
		}
</script>

<style>
	.imgs{width: 500upx;height: 500upx;display: flex;margin: 0 auto;flex-wrap: wrap;}
	.imgs img{width: 100%; height: 100%;}
</style>
