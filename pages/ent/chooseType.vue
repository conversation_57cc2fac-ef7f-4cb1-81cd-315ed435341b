<template>
	<view v-show="show">
		<view class="whztxx_all">
			<div class="whztxx_cont" @click="setBusinessType(1)">
				<div class="whztxx_img">
					<image :src="imgPath+'img/img23.png'" alt="">
				</div>
				<div class="whztxx_tit">
					<p>养殖主体</p>
					<uni-icons type="arrowright" size="25" style="color:grey;float: right;"></uni-icons>
				</div>
			</div>
			<div class="whztxx_cont" @click="setBusinessType(0)">
				<div class="whztxx_img">
					<image :src="imgPath+'img/img24.png'" alt="">
				</div>
				<div class="whztxx_tit">
					<p>种植主体</p>
					<uni-icons type="arrowright" size="25" style="color:grey;float: right;"></uni-icons>
				</div>
			</div>
      <div class="whztxx_cont" @click="setBusinessType(2)">
        <div class="whztxx_img">
          <image :src="imgPath+'img/img55.png'" alt="">
        </div>
        <div class="whztxx_tit">
          <p>检测主体</p>
          <uni-icons type="arrowright" size="25" style="color:grey;float: right;"></uni-icons>
        </div>
      </div>
			<div class="whztxx_title">
				<span style="color: #ff0000">*</span>在正式维护主体信息前，请先选择主体类型
			</div>
			<div class="whztxx_title">
				<span style="color: #ff0000">*</span>请注意主体信息维护保存后，主体类型将不可更改
			</div>
		</view>
    <tarbar current="2"></tarbar>
	</view>
</template>

<script>
	import entApi from '../../api/ent.js';
	import uniIcons from "@/components/uni-icons/uni-icons.vue"
  import Tarbar from "../../components/tabbar/tarbar.vue";
	var config = require('../../common/config.js')
	export default {
		components: {
      Tarbar,
			uniIcons
		},
		data() {
			return {
				imgPath: config.IMG_PATH,
				show: false,
				token: uni.getStorageSync("token")
			}
		},
		onLoad:function(){

		},
		onShow: function(option) {
			/**判断逻辑
			 * 1、先判断token有效性，
			 * 2、判断ent缓存是否存在，不存在说明是新增业务
			 * 3、判断当前主体信息是否需要补入基础信息采集数据
			 * 4、判断主体审核状态，如果examineStatus == '90'，那么说明此主体是变更暂存，跳转至变更页面
			 * 5、根据主体信息的entType判断展示不同的页面
			 * 6、以上逻辑都通过后显示当前页面功能
			 */
			let that = this;
			if (!that.token) {
				that.show = true;
				return;
			}
			uni.getStorage({
				key: 'ent',
				success: function(res) {
					uni.setNavigationBarTitle({
						title: '维护主体信息'
					});
					if (res.data != null && res.data != '') {
						if (res.data.basicFlag == '1' && res.data.basicEnterFlag == '0') {
							uni.showModal({
								icon: "none",
								content: '未在基础数据采集系统内查询到您的信息,请前往填写详细信息',
								mask: true,
								showCancel: false,
								success: function(res) {
									that.$Router.replaceAll("/pages/basic/basicEntForm")
								}
							});
							return;
						}
						//变更暂存状态：跳转至变更页面
						if (res.data.examineStatus == '90') {
							that.viewChange();
							return;
						}
						//冻结状态：跳转首页页面
						if (res.data.frozenFlag == '1') {
							uni.showModal({
								icon: "none",
							    content: '您的账号已冻结，请联系技术服务人员取消冻结',
								mask: true,
								showCancel:false,
							    success: function (res) {
									that.$Router.replaceAll("/pages/index/index")
							    }
							});
							return ;
						}
						let entType = res.data.entType;
						//变更后查看标识,0为变更审核后，主体首次查看主体信息，需要对变更审核通过、驳回操作给出提示
						let changeViewFlag = res.data.changeViewFlag;
						if (changeViewFlag == "0") {
							//变更审核状态1：通过,-1驳回
							let changeStatus = res.data.changeStatus;
							//通过
							if (changeStatus == "1") {
								uni.showModal({
									icon: "none",
									content: '主体信息变更已通过',
									mask: true,
									showCancel: false,
									success: function(res) {
										that.viewCheck(entType)
									}
								});
							} else if (changeStatus == "-1") { //驳回
								let changeOpinion = res.data.changeOpinion?res.data.changeOpinion:"无";
								let msg = "主体信息变更审核已驳回，是否重新提交变更申请？\n驳回原因：" + changeOpinion;
								uni.showModal({
									icon: 'none',
									content: msg,
									mask: true,
									success: function(res) {
										if (res.confirm) {
											that.toEntChange();
										} else if (res.cancel) {
											that.viewCheck(entType)
										}
									}
								});
							}
							//调用后台api更新changeViewFlag
							entApi.updateChangeView().then(res => {
								if (res.code == 1) {
									uni.showModal({
										title: '提示',
										content: res.message,
										mask: true,
										showCancel: false,
										success: function(res) {}
									});
								}
							})
							return;
						}
						that.viewCheck(entType)
						return;
					} else {
						that.show = true;
					}
				}
			});
		},
		methods: {
			setBusinessType: function(value) {
				if (!this.token) {
					this.gotoLogin();
				} else {
					if (1 === value) {
						uni.navigateTo({
							url: "../ent/chooseFarm?businessType=" + value
						})
					} else if (0 === value) {
						uni.navigateTo({
							url: "../ent/chooseNature?businessType=" + value
						})
					} else if (2 === value) {
            uni.navigateTo({
              url: "../ent/enterpriseInfo?businessType="+ value+"&farmType="
            })
          }

				}
			},
			gotoLogin() {
				uni.showModal({
					title: '未登录',
					content: '您未登录，需要登录后才能继续',
					mask: true,
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '../login/index'
							});
						}
					}
				});
			},
			viewCheck(entType) {
				uni.setNavigationBarTitle({
					title: '主体信息查看'
				});
				if (entType == '0' || entType == '2') {
					this.viewEnt()
				} else {
					this.viewPerson()
				}
			},
			viewEnt() {
				uni.redirectTo({
					url: "../ent/enterpriseInfoShow",
				})
			},
			viewPerson() {
				uni.redirectTo({
					url: "../ent/personInfoShow",
				})
			},
			viewChange() {
				uni.redirectTo({
					url: "/pages/entChange/viewCheck",
				})
			},
			toEntChange() {
				uni.redirectTo({
					url: '/pages/entChange/chooseType',
				})
			},
		}
	}
</script>

<style>
</style>
