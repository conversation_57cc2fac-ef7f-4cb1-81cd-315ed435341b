<template>
	<view class="whztxx_all">
		<div class="whztxx_cont" @click="enterpriseInfo">
		        <div class="whztxx_img">
		            <image :src="imgPath+'img/img25.png'" alt="">  
		        </div>
		        <div class="whztxx_tit">
		            <p>企业主体</p>
		            <uni-icons type="arrowright" size="25" style="color:grey;float: right;"></uni-icons>
		        </div>
		</div>
		<div class="whztxx_cont" @click="personInfo">
		        <div class="whztxx_img">
		            <image :src="imgPath+'img/img26.png'" alt="">
		        </div>
		        <div class="whztxx_tit">
		            <p>个人主体</p>
		            <uni-icons type="arrowright" size="25" style="color:grey;float: right;"></uni-icons>
		        </div>
		</div>
		<div class="whztxx_title">
		        <span style="color: #ff0000">*</span>在正式维护主体信息前，请先选择主体类型
		</div>
		<div class="whztxx_title">
		        <span style="color: #ff0000">*</span>请注意主体信息维护保存后，主体类型将不可更改
		</div>
		<div class="whztxx_title">
		        <span style="color: #ff0000">*</span>企业主体：在市场监督管理局进行注册登记，取得营业执照
		</div>
		<div class="whztxx_title">
		        <span style="color: #ff0000">*</span>个人主体：以个人/家庭为单位进行农产品生产活动，未在市场监督管理局进行注册登记
		</div>
	</view>
</template>

<script>
	import uniIcons from "@/components/uni-icons/uni-icons.vue"
	var config = require('../../common/config.js')
	export default {
		components: {uniIcons},
		data() {
			return {
				imgPath:config.IMG_PATH,
				info:{
					businessType:"",
					farmType:""
				}
			}
		},
		methods: {
			personInfo:function(){
				uni.navigateTo({
					url: "../ent/personInfo?businessType="+ this.info.businessType+"&farmType="+this.info.farmType
				})
			},
			enterpriseInfo:function(){
				uni.navigateTo({
					url: "../ent/enterpriseInfo?businessType="+ this.info.businessType+"&farmType="+this.info.farmType
				})
			}
		},
		onLoad: function (option) { //option为object类型，会序列化上个页面传递的参数
			this.info.businessType = option.businessType;
			if(option.farmType){
				this.info.farmType = option.farmType;
			}
		}
	}
</script>

<style>
</style>
