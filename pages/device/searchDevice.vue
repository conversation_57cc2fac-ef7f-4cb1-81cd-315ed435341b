<template>
    <view>
		<div class="sblj_top" >
			<view class="title" style="float: left;"></view>
			<view @click="toSupportDevice()" style="float: right;">
				<p style="float:right;">查看设备的型号</p>	
				<image :src="imgPath+'img/print.png'" style="float:right;width: 23px;height: 25px;margin-top:12px;margin-right:5px;">
			</view>
		</div >
		<div class="print_all" v-if="getBleDevice && getBleDevice.name">
			<div class="print_left">
				<image :src="imgPath+'img/print.png'">
				<p>{{getBleDevice.name}}</p>
			</div>
			<div class="print_right">
				<p>已连接</p>
			</div>
		</div>
		<view></view>
		<view></view>
		<view></view>
		<view></view>
		<view></view>
		<div class="sblj_top">
			<view class="title">蓝牙设备列表</view>
		</div>
		<div class="print_all">
			<view v-for="(item,index) in devices" class="device-view" :key="index" @click="connect(item)" style="width: 100%;">
				<div class="print_left">
					<image :src="imgPath+'img/print_gray.png'" />
					<p>{{item.name || item.localName}}</p>
				</div>
				<div class="print_right">
					<!-- <p>{{item.deviceId}}</p> -->
				</div>
			</view>
		</div>
    </view>
</template>

<script>
	import bluetoothDeviceApi from '../../api/bluetoothDevice';
	import {mapGetters} from 'vuex';
    import bluetooth from "../../utils/bluetooth.js"
	var config = require('../../common/config.js')
    export default {
        data() {
            return {
				powerBy: config.POWER_BY,
				serviceTel: config.SERVICE_TEL,
				imgPath:config.IMG_PATH,
                devices: [],
                testData: [{name: "device1", deviceId: "a1111111"}, {name: "device2", deviceId: "b2222222"}]
            };
        },
		computed: {
        	...mapGetters(['getBleDevice', "getBluetoothDiscovering", "getBluetoothAvailable"])
		},
		watch: {
			getBluetoothAvailable(curVal, oldVal) {
				if (curVal && !oldVal) {
					this.goSearch()
				}
			}
		},
        methods: {
            goSearch() {
            	if (this.getBluetoothDiscovering) {
            		bluetooth.stopFindBule();
				}
                bluetooth.searchDevice().then(res => {
                    //监听寻找到新设备的事件
                    console.log("监听寻找到新设备的事件---------------")
                    this.callback();
                }).catch(error => {
					uni.showToast({
						title: '请打开蓝牙',
						icon: "none",
						mask: true,
						duration: 3000,
						success() {
							setTimeout(() => {
							}, 3000)
						}
					});
                });
            },
			callback() {
				uni.onBluetoothDeviceFound(res => {
					//console.log('--------------new-----------------------');
					res.devices.forEach(device => {
						if (!this.devices.find(d => d.deviceId === device.deviceId) && device.name !== "未知设备" && device.name !== '') {
							this.devices.push(device)
						}
					});
					//console.log(this.devices)
				})
			},
            connect(device) {
				uni.showLoading({
				    title: '连接中',
					mask:true,
				});
				bluetoothDeviceApi.recordAmount({
				    deviceName: device.name
				}).then(res => {
					if (res.code === 1) {
						uni.hideLoading();
						uni.showToast({
							icon: 'none',
							title: res.message,
							mask: true,
							duration: 1500
						});
						return;
					}else if(res.data==0){
						uni.hideLoading();
						uni.showModal({
							icon: "none",
						    content: '系统暂未适配该设备\n如有疑问，请联系技术人员\n联系电话：'+this.serviceTel,
							mask: true,
							showCancel:false,
						});
						return;
					}else{
						bluetooth.disconnetDevices().then(() => {
							bluetooth.connectDevices(device).then(res => {
								uni.hideLoading();
								//uni.navigateTo跳转的返回，默认1为返回上一级
								uni.navigateBack({  
									delta: 1
								});
							}).catch(error => {
								uni.hideLoading();
							});
						})
					}
				})
            },
			toSupportDevice(){
				this.$Router.push("pages/device/supportDevice")
			}
        },
        onLoad() {
			
        },
		onShow() {
      bluetooth.openBluetoothAdapter().then(res => {
        this.goSearch();
      })
		},
		onHide() {
        	bluetooth.stopFindBule();
		},
		onUnload() {
			bluetooth.stopFindBule();
		}

    }
</script>

<style scoped lang="less">
    .device-view {
        height: 100rpx;
    }
	.title {
		color: #0e0e0e;
		font-size: 16px;
		/* margin: 18rpx;
		border-bottom: 1px solid #cccccc;
		font-size: 14px; */
	}
	.sblj_top {
		width: 96%;
		height: 45px;
		line-height: 45px;
		border-bottom: 1px solid #e0e0e0;
		margin: 0 auto;
	}
	.print_all {
		width: 96%;
		margin: 10px auto;
		overflow: auto;
	}
	.print_all p {
		padding: 8px;
	}
	.print_left {
		width: 70%;
		float: left;
	}
	.print_left image {
		width: 32px;
		height: 33px;
		float: left;
	}
	.print_left p {
		
		float: left;
	}
	.print_right {
		width: 30%;
		float: left;
	}
</style>
