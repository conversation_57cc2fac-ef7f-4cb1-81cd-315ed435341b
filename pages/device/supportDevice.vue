<template>
	<view>
		<div class="sblj_top" >
			<image :src="imgPath+'img/print.png'" style="float:left;width: 23px;height: 25px;margin-top:12px;margin-right:5px;">
			<view class="title" style="float: left;">适配的打印设备型号</view>	
		</div> 
		<div class="dysb_list" style="padding-bottom: 15px;">
			<div class="dysb_all" style="overflow: hidden;">
				<div class="dysb_left">
					<div class="dysb_tit">
						<span>设备1</span>
					</div>
					<image :src="imgPath+'img/device/D45BT.png'">
				</div>
				<div class="dysb_right">
					<div class="dysb_tit">
						<span>打印参数</span>
					</div>
					<div class="dysb_title">
						<p>产品：<span>D45BT</span></p>
					</div>
					<div class="dysb_title">
						<p>打印方式：<span>热敏打印</span></p>
					</div>
					<div class="dysb_title">
						<p>分辨率：<span>203dpi</span></p>
					</div>
					<div class="dysb_title">
						<p>通讯接口：<span>USB+蓝牙</span></p>
					</div>
					<div class="dysb_title">
						<p>打印宽度：<span>104mm</span></p>
					</div>
					<div class="dysb_title">
						<p>标签宽度：<span>≤108mm</span></p> 
					</div>	
				</div>
			</div>
			<div class="dysb_all" style="overflow: hidden;">
				<div class="dysb_left">
					<div class="dysb_tit">
						<span>设备2</span>
					</div>
					<image :src="imgPath+'img/device/M22.png'">
				</div>
				<div class="dysb_right">
					<div class="dysb_tit">
						<span>打印参数</span>
					</div>
					<div class="dysb_title">
						<p>产品：<span>M22</span></p>
					</div>
					<div class="dysb_title">
						<p>打印方式：<span>热敏打印</span></p>
					</div>
					<div class="dysb_title">
						<p>分辨率：<span>203dpi</span></p>
					</div>
					<div class="dysb_title">
						<p>通讯接口：<span>蓝牙</span></p>
					</div>
					<div class="dysb_title">
						<p>打印宽度：<span>72mm(max)</span></p>
					</div>
					<div class="dysb_title">
						<p>标签宽度：<span>≤75mm</span></p> 
					</div>	
				</div>
			</div>
			
			<div class="dysb_all" style="overflow: hidden;">
				<div class="dysb_left">
					<div class="dysb_tit">
						<span>设备3</span>
					</div>
					<image :src="imgPath+'img/device/M32B.png'">
				</div>
				<div class="dysb_right">
					<div class="dysb_tit">
						<span>打印参数</span>
					</div>
					<div class="dysb_title">
						<p>产品：<span>M32B</span></p>
					</div>
					<div class="dysb_title">
						<p>打印方式：<span>热敏打印</span></p>
					</div>
					<div class="dysb_title">
						<p>分辨率：<span>203dpi</span></p>
					</div>
					<div class="dysb_title">
						<p>通讯接口：<span>蓝牙</span></p>
					</div>
					<div class="dysb_title">
						<p>打印宽度：<span>72mm(max)</span></p>
					</div>
					<div class="dysb_title">
						<p>标签宽度：<span>≤76mm</span></p> 
					</div>	
				</div>
			</div>
			
			<div class="dysb_all" style="overflow: hidden;">
				<div class="dysb_left">
					<div class="dysb_tit">
						<span>设备4</span>
					</div>
					<image :src="imgPath+'img/device/E3PLUS-v2.png'">
				</div>
				<div class="dysb_right">
					<div class="dysb_tit">
						<span>打印参数</span>
					</div>
					<div class="dysb_title">
						<p>产品：<span>E3plus</span></p>
					</div>
					<div class="dysb_title">
						<p>打印方式：<span>热敏打印</span></p>
					</div>
					<div class="dysb_title">
						<p>分辨率：<span>203dpi</span></p>
					</div>
					<div class="dysb_title">
						<p>通讯接口：<span>USB+蓝牙</span></p>
					</div>
					<div class="dysb_title">
						<p>打印宽度：<span>72mm(max)</span></p>
					</div>
					<div class="dysb_title">
						<p>标签宽度：<span>25mm-80mm</span></p> 
					</div>	
				</div>
			</div>
		</div>
	</view>
</template>

<script>
	var config = require('../../common/config.js')
	export default {
	    data() {
	        return {
				imgPath:config.IMG_PATH,
	        };
	    },
	}
</script>

<style>
	.title {
		color: #0e0e0e;
		font-size: 16px;
		/* margin: 18rpx;
		border-bottom: 1px solid #cccccc;
		font-size: 14px; */
	}
	.sblj_top {
		width: 96%;
		height: 45px;
		line-height: 45px;
		border-bottom: 1px solid #e0e0e0;
		margin: 0 auto;
	}
</style>
