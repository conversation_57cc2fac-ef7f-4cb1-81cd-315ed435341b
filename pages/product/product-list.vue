<template>
	<view class="index" v-if="showFlag">
		<view class="safe-body" style="margin-top: 5px;">
			<div class="cpxx_title" v-if="token">
				<div class="date_all" @click="addDateOrder()">
					<div class="cpxx_left" style="margin: 0 6%;">添加时间</div>
					<div class="cpxx_right" style=" display:inline-block;margin-top:10px;">
						<div class="jt_all">
							<div v-if="addUpShow" class="triangle-up"></div>
						</div>
						<div class="jt_all">
							<div v-if="addDownShow" class="triangle-down"></div>
							<div v-if="downShow" class="triangle-down" style="border-top: 7px solid #fff;"></div>
						</div>
					</div>
				</div>
				<div class="number_all" @click="printCountOrder()">
					<div class="cpxx_left" style="margin: 0 2%;">开具数量</div>
					<div class="cpxx_right" style=" display:inline-block;margin-top:10px;">
						<div class="jt_all">
							<div v-if="countUpShow" class="triangle-up"></div>
						</div>
						<div class="jt_all">
							<div v-if="countDownShow" class="triangle-down"></div>
							<div v-if="cDownShow" style="border-top: 7px solid #fff;" class="triangle-down"></div>
						</div>
					</div>
				</div>
				<div class="cppl_all">
					<picker @change="bindPickerChange" :value="index" :range="array" :range-key="'label'">
						<view class="uni-input">{{array[index]?array[index].label:'请选择产品品类'}}</view>
					</picker>
				</div>
				<div v-if="managerShow" class="manage_all">
					<view @click="del()">管理</view>
				</div>
				<div v-if="isListDataEmpty" class="add_all">
					<div class="add_top" @click="onAdd()">
						<image :src="imgPath+'img/add.png'">
							<p style="float:left">添加产品</p>
					</div>
					<div class="add_bottom">
						<p>目前还没有添加任何产品</p>
					</div>
				</div>
			</div>
			<div v-else>
				<div class="add_all">
					<div class="add_top" @click="onAdd()">
						<image :src="imgPath+'img/add.png'">
							<p style="float:left">添加产品</p>
					</div>
					<div class="add_bottom">
						<p>目前还没有添加任何产品</p>
					</div>
				</div>
			</div>
		</view>
		<view v-if="delShow && list.length >0" class="fixBt" @click="onAdd()">添加</view>
		<view>
			<checkbox-group class="block" @change="changeCheckbox">
				<view class="news-item" v-for="(item,index) in list" :key="index">
					<checkbox v-if="!delShow" :value="String(item.id)" :checked="checkedArr.includes(String(item.id))" :class="{'checked':checkedArr.includes(String(item.id))}"></checkbox>
					<view class="news-intr" @click="onEdit(item)" style="width: 90%;float:left;">
						<view class="flex-table">
							<view class="news-title">{{item.name}}</view>
						</view>
						<view class="news-info" style="width: 100%;">
							<p style="float:left;width:60%;">
								添加时间:{{item.addDate | formatDate}}
							</p>
							<p style="float:left;width:40%;text-align: right;">开具数量：{{item.count?Math.floor(item.count):0}}</p>
						</view>

					</view>
					<uni-icons type="arrowright" size="17" style="float:left;color:grey;width:50px;height:30px;line-height:30px;text-align: right;"></uni-icons>
				</view>
			</checkbox-group>
			<view>
				<checkbox-group @change="allChoose" style="margin-top:10px ;">
					<label v-if="!delShow" style="wisth:100%;">
						<div style="float:left;margin-left: 30rpx;">
							<checkbox value="all" :class="{'checked':allChecked}" :checked="allChecked?true:false"></checkbox> 全选
						</div>
						<div style="float:right;">
							<button :disabled="deleteShow" type="primary" style="color: #fff; background-color: #02994f;width: 80px;float:right;height: 30px;line-height: 30px;font-size:14px;margin-right: 10px;"
							 @click="onDel()">删除</button>
							<button style="color: #000;width: 80px;float:right;height: 30px;line-height: 30px;font-size:14px;margin-right: 5px;"
							 @click="del()">取消</button>
						</div>
					</label>
				</checkbox-group>
			</view>
		</view>
		<uni-load-more :status="status" :content-text="contentText" />
    <tarbar current="1"></tarbar>
	</view>
</template>

<script>
	import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';
	import api from '../../api/product.js';
	import dictApi from '../../api/dict.js';
	import {
		formatDate
	} from '@/common/formatDate.js'
  import Tarbar from "../../components/tabbar/tarbar.vue";
	var config = require('../../common/config.js')
	export default {
		components: {
      Tarbar,
			uniLoadMore
		},
		data: function() {
			return {
				imgPath: config.IMG_PATH,
				token: uni.getStorageSync("token"),
				deleteShow: true, //删除按钮是否禁用
				showFlag: true, //控制页面是否显示
				managerShow: true, //控制管理是否显示
				status: 'more',
				delShow: true, //删除多选是否显示
				order: true, //判断排序是正序排序还是倒序
				addUpShow: true, //时间正序排序显示
				addDownShow: true, //时间倒序排序显示
				countUpShow: true, //开具数量正序排序显示
				countDownShow: true, //开具数量正序排序显示
				param: {
					name: '',
					order: '',
					productSortCode: '',
				},
				form: {
					idList: []
				},
				page: 1,
				list: [],
				total: 0, //总数
				refreshing: false, //为true表示正在刷新
				fetchPageNum: 1, //当前页数
				loading: true, // 为true表示加载中
				array: [],
				index: '',
				checkedArr: [], //复选框选中的值
				allChecked: false, //是否全选
				contentText: { // 加载提示
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				flag: true,
				downShow: false,
				cDownShow: false,
				oldProductSortCode:'',//保存选中的产品类型id
				isListDataEmpty:false,//列表数据是否为空
			}

		},
		created() {
			/**
			 * 判断逻辑
			 * 1、判断token是否存在
			 * 2、判断当前ent缓存，如果不存在，提示需要先维护主体信息
			 * 3、判断当前主体信息是否需要补入基础信息采集数据
			 * 4、判断主体审核状态，审核中，跳转至主体查看页面
			 * 5、判断主体审核状态，如果examineStatus == '90'，那么说明此主体是变更暂存，跳转至变更页面
			 * 6、以上逻辑都通过后显示当前页面功能
			 */
			if (!this.token) {
				return;
			}
			let that = this;
			//验证用户是否维护了主体信息
			let ent = uni.getStorageSync("ent");
			if (!ent) {
				uni.showModal({
					title: '先维护主体信息',
					content: '确定前往维护主体信息?',
					mask: true,
					success: function(res) {
						if (res.confirm) {
							that.$Router.replaceAll("/pages/ent/chooseType")
						} else if (res.cancel) {
							that.$Router.replaceAll("/pages/index/index")
						}
					}
				});
				return;
			} else if (ent.basicFlag == '1' && ent.basicEnterFlag == '0') {
				uni.showModal({
					icon: "none",
					content: '未在基础数据采集系统内查询到您的信息,请前往填写详细信息',
					mask: true,
					showCancel: false,
					success: function(res) {
						that.$Router.replaceAll("/pages/basic/basicEntForm")
					}
				});
				return;
			} else if (ent.examineStatus === '0' || ent.examineStatus === '-1') {
				uni.showModal({
					icon: "none",
					content: '主体信息审核中，前往主体信息查看审核状态？',
					mask: true,
					success: function(res) {
						if (res.confirm) {
							that.$Router.replaceAll("/pages/ent/chooseType")
						} else if (res.cancel) {
							that.$Router.replaceAll("/pages/index/index")
						}
					}
				});
				return;
			} else if (ent.examineStatus == '90') { //变更暂存状态：跳转至变更页面
				uni.redirectTo({
					url: "/pages/entChange/viewCheck",
				})
				return;
			} else if (ent.frozenFlag == '1'){
				uni.showModal({
					icon: "none",
				    content: '您的账号已冻结，请联系技术服务人员取消冻结',
					mask: true,
					showCancel:false,
				    success: function (res) {
						that.$Router.replaceAll("/pages/index/index")
				    }
				});
				return ;
			}else {
				/* let wechatUser = uni.getStorageSync("wechatUser");
				that.headImg = that.imgPath+"img/index/img18.png";
				if (wechatUser) {
					that.headImg = wechatUser.avatarUrl;
				} */
				that.showFlag = true;
			}
		},
		onLoad: function(option) {
			uni.setNavigationBarTitle({
				title: '维护产品信息'
			});

		},
		onShow: function() {
			if (!this.token) {
				return;
			}
			this.list = [];
			this.index = '';
			this.param = {};
			this.checkedArr = [];
			this.fetchPageNum = 1;
			this.refreshing = true;
			this.addUpShow = true;
			this.addDownShow = true;
			this.downShow = false;
			this.countUpShow = true;
			this.countDownShow = true;
			this.cDownShow = false;
			this.delShow = true;
			this.deleteShow = true;
			this.getData();
			this.getDict();

		},
		//上拉加载
		onReachBottom: function() {
			if (!this.token) {
				return;
			}
			this.pullLoad();
		},
		//下拉刷新
		onPullDownRefresh() {
			if (!this.token) {
				return;
			}
			console.log('下拉刷新');
			this.refreshing = true;
			this.getData();
		},
		methods: {
			//控制时间排序
			addDateOrder: function() {
				let that = this;
				that.countUpShow = true;
				that.addDownShow = false;
				that.countDownShow = true;
				that.cDownShow = false;
				if (that.order) {
					that.addUpShow = true;
					that.downShow = false;
					that.order = false;
					that.param.addDateOrder = '1';
				} else {
					that.downShow = true;
					that.addUpShow = false;
					that.order = true;
					that.param.addDateOrder = '2';
				}
				that.param.printCountOrder = '0'
				that.refreshing = true;
				that.getData();

			},
			//控制开具数量排序
			printCountOrder: function(item) {
				let that = this;
				that.downShow = false;
				that.addDownShow = true;
				that.addUpShow = true;
				that.countDownShow = false;
				that.param.addDateOrder = '0'
				if (that.order) {
					that.countUpShow = true;
					that.cDownShow = false;
					that.order = false;
					that.param.addDateOrder = '1';
				} else {
					that.cDownShow = true;
					that.countUpShow = false;
					that.order = true;
					that.param.addDateOrder = '2';
				}
				that.refreshing = true;
				that.getData();
			},
			getDict: function() {
				let that = this;
				let dict = {
					label: '全部',
					value: ''
				};
				dictApi.findDict({
					type: 'product_sort_code'
				}).then(res => {
					if (res.code === 0) {
						that.array=[]
						that.array.push(dict)
						that.array = that.array.concat(res.data);
						
					}
				})
			},
			pullLoad() {
				let that = this;
				if (that.flag) {
					that.status = 'loading';
					that.page++; //每触底一次 page +1
					let pageNo = that.page;
					api.findProductList(pageNo, that.param).then(res => {
						if (res.code != 0) {
							uni.showToast({
								title: "请求数据失败请重试"
							})
							return;
						} else {

							if (res.data.list.length < 1) { //没有数据
								that.status = 'noMore'; //没有数据时显示‘没有更多’
								return false;
							}
							for (var i = 0; i < res.data.list.length; i++) {
								that.list.push(res.data.list[i])
							}
							if (that.list.length >= res.data.count) {
								that.status = 'noMore'; //没有数据时显示‘没有更多’
								that.flag = false;
								return;
							}
						}
					})
				}
			},
			getData: function() {
				let that = this;
				that.list = [];
				that.loading = true;
				that.flag = true;
				that.page = 1;
				that.status = "loading"
				let pageNo = that.refreshing ? 1 : that.fetchPageNum;
				that.isListDataEmpty = false;
				api.findProductList(pageNo, that.param).then(res => {
					if (res.code != 0) {
						uni.showToast({
							title: "请求数据失败请重试"
						})
						return;
					} else {
						if(that.oldProductSortCode == that.param.productSortCode  || !that.param.productSortCode ){
							that.allChecked = false
							
						}else{
							that.checkedArr = []
							that.oldProductSortCode = that.param.productSortCode
						}
						
						that.total = res.data.count;
						if(that.total==0){
							that.isListDataEmpty = true;
						}
						if (that.refreshing) {
							that.refreshing = false;
							uni.stopPullDownRefresh();
							
							if (res.data.list && res.data.list!=undefined &&res.data.list!='undefined') {
								that.list = res.data.list;
								if (that.list.length >= res.data.count) {
									that.flag = false;
									if(that.total ==0){
										that.status = 'more';
									}else{
										that.status = 'noMore';
									}
								}else{
									that.status = 'more';
								}
							}else{
								that.status = 'more';
							}
							that.fetchPageNum = res.data.next;
							that.loadingType = 0;
						} else {
							that.list = that.list.concat(res.data.list);
							that.fetchPageNum = res.data.next;
						}
						if (!res.data.list) {
							that.managerShow = false;
							that.allChecked = false;
						} else {
							that.managerShow = true;
							if(res.data.list.length > that.checkedArr.length){
								
								that.allChecked = false;
							}else{
								for(let i = 0;i<res.data.list.length;i++){
									let dProductId = res.data.list[i].id;
									if( !that.checkedArr.includes(dProductId)){
										that.allChecked = false
										return
									}
								}
								that.allChecked = true;
							}
						}
					}
				})

			},
			bindPickerChange: function(e) {
				//重置时间排序状态
				this.addUpShow = true;
				this.addDownShow = true;
				this.form.addDateOrder = '0';
				//重置打印数量排序状态
				this.countUpShow = true;
				this.countDownShow = true;
				this.form.printCountOrder = '0';
				//获取下拉选的内容
				this.index = e.target.value
				var selected = this.array[this.index] //获取选中的数组
				this.param.productSortCode = selected.value //选中的id
				this.param.productSortName = selected.label //选中的名称
				this.refreshing = true;
				this.getData();
			},
			onAdd: function() {
				if (!this.token) {
					this.gotoLogin();
				} else {
					uni.navigateTo({
						url: '../product/product-add',
					})
				}
			},
			onEdit: function(item) {
				var textObj = encodeURIComponent(JSON.stringify(item))
				uni.navigateTo({
					url: '../product/product-show?textObj=' + textObj,
				})
			},
			onDel: function(id) {
				let that = this;
				let fileList = [];
				that.checkedArr.forEach((item) => {
					fileList.push(item);
				})
				that.form.idList = fileList;
				uni.showModal({
					title: '提示',
					content: '确定要删除该产品么？',
					showCancel: true,
					success: (res) => {
						if (res.confirm) {
							uni.showLoading({
								title: '删除中',
								mask: true,
							});
							api.deleteBatch(that.form).then(res => {
								uni.showToast({
									"title": '删除成功'
								})
								setTimeout(function() {
									that.list = [];
									that.fetchPageNum = 1;
									that.refreshing = true;
									that.delShow = true;
									that.getData();
								}, 600)
							}).catch(err => {
								uni.hideLoading();
								console.log('请求失败：' + err.status + ',' + err.statusText);
							})
						}
					}
				});

			},
			del() {
				if (this.delShow) {
					this.delShow = false;
					this.allChecked = false;
					this.checkedArr = [];
				} else {
					this.delShow = true;
				}
			},
			// 多选复选框改变事件
			changeCheckbox(e) {
				let that = this;
				that.checkedArr = e.detail.value;
				// 如果选择的数组中有值，并且长度等于列表的长度，就是全选
				if (that.checkedArr.length > 0 && that.checkedArr.length == that.list.length) {
					that.allChecked = true;
				} else {
					that.allChecked = false;
				}
				if (that.checkedArr.length > 0) {
					that.deleteShow = false;
				} else {
					that.deleteShow = true;
				}
			},
			// 全选事件
			allChoose(e) {
				let that = this;
				let chooseItem = e.detail.value;
				// 全选
				if (chooseItem[0] == 'all') {
					that.allChecked = true;
					for (let item of that.list) {
						let itemVal = String(item.id);
						if (!that.checkedArr.includes(itemVal)) {
							that.checkedArr.push(itemVal);
						}
					}
				} else {
					// 取消全选
					that.allChecked = false;
					that.checkedArr = [];
				}
				if (that.checkedArr.length > 0) {
					that.deleteShow = false;
				} else {
					that.deleteShow = true;
				}
			},
			gotoLogin() {
				uni.showModal({
					title: '未登录',
					content: '您未登录，需要登录后才能继续',
					mask: true,
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '../login/index'
							});
						}
					}
				});
			}
		},
		filters: {
			formatDate(time) {
				time = time.replace("-", "/").replace("-", "/");
				let date = new Date(time)
				return formatDate(date, 'yyyy-MM-dd') //年-月-日 时分
			}
		}
	}
</script>

<style>
	.fixBt {
		position: fixed;
		width: 120upx;
		height: 120upx;
		text-align: center;
		line-height: 120upx;
		border-radius: 50%;
		background-color: #02994f;
		color: #fff;
		right: 5upx;
		bottom: 290upx;
	}

	.safe-body {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		width: 100%;
	}

	.index {
    padding-bottom: calc(env(safe-area-inset-bottom) + 60px);
	}

	.row {
		flex-direction: row;
	}

	.column {
		flex-direction: column;
	}

	.load-bottom {
		width: 100%;
		text-align: center;
	}

	.loadMore {
		font-size: 30upx;
		color: #555;
		margin-bottom: 20upx;
		line-height: 60rpx;
		margin-left: 170px;
	}

	.emptyData {
		margin-left: 175px;
	}

	.news-item {
		height: 40px;
		margin-left: 30rpx;
		margin-right: 30rpx;
		display: flex;
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		padding-top: 15px;
		padding-bottom: 15px;
		border-bottom: 2rpx #efefef solid;
	}

	.news-item .news-intr {
		flex: 2;
		padding-right: 20rpx;
		/* display: flex; */
		flex-direction: column;
		justify-content: space-between;
	}

	.news-intr .news-title {}

	.news-intr .news-info {
		display: flex;
		font-size: 12px;
		color: #999999;
	}

	.news-info .news-ago {
		text-align: left;
	}

	.news-info .news-type {
		text-align: right;
		flex: 2;
	}

	.news-item .news-image {
		flex: 1;
		max-width: 240rpx;
		max-height: 160rpx;
	}

	.news-item .news-image image {
		max-width: 100%;
		max-height: 100%;
	}

	.left {
		margin-left: 130rpx;
	}

	.triangle-up {
		margin-top: 3px;
		/*display:inline-block;*/
		width: 0;
		height: 0;
		border-left: 5px solid transparent;
		border-right: 5px solid transparent;
		border-bottom: 7px solid #fff;
		/* background: #fff; */
	}

	.triangle-down {
		margin-top: 3px;
		/*display:inline-block;*/
		width: 0;
		height: 0;
		border-left: 5px solid transparent;
		border-right: 5px solid transparent;
		border-top: 7px solid #9dd6b7;
		/* background: #9dd6b7; */
	}
</style>
