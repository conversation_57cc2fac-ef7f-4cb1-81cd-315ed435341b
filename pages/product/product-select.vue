<template>
    <div class="select-pro">
        <div class="pro-search">
            <div class="input-wrap">
                <div class="icon-search">
                    <image src="../../static/img/search.png" mode="widthFix"></image>
                </div>
                <input class="uni-input" maxlength="20" name="name" :value="param.name" placeholder="搜索商品名称"
                    @confirm="searchData">
                <div class="icon-close" @click="resetData()">
                    <image src="../../static/img/close-search.png" mode="widthFix"></image>
                </div>
            </div>
            <!-- <input class="uni-input" maxlength="20" name="name" :value="param.name" placeholder="搜索商品名称"
                @confirm="searchData"> -->
        </div>
        <div class="pro-main">
            <radio-group @change="handleChange">
                <div class="pro-item" v-for="(item,index) in list" :key="index">
                    <div class="img-bor">
                        <image :src="item.fileList[0].url"></image>
                    </div>
                    <div class="info">
                        <p class="name">{{item.name}}</p>
                        <p class="inve"><span>{{item.standardStr}}</span> 库存{{item.currentStore}}</p>
                        <p class="price">价格 <span>￥{{item.price}}/{{item.unitLabel}}</span></p>
                    </div>
                    <div class="radio">
                        <radio :value="item.id" :checked="item.checked"></radio>
                    </div>
                </div>
                <uni-load-more :status="status" :content-text="contentText" />
                <div v-if="isListDataEmpty" class="add_all">

                    <div class="add_bottom">
                        <p>目前还没有任何产品</p>
                    </div>
                </div>
                <div style="height: 50px;"></div>
            </radio-group>
        </div>

        <div class="btm">
            <div class="button" @click="bindProduct()">确认绑定</div>
        </div>

    </div>
</template>

<script>
    var config = require('../../common/config.js')
    import api from '../../api/product.js';
    export default {
        data() {
            return {
                imgPath: config.FILE_PATH,
                list: [],
                status: 'more',
                contentText: { // 加载提示
                    contentrefresh: "正在加载...",
                    contentnomore: "没有更多数据了"
                },
                page: 1,
                refreshing: false, //为true表示正在刷新
                fetchPageNum: 1, //当前页数
                flag: true,
                loading: false,
                isListDataEmpty: false, //列表数据是否为空
                selectData: null,
                param: {
                    pageNum: 0,
                    pageSize: 10,
                    name: '',
                    shopId: uni.getStorageSync('userInfo').phoneNumber //'13596211372', //
                }
            }
        },
        onLoad() {

        },
        onShow() {
            this.refreshing = true;
            this.getData()
        },
        methods: {
            searchData(e) {
                this.param.name = e.detail.value
                this.list = [];
                this.fetchPageNum = 1;
                this.refreshing = true;
                this.getData()
            },
            //上拉加载
            onReachBottom: function() {
                if (!this.token) {
                    return;
                }
                this.pullLoad();
            },
            //下拉刷新
            onPullDownRefresh() {
                if (!this.token) {
                    return;
                }
                this.refreshing = true;
                this.getData();
            },
            handleChange(e) {
                const selectData = this.list.filter(item => item.id == e.target.value)
                this.selectData = selectData[0]
            },
            resetData() {
                this.refreshing = true
                this.param.name = ''
                this.getData()
            },
            getData() {
                let that = this;
                that.list = [];
                that.loading = true;
                that.flag = true;
                that.page = 1;
                that.status = "loading"
                let pageNo = that.refreshing ? 1 : that.fetchPageNum;
                that.isListDataEmpty = false;
                that.param.pageNum = pageNo
                api.productfindReBuyProductPage(that.param).then(res => {
                    console.log(res)
                    if (res.code == 0) {
                        that.total = res.data.total;
                        if (that.total == 0) {
                            that.isListDataEmpty = true;
                        }
                        if (that.refreshing) {
                            that.refreshing = false;
                            uni.stopPullDownRefresh();
                            if (res.data.data && res.data.data != undefined && res.data.data != 'undefined') {
                                that.list = res.data.data;
                                that.list = that.list.map(function(obj) {
                                    return Object.assign({}, obj, {
                                        checked: false
                                    })
                                })
                                if (that.list.length >= res.data.total) {
                                    that.flag = false;
                                    if (that.total == 0) {
                                        that.status = 'more';
                                    } else {
                                        that.status = 'noMore';
                                    }
                                } else {
                                    that.status = 'more';
                                }
                            } else {
                                that.status = 'more';
                            }
                            that.fetchPageNum = res.data.next;
                            that.loadingType = 0;
                        }
                    }
                })
            },
            bindProduct() {
                if (this.selectData) {
                    uni.$emit('getProduct', this.selectData)
                    uni.navigateBack({
                        delta: 1 // 返回的页面数
                    })
                } else {
                    uni.showToast({
                        title: '请选择商品'
                    })
                }
            }
        }
    }
</script>

<style scoped lang="scss">

</style>