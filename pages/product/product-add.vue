<template>
    <view>
        <form @submit="formSubmit">
            <div class="content_all">
                <div class="whxx_all" style="display: flex;align-items: center;">
                    <div class="whxx_left">
                        <span>*</span>产品名称:
                    </div>
                    <div class="whxx_right" style="min-height: 36px;display: flex;align-items: center;">
                        <input class="uni-input" maxlength="20" name="name" :value="form.name" placeholder="请输入产品名称">
                    </div>
                </div>
                <div class="whxx_all" style="display: flex;align-items: center;">
                    <div class="whxx_left">
                        <span>*</span>产品分类:
                    </div>
                    <div class="whxx_right">
                        <!--下拉选-->
                        <view class="uni-list-cell-db">
                            <picker style="min-height: 36px;display: flex;align-items: center;" name="productSortCode"
                                @change="bindPickerChange" :value="index" :range="array" :range-key="'label'">
                                <view class="uni-input">{{array[index]?array[index].label:'请选择产品品类'}}</view>
                            </picker>
                        </view>
                    </div>
                </div>
                <div class="whxx_all" style="display: flex;align-items: center;">
                    <div class="whxx_left">
                        <span>*</span>生产地址:
                    </div>
                    <div class="whxx_right"
                        style="position: relative;min-height: 36px;display: flex;align-items: center;">
                        <input class="uni-input" placeholder="请输入或点击右侧图标" maxlength="100" name="detail"
                            :value="form.detail" style="width: 80%;float: left;" />
                        <view style="float: left;width: 20%;" @click="clickMap">
                            <image :src="imgPath+'img/location.png'" alt=""
                                style="width: 30px;height: 30px;position: absolute;top: 10px;" />
                        </view>
                    </div>
                </div>

                <div class="whxx_all" style="display: flex;align-items: center;">
                    <div class="whxx_left">
                        <span></span>复购商品:
                    </div>
                    <div class="whxx_right" style="display: flex;align-items: center;" @click="toProductSelect()">
                        <input class="uni-input" maxlength="20" name="reBuyProductName" disabled="true"
                            :value="selectData.name" placeholder="请绑定选品平台商品">
                    </div>
                </div>

                <div class="whxx_all" style="display: flex;align-items: center;">
                    <div class="whxx_left">
                        <div style="float: left;">复购直通车:</div>
                        <div class="fg_icon" @click="open_reBuy">
                            <image src="../../static/img/help.png"></image>
                        </div>
                    </div>
                    <div class="whxx_right" style="min-height: 36px; padding: 0;">
                        <switch :checked="fg_checked" height="10" @change='changeSwitch'
                            style="transform: scale(0.6,0.6); float: right; margin-top: 3px;" />
                    </div>
                </div>

                <div class="whxx_all" style="border-bottom:none;">
                    <div class="whxx_left">
                        产品介绍:
                    </div>
                </div>
                <div class="whxx_all_hist">
                    <textarea maxlength="300"
                        style="font-size: 14px;width: 96%;margin: 10px auto;border: 1px solid #e0e0e0;"
                        name="productIntroduction" :value="form.productIntroduction" placeholder="请填写少于300字的介绍" />
                </div>
                <div class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
                    <span class="tit_left">产品照片(最多上传5张):</span>
                </div>
                <div class="whxx_all_hist" style="margin:10px auto;">
                    <div class="pboto_right" style="margin-bottom:10px;">
                        <view style="width:100%;">
                            <view class="row-img">
                                <ck-upload-img @removeImg="removeProduct" @returnImgUrl="getProductImgUrl"
                                    :initImgList="pictureUrl" :selectNum=5 :token="upToken" :tableName="tableName">
                                </ck-upload-img>
                            </view>
                        </view>
                    </div>
                </div>
                <div class="whxx_all" style="margin-top:10px;">
                    <div class="whxx_left">
                        <span v-if="CertificationFileList.length > 0 || form.productCertificationCode">*</span>产品认证:
                    </div>
                    <div class="whxx_right">
                        <checkbox-group @change="changeCertification">
                            <label v-for="(item, index) in productCertification" :key="index"
                                style="width: 150px;float: left;">
                                <checkbox :value="item.value" style="transform:scale(0.7)" />{{item.label}}
                            </label>
                        </checkbox-group>
                    </div>
                </div>
                <div class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
                    <span v-if="CertificationFileList.length > 0 || form.productCertificationCode"
                        style="color: red;">*</span>
                    <span class="tit_left">认证证书(最多上传5张):</span>
                </div>
                <div class="whxx_all_hist" style="margin:10px auto;">
                    <div class="pboto_right" style="margin-bottom:10px;">
                        <view style="width:100%;">
                            <view class="row-img">
                                <ck-upload-img @removeImg="removeAtt" @returnImgUrl="getAttImgUrl"
                                    :initImgList="CertificationUrl" :selectNum="5" :token="upToken"
                                    :tableName="tableName"></ck-upload-img>
                            </view>
                        </view>
                    </div>
                </div>

                <div v-show="moreShow">
                    <div class="whxx_all_hist" style="margin-top:10px;">
                        <div style="font-size: 14px;font-weight: bold;">推荐保质方式:</div>
                        <div>
                            <div v-for="(item,index) in form.productStorageList" :key="index" class="tjbz_cont">
                                <div class="tjbz_img">
                                    <icon type="cancel" size="26" @click="delItem(index)" />
                                </div>
                                <div class="whxx_all_hist">
                                    <div style="padding: 10px 0;overflow: hidden;">
                                        <p class="num_left">序号：</p>
                                        <p class="num_right">{{index+1}}</p>
                                    </div>
                                    <!-- <icon type="cancel" size="26" @click="delItem(index)"/> -->
                                </div>
                                <div class="whxx_all">
                                    <div class="whxx_left">
                                        <span>*</span>保存条件:
                                    </div>
                                    <div class="whxx_right">
                                        <picker @change="storageTypeChange($event,item)" :range="storageTypeDictList"
                                            :range-key="'label'">
                                            <view class="uni-input">
                                                {{item.storageTypeName?item.storageTypeName:'请选择保存条件'}}
                                            </view>
                                        </picker>
                                    </div>
                                </div>
                                <div class="whxx_all">
                                    <div class="whxx_left" style="margin-top: 8px;">
                                        <span>*</span>保存时间:
                                    </div>
                                    <div class="whxx_right">
                                        <uni-easyinput v-model="item.storageDays" type="number" :clearable="false"
                                            maxlength="20" placeholder="请填写数字"></uni-easyinput>
                                    </div>
                                </div>
                                <div class="whxx_all">
                                    <div class="whxx_left">
                                        <span>*</span>时间单位:
                                    </div>
                                    <div class="whxx_right">
                                        <picker @change="storageUnitChange($event,item)" :range="storageUnitList"
                                            :range-key="'label'">
                                            <view class="uni-input">
                                                {{item.storageUnit?item.storageUnit:'请选择时间单位'}}
                                            </view>
                                        </picker>
                                    </div>
                                </div>
                                <div class="whxx_all" style="margin-bottom: 10px;">
                                    <div class="whxx_left" style="margin-top: 5px;">
                                        补充说明:
                                    </div>
                                    <div class="whxx_right">
                                        <uni-easyinput v-model="item.instruction" type="textarea" :clearable="false"
                                            maxlength="50" placeholder="请填写补充说明"></uni-easyinput>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div style="margin-bottom: 8px;">
                            <button type="default" @click="addItem" plain="true"
                                style="width:160px;height:35px;line-height:35px;font-size:14px;color: #02994F;border-color: #cecece;">+增加推荐保质方式</button>
                        </div>
                    </div>
                    <div class="whxx_all">
                        <div class="whxx_left" style="margin-top: 8px;">
                            电商链接:
                        </div>
                        <div class="whxx_right">
                            <uni-easyinput v-model="form.electricityLink" type="text" :clearable="false" maxlength="500"
                                placeholder="请输入电商链接"></uni-easyinput>
                        </div>
                    </div>
                    <div class="whxx_all">
                        <div class="whxx_left" style="margin-top: 8px;">
                            种养殖规模:
                        </div>
                        <div class="whxx_right">
                            <uni-easyinput v-model="form.scaleAmount" type="number" :clearable="false" maxlength="20"
                                placeholder="请输入种养殖规模"></uni-easyinput>
                        </div>
                    </div>
                    <div class="whxx_all" style="display: flex;align-items: center;">
                        <div class="whxx_left">
                            种养殖规模单位:
                        </div>
                        <div class="whxx_right">
                            <picker style="min-height: 36px;display: flex;align-items: center;"
                                @change="scaleUnitChange" :range="productUnitDictList" :range-key="'label'">
                                <view class="uni-input">
                                    {{form.scaleUnitName?form.scaleUnitName:'请选择种养殖规模单位'}}
                                </view>
                            </picker>
                        </div>
                    </div>
                    <div class="whxx_all">
                        <div class="whxx_left" style="margin-top: 8px;">
                            年产值(万元):
                        </div>
                        <div class="whxx_right">
                            <uni-easyinput v-model="form.productionValue" type="number" :clearable="false"
                                maxlength="20" placeholder="请输入年产值"></uni-easyinput>
                        </div>
                    </div>
                    <div class="whxx_all">
                        <div class="whxx_left" style="margin-top: 8px;">
                            年产量:
                        </div>
                        <div class="whxx_right">
                            <uni-easyinput v-model="form.productionAmount" type="number" :clearable="false"
                                maxlength="20" placeholder="请输入年产量"></uni-easyinput>
                        </div>
                    </div>
                    <div class="whxx_all" style="display: flex;align-items: center;">
                        <div class="whxx_left">
                            年产量单位:
                        </div>
                        <div class="whxx_right">
                            <picker style="min-height: 36px;display: flex;align-items: center;"
                                @change="productionUnitChange" :range="productUnitDictList" :range-key="'label'">
                                <view class="uni-input">
                                    {{form.productionUnitName?form.productionUnitName:'请选择年产量单位'}}
                                </view>
                            </picker>
                        </div>
                    </div>
                </div>

                <view @click="moreShow = !moreShow" class="more">
                    <view v-if="moreShow"
                        style="height: 30px;display: flex;justify-content: center;align-items: center;">
                        <image :src="imgPath+'img/arrowdown2.png'"
                            style="width: 20px;height: 30px;transform: rotate(180deg)"></image><span>收起更多产品信息</span>
                    </view>
                    <view v-if="!moreShow"
                        style="height: 30px;display: flex;justify-content: center;align-items: center;">
                        <image :src="imgPath+'img/arrowdown2.png'" style="width: 20px;height: 30px;"></image>
                        <span>展开显示更多产品信息</span>
                    </view>
                </view>

                <div v-if="saveType=='product'" style="margin-top:20px;margin-bottom: 20px;height: 25px;">
                    <button form-type="submit" type="primary"
                        style="width:100px;height:35px;line-height:35px;font-size:14px;color: #fff; background-color: #02994f">提交</button>
                </div>
                <div v-if="saveType=='ent'" style="margin-top:20px;margin-bottom: 20px;height: 25px;">
                    <div style="float:left;width: 55%;">
                        <button type="primary" @click="onBack()"
                            style="width:100px;height:35px;line-height:35px;font-size:14px;color: #fff; background-color: #02994f">上一步</button>
                    </div>
                    <div style="float:left;width: 30%;">
                        <button form-type="submit" type="primary"
                            style="width:100px;height:35px;line-height:35px;font-size:14px;color: #fff; background-color: #02994f">提交</button>
                    </div>
                </div>
                <div v-if="saveType=='ent'" class="footer_tit">
                    <view>提交后由当地业务主管部门进行审核</view>
                </div>
            </div>
        </form>
        <uni-popup ref="popup" type="center">
            <div class="re-buy-main">
                <div class="title"><span @click="close_reBuy">
                        <image src="../../static/img/close.png" mode="widthFix"></image>
                    </span></spam>复购直通车说明</div>
                <div class="info">
                    <p>开启复购直通车后，</p>
                    <p>消费者可在合格证扫码页购买您的产品</p>
                </div>
                <div class="img">
                    <image src="https://jlsyncphgzqn.jikeruan.com/img/ynyp_alert.png" mode="widthFix"></image>
                </div>
            </div>
        </uni-popup>
    </view>
</template>

<script>
    import api from '../../api/product.js';
    import areaApi from '../../api/area.js';
    import dictApi from '../../api/dict.js';
    import entApi from '../../api/ent.js';
    import ckUploadImg from '@/components/ck-uploadImg/ck-uploadImg.vue';
    var graceChecker = require("../../common/graceui-dataChecker/graceChecker.js");
    var config = require('../../common/config.js')
    export default {
        components: {
            ckUploadImg
        },
        data() {
            return {
                imgPath: config.IMG_PATH,
                tableName: 'bas_product',
                isRotate: false, //七牛
                upToken: '', //七牛token
                indicatorDots: true,
                autoplay: true,
                interval: 2000,
                pictureUrl: [],
                CertificationUrl: [],
                areaName: [],
                areaList: {},
                saveType: "product", //保存标识 product=产品 ent=主体
                ent: {}, //主体对象
                form: {
                    name: '',
                    address: '',
                    detail: '',
                    entId: '',
                    productSortCode: '',
                    productSortName: '',
                    province: '',
                    city: '',
                    county: '',
                    longitude: '',
                    latitude: '',
                    productIntroduction: '',
                    productCertificationCode: '',
                    productCertificationName: '',
                    productStorageList: [],
                    electricityLink: '',
                    scaleAmount: '',
                    scaleUnitCode: '',
                    scaleUnitName: '',
                    productionValue: '',
                    productionAmount: '',
                    productionUnitCode: '',
                    productionUnitName: '',

                },
                storageTypeDictList: [],
                pictureFileList: [],
                CertificationFileList: [],
                latitude: 0,
                longitude: 0,
                marker: [{
                    latitude: 0, //纬度
                    longitude: 0, //经度 
                }],

                scale: 16, //地图缩放程度
                center: {
                    lng: 0,
                    lat: 0
                },
                zoom: 3,
                array: [],
                index: '',
                productCertification: [],
                storageUnitList: [{
                        label: "日"
                    },
                    {
                        label: "周"
                    },
                    {
                        label: "月"
                    },
                    {
                        label: "年"
                    },
                ],
                moreShow: false, //更多展示标识
                productUnitDictList: [],
                selectData: null, //绑定的复购商品
                fg_checked: 0
            }
        },

        onLoad: function(option) {
            var that = this;
            if (option.ent) {
                that.ent = JSON.parse(decodeURIComponent(option.ent));
                that.saveType = "ent";
            }
            uni.setNavigationBarTitle({
                title: '维护产品信息',
            });
            entApi.get7nToken().then(res => {
                if (res.code == 0) {
                    that.upToken = res.data;
                }
            });
            uni.getLocation({ //获取当前的位置坐标
                type: 'wgs84',
                success: function(res) {
                    that.latitude = res.latitude
                    that.longitude = res.longitude
                    uni.request({
                        url: "https://api.map.baidu.com/reverse_geocoding/v3/?ak=NEXIhGPsRgKDcGa1Ox2OWj61ifdFhnCy&output=json&coordtype=gcj02&location=" +
                            that.latitude + "," + that.longitude,
                        method: 'GET',
                        success: function(res) {
                            that.areaName.push(res.data.result.addressComponent.province)
                            that.areaName.push(res.data.result.addressComponent.city)
                            that.form.county = res.data.result.addressComponent.adcode
                            that.form.detail = res.data.result.formatted_address
                            that.form.address = res.data.result.addressComponent.province
                            that.form.address += res.data.result.addressComponent.city
                            that.form.address += res.data.result.addressComponent.district
                            that.findArea();
                        },
                        error: function(e) {
                            reject('网络出错');
                        }
                    })
                }
            });
            that.getDictClassify();
            that.getDictCertification();
            that.getDictStorageType();
            that.getDictProductUnit();
            //that.addItem();
        },
        onShow() {
            let that = this
            uni.$on('getProduct', function(data) {
                that.selectData = data
                that.fg_checked = 1
            })
        },
        methods: {
            open_reBuy() {
                this.$refs.popup.open('center')
            },
            close_reBuy() {
                this.$refs.popup.close()
            },
            changeSwitch(e) {
                this.fg_checked = e.detail.value ? 1 : 0
            },
            toProductSelect() {
                let param = {
                    phoneNum: uni.getStorageSync('userInfo').phoneNumber
                }
                api.checkPhoneNumOpenShopCart(param).then(res => {
                    if (res.data) {
                        uni.navigateTo({
                            url: './product-select'
                        })
                    } else {
                        uni.navigateTo({
                            url: '/pages/index/assisting'
                        })
                    }
                })

            },
            bindPickerChange: function(e) {
                this.index = e.target.value
                var selected = this.array[this.index] //获取选中的数组
                this.form.productSortCode = selected.value //选中的分类code
                this.form.productSortName = selected.label //选中的分类名称
            },
            storageTypeChange: function(e, item) {
                let storageTypeDictIndex = e.target.value;
                var selected = this.storageTypeDictList[storageTypeDictIndex]; //获取选中的数组
                item.storageTypeCode = selected.value; //选中的分类code
                item.storageTypeName = selected.label; //选中的分类名称
            },
            storageUnitChange: function(e, item) {
                let storageUnitIndex = e.target.value;
                var selected = this.storageUnitList[storageUnitIndex]; //获取选中的数组
                item.storageUnit = selected.label; //选中的分类名称
            },
            scaleUnitChange: function(e) {
                let scaleUnitIndex = e.target.value;
                var selected = this.productUnitDictList[scaleUnitIndex]; //获取选中的数组
                this.form.scaleUnitCode = selected.value //选中的分类code
                this.form.scaleUnitName = selected.label //选中的分类名称
            },
            productionUnitChange: function(e) {
                let productionUnitIndex = e.target.value;
                var selected = this.productUnitDictList[productionUnitIndex]; //获取选中的数组
                this.form.productionUnitCode = selected.value //选中的分类code
                this.form.productionUnitName = selected.label //选中的分类名称
            },
            addItem() {
                let that = this;
                if (that.form.productStorageList.length >= 3) {
                    uni.showToast({
                        title: '最多只能添加3个',
                        icon: "none"
                    });
                    return;
                }
                let productStorage = {
                    index: that.form.productStorageList.length,
                    entId: '',
                    productId: '',
                    storageTypeCode: '',
                    storageTypeName: '',
                    storageDays: '',
                    storageUnit: '',
                    instruction: '',

                };
                that.form.productStorageList.push(productStorage);
            },
            delItem(index) {
                let that = this;
                that.form.productStorageList.splice(index, 1)
            },
            storageDaysInputChange(e, item) {
                item.storageDays = e.detail.value;
            },
            formSubmit: function(e) {
                var that = this;
                uni.showLoading({
                    title: '保存中',
                    mask: true,
                });
                that.form.longitude = that.longitude
                that.form.latitude = that.latitude
                that.form.productIntroduction = e.detail.value.productIntroduction
                that.form.name = e.detail.value.name
                that.form.detail = e.detail.value.detail
                if (that.selectData) {
                    that.form.reBuyProductName = that.selectData.name
                    that.form.reBuyProductId = that.selectData.id
                }
                that.form.reBuyVisible = that.fg_checked
                var rule = [{
                        name: "name",
                        checkType: "string",
                        checkRule: "1,80",
                        errorMsg: "产品名称不能为空"
                    },
                    {
                        name: "productSortCode",
                        checkType: "string",
                        checkRule: "1,20",
                        errorMsg: "请选择产品分类"
                    },
                    {
                        name: "detail",
                        checkType: "string",
                        checkRule: "1,200",
                        errorMsg: "生产地址不能为空"
                    },
                ];
                var checkRes = graceChecker.check(that.form, rule);
                if (!checkRes) {
                    uni.showToast({
                        title: graceChecker.error,
                        icon: "none"
                    });
                    return;
                }
                var regRule = /[^\u4E00-\u9FA5|\d|\a-zA-Z|\r\n\s,.?!，。？！…—&$=()-+/*{}[\]]|\s/g;
                if (regRule.test(that.form.name)) {
                    uni.showToast({
                        title: '请输入正确的字符',
                        icon: "none"
                    });
                    return;
                }
                if (that.form.productCertificationCode && that.CertificationFileList.length < 1) {
                    uni.showToast({
                        title: '请上传认证证书',
                        icon: "none"
                    });
                    return;
                }
                if (that.CertificationFileList.length > 0 && !that.form.productCertificationCode) {
                    uni.showToast({
                        title: '请选择产品认证',
                        icon: "none"
                    });
                    return;
                }
                if (that.form.productStorageList.length > 0) {
                    let productStorageFlag = true;
                    that.form.productStorageList.forEach((item) => {
                        if (!item.storageTypeCode || !item.storageDays || !item.storageUnit) {
                            productStorageFlag = false;
                        }
                    })
                    if (!productStorageFlag) {
                        uni.showToast({
                            title: '请检查推荐保质方式',
                            icon: "none"
                        });
                        return;
                    }

                }
                let fileList = [];
                that.pictureFileList.forEach((item) => {
                    fileList.push(item);
                })
                that.CertificationFileList.forEach((item) => {
                    fileList.push(item);
                })
                that.form["fileList"] = fileList;

                console.log(that.form)

                if (that.saveType == "ent") {
                    entApi.saveEnt(that.ent).then(res => {
                        if (res.code == 1) {
                            uni.hideLoading();
                            uni.showToast({
                                icon: 'none',
                                title: res.message,
                                mask: true,
                                duration: 3000
                            });
                            return;
                        }
                        that.saveProduct(false);
                        uni.setStorageSync('ent', res.data);
                        if (res.data.basicFlag === '1') {
                            uni.hideLoading();
                            uni.showModal({
                                content: '未在基础数据采集系统内查询到您的信息,请前往填写详细信息',
                                mask: true,
                                showCancel: false,
                                success: (res) => {
                                    uni.reLaunch({
                                        url: "/pages/basic/basicEntForm"
                                    })
                                }
                            });
                        } else {
                            uni.showToast({
                                //icon:'none',
                                title: '信息提交成功',
                                duration: 2000,
                                mask: true,
                            });
                            setTimeout(() => {
                                uni.hideLoading();
                                uni.reLaunch({
                                    url: "/pages/index/index"
                                })
                            }, 2000);
                        }
                    })
                } else if (that.saveType == "product") {
                    that.saveProduct(true);
                }

            },
            saveProduct(relaunchFlag) {
                let that = this;
                api.saveProductNew(that.form).then(res => {
                    //that.form = {};
                    uni.showToast({
                        "title": '保存成功',
                        mask: true,
                    })
                    setTimeout(function() {
                        uni.hideLoading();
                        if (relaunchFlag) {
                            if (that.saveType == "ent") {
                                uni.reLaunch({
                                    url: "/pages/index/index",
                                })
                            } else {
                                uni.navigateBack({ //uni.navigateTo跳转的返回，默认1为返回上一级
                                    delta: 1
                                })
                            }
                        }
                    }, 600)
                }).catch(err => {
                    uni.hideLoading();
                    console.log('请求失败：' + err.status + ',' + err.statusText);
                })
            },
            changeCertification: function(e) {
                this.form.productCertificationCode = '';
                this.form.productCertificationName = '';
                for (var i = 0; i < e.detail.value.length; i++) {
                    this.form.productCertificationCode += e.detail.value[i] + ","
                    for (var j = 0; j < this.productCertification.length; j++) {
                        if (this.productCertification[j].value === e.detail.value[i]) {
                            this.form.productCertificationName += this.productCertification[j].label + ","
                        }
                    }
                }
                this.form.productCertificationCode = this.form.productCertificationCode.substr(0, this.form
                    .productCertificationCode.length - 1)
                this.form.productCertificationName = this.form.productCertificationName.substr(0, this.form
                    .productCertificationName.length - 1)
            },
            getDictClassify: function() {
                let that = this;
                dictApi.findDict({
                    type: 'product_sort_code'
                }).then(res => {
                    if (res.code === 0) {
                        that.array = res.data;
                    }
                })
            },
            getDictCertification: function() {
                let that = this;
                dictApi.findDict({
                    type: 'productCertification'
                }).then(res => {
                    if (res.code === 0) {
                        that.productCertification = res.data;
                    }
                })
            },
            getDictStorageType: function() {
                let that = this;
                dictApi.findDict({
                    type: 'storageTypeCode'
                }).then(res => {
                    if (res.code === 0) {
                        that.storageTypeDictList = res.data;
                    }
                })
            },
            findArea: function() {
                var that = this;
                areaApi.findTreeList({
                    areaName: that.areaName
                }).then(res => {
                    if (res.code === 0) {
                        that.form.province = res.data[0].code
                        that.form.city = res.data[1].code
                    }
                })
            },
            //删除产品照片
            removeProduct(index) {
                this.pictureFileList.splice(index, 1);
            },
            //返回产品照片
            getProductImgUrl(urls) {
                this.pictureFileList = [];
                urls.forEach((url) => {
                    let item = {};
                    item["fileType"] = 'productPicture';
                    item["tableName"] = 'bas_product';
                    item["filePath"] = url;
                    this.pictureFileList.push(item);
                })
            },
            //删除认证照片
            removeAtt(index) {
                this.CertificationFileList.splice(index, 1);
            },
            //返回认证照片
            getAttImgUrl(urls) {
                this.CertificationFileList = [];
                urls.forEach((url) => {
                    let item = {};
                    item["fileType"] = 'productCertPic';
                    item["tableName"] = 'bas_product';
                    item["filePath"] = url;
                    this.CertificationFileList.push(item);
                })
                console.log("this.CertificationFileList", this.CertificationFileList)
            },
            clickMap() {
                let that = this;
                //选择位置
                uni.chooseLocation({
                    latitude: that.latitude,
                    longitude: that.longitude,
                    success: function(res) {
                        that.form.detail = res.address;
                        that.form.latitude = res.latitude;
                        that.form.longitude = res.longitude;
                        that.getDivision(that.form.latitude, that.form.longitude)
                    }
                });
            },
            getDivision(latitude, longitude) {
                let that = this;
                uni.request({
                    url: "https://api.map.baidu.com/reverse_geocoding/v3/?ak=NEXIhGPsRgKDcGa1Ox2OWj61ifdFhnCy&output=json&coordtype=gcj02&location=" +
                        latitude + "," + longitude,
                    method: 'GET',
                    success: function(res) {
                        that.areaName.push(res.data.result.addressComponent.province)
                        that.areaName.push(res.data.result.addressComponent.city)
                        that.form.county = res.data.result.addressComponent.adcode
                        that.form.address = res.data.result.addressComponent.province
                        that.form.address += res.data.result.addressComponent.city
                        that.form.address += res.data.result.addressComponent.district
                        that.findArea();
                    },
                    error: function(e) {
                        reject('网络出错');
                    }
                })
            },
            onBack() {
                //uni.navigateTo跳转的返回，默认1为返回上一级
                uni.navigateBack({
                    delta: 1
                });
            },
            getDictProductUnit() {
                dictApi.findDict({
                    type: 'product_unit_code'
                }).then(res => {
                    if (res.code === 0) {
                        this.productUnitDictList = res.data;
                    }
                })
            },
        },

    }
</script>

<style scoped>
    .content {
        padding: 0 30upx;
    }

    .scroll {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
    }

    .box {
        display: inline-block;
        margin-right: 30upx;
    }

    .box:last-child {
        margin-right: 0;
    }

    .images {
        width: 520upx;
        height: 360upx;
        border-radius: 16upx;
    }

    .carousel {
        width: 50%;
        height: 300upx;
    }

    .sw-img {
        width: 100upx;
        height: 100upx;
    }

    .tjbz_cont {
        width: 97%;
        margin: 8px auto;
        border: 1px solid #ccc;
        border-radius: 5px;
        position: relative;
        box-shadow: 2px 2px 4px #ccc;
    }

    .tjbz_img {
        position: absolute;
        right: 0;
        top: 0;
    }

    .num_left {
        width: 45%;
        float: left;
        font-weight: bold;
    }

    .num_right {
        width: 55%;
        float: left;
    }

    .more {
        /* margin-top: 20px; */
        font-size: 14px;
        font-weight: bold;
        text-align: center;
        color: #01BE6E;
    }
</style>