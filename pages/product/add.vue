<template>
	<view>
		<form @submit="formSubmit">
			<view class="input-flex">
				<view class="input-flex-label">名称</view>
				<input type="text" name="name" class="input-flex-text" id="truename">
			</view>
			<button form-type="submit" class="btn-row-submit">保存</button>
		</form>
	</view>
</template>

<script>
	import api from '../../api/product.js';
	export default {
		data: function() {
			return {
				
			}

		},
		onLoad: function(option) {
			uni.setNavigationBarTitle({
				title: '产品添加'
			});

		},
		methods: {
			formSubmit: function(e) {
				let that = this;
				console.log(e)
				api.saveProduct(e.detail.value).then(res => {
					console.log(res)
					uni.showToast({
						"title": res.message
					})
					
					setTimeout(function() {
						let pages = getCurrentPages();  //获取所有页面栈实例列表
						let nowPage = pages[ pages.length - 1];  //当前页页面实例
						let prevPage = pages[ pages.length - 2 ];  //上一页页面实例
						uni.navigateBack({  //uni.navigateTo跳转的返回，默认1为返回上一级
							delta: 1
						});
					}, 600)
				})
			}
		}
	}
</script>

<style>
</style>
