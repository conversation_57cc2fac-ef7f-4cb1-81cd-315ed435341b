<template>
	<view>
		<view class="fixBt" @click="onAdd()">添加</view>
		<view v-if="list.length==0">
			<view class="emptyData">暂无数据</view>
		</view>
		<view v-else>
			<view class="row-box mgb-10" v-for="(item,index) in list" :key="index">
				<view class="flex-table">
					<view class="flex-table-label">名称</view>
					<view class="flex-table-box">{{item.name}}</view>
				</view>
				<view class="flex-table">

					<view class="flex-table-label">分类 </view>
					<view class="flex-table-box">{{item.productSortCode}} </view>
				</view>
				<view class="pd-10 flex flex-center">
					<view class="btn-small btn-outline-primary mgr-10" @click="onEdit(item.id)">编辑</view>
					<view class="btn-small btn-outline-danger" @click="onDel(item.id)">删除</view>
				</view>
			</view>
			<text class="loadMore" v-if="loading">{{loadMoreText}}</text>
		</view>


	</view>
</template>

<script>
	import api from '../../api/product.js';
	export default {
		data: function() {
			return {
				list: [],
				total: 0, //总数
				loadMoreText: "加载更多",
				refreshing: false, //为true表示正在刷新
				fetchPageNum: 1, //当前页数
				loading: true, // 为true表示加载中
			}

		},
		onLoad: function(option) {
			uni.setNavigationBarTitle({
				title: '维护产品信息'
			});
		},
		onShow: function() {
			this.list=[];
			this.fetchPageNum=1;
			this.refreshing = true;
			this.getData();
		},
		onReachBottom() {
			console.log('到底部加载')
			if (this.list.length < this.total) {
				this.getData();
			} else {
				this.loadMoreText = "暂无更多"
			}

		},
		onPullDownRefresh() {
			console.log('下拉刷新');
			this.refreshing = true;
			this.getData();
		},
		methods: {
			getData: function() {
				let that = this;
				that.loading = true;
				let param = {
					name: ""
				};
				let pageNo = that.refreshing ? 1 : that.fetchPageNum;
				api.findProductList(pageNo, param).then(res => {
					if (res.code != 0) {
						uni.showToast({
							title: "请求数据失败请重试"
						})
						return;
					} else {
						that.total = res.data.count;
						if (that.refreshing) {
							that.refreshing = false;
							uni.stopPullDownRefresh();
							that.list = res.data.list;
							that.fetchPageNum = res.data.next;
						} else {
							that.list = that.list.concat(res.data.list);
							that.fetchPageNum = res.data.next;
						}
					}
				})
			},
			onAdd: function() {
				uni.navigateTo({
					url: '../product/product-add',
				})
			},
			onEdit: function(id) {
				uni.navigateTo({ 
					url: "../product/edit?id=" + id,
				})
			},
			onDel: function(id) {
				let that = this;
				uni.showModal({
					title: '提示',
					content: '确认删除?',
					showCancel: true,
					success: (res) => {
						if (res.confirm) {
							api.deleteProduct(id).then(res => {
								uni.showToast({
									"title": res.message
								})
								setTimeout(function() {
									that.list=[];
									that.fetchPageNum=1;
									that.refreshing = true;
									that.getData();
								}, 600)
							})
						}
					}
				});
			}
		}
	}
</script>

<style>
	.fixBt {
		position: fixed;
		width: 120upx;
		height: 120upx;
		text-align: center;
		line-height: 120upx;
		border-radius: 50%;
		background-color: #007AFF;
		color: #fff;
		right: 5upx;
		bottom: 110upx;
	}
</style>
