<template>
    <view>
        <div class="content_all">
            <div class="whxx_all">
                <div class="whxx_left">
                    产品名称:
                </div>
                <div class="whxx_right">
                    <view>
                        <text>{{product.name}}</text>
                    </view>
                </div>
            </div>
            <div class="whxx_all">
                <div class="whxx_left">
                    产品分类:
                </div>
                <div class="whxx_right">
                    <text>{{product.productSortName}}</text>
                </div>
            </div>
            <div class="whxx_all">
                <div class="whxx_left">
                    生产地址:
                </div>
                <div class="whxx_right">
                    <text>{{product.detail}}</text>
                </div>
            </div>
            <div class="whxx_all">
                <div class="whxx_left">
                    复购商品:
                </div>
                <div class="whxx_right">
                    <text>{{product.reBuyProductName}}</text>
                </div>
            </div>
            <div class="whxx_all">
                <div class="whxx_left">
                    <p style="float: left;">复购直通车:</p>
                    <div class="fg_icon" @click="open_reBuy">
                        <image src="../../static/img/help.png"></image>
                    </div>
                </div>
                <div class="whxx_right" style="padding: 0;">
                    <switch :checked="fg_switch" :disabled="true"
                        style="transform: scale(0.6,0.6); float: right; margin-top: 5px;" />
                </div>
            </div>
            <div class="whxx_all" style="border-bottom: none;">
                <div class="whxx_left">
                    产品介绍:
                </div>
            </div>
            <div class="whxx_all_hist">
                <view style="word-break:break-all;margin-top:">
                    <text style="font-size: 12px; color: #888888;padding:10px 0;">{{product.productIntroduction}}</text>
                </view>
            </div>
            <div class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
                <span class="tit_left">产品照片:</span>
            </div>

            <view class="whxx_all_hist" style="margin:10px auto;">
                <view class="pboto_right" style="margin-bottom:10px;width: 90%;margin-left: 5%;">
                    <view style="width:100%;">
                        <swiperImg :swiperList="pictureUrlView" :indicatorDots="true" :autoplay="true" :interval="2000"
                            :duration="500" :mode="imageMode" />
                    </view>
                </view>
            </view>
            <div class="whxx_all" style="margin-top: 10px;overflow-y: hidden;margin-bottom: 10px;">
                <div class="whxx_left">
                    产品认证:
                </div>
                <div class="whxx_right" style="padding: 0;">
                  <view v-for="(item,index) in CertificationName" :key="index"
                        style="word-break:break-all;margin-top: 10px;">
                    <text style="font-size: 13px; color: #888888;padding:10px 0;">{{item}}</text>
                  </view>
                </div>

            </div>

            <div class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
                <span class="tit_left">认证证书:</span>
            </div>
            <view class="whxx_all_hist" style="margin:10px auto;">
                <view class="pboto_right" style="margin-bottom:10px;width: 90%;margin-left: 5%;">
                    <view style="width:100%;">
                        <swiperImg :swiperList="CertificationUrlView" :indicatorDots="true" :autoplay="true"
                            :interval="2000" :duration="500" :mode="imageMode" />
                    </view>
                </view>
            </view>

            <div v-if="productStorageList.length>0" class="whxx_all_hist" style="margin-top:10px;">
                <div style="font-weight: bold;font-size: 14px;">推荐保质方式:</div>
                <div>
                    <div v-for="(item,index) in productStorageList" :key="index" class="tjbz_cont">
                        <div class="whxx_all_hist">
                            <div style="padding: 10px 0;overflow: hidden;">
                                <p style="font-weight: bold;font-size: 14px;">推荐保质方式{{index+1}}</p>
                            </div>
                        </div>
                        <div class="whxx_all">
                            <div class="whxx_left">
                                保存条件:
                            </div>
                            <div class="whxx_right">
                                {{item.storageTypeName}}
                            </div>
                        </div>
                        <div class="whxx_all">
                            <div class="whxx_left">
                                保存时间:
                            </div>
                            <div class="whxx_right">
                                {{item.storageDays}}{{item.storageUnit}}
                            </div>
                        </div>
                        <div class="whxx_all" style="margin-bottom: 10px;">
                            <div class="whxx_left">
                                补充说明:
                            </div>
                            <div class="whxx_right">
                                {{item.instruction}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="whxx_all">
                <div class="whxx_left">
                    电商链接:
                </div>
                <div class="whxx_right">
                    {{product.electricityLink}}
                </div>
            </div>
            <div class="whxx_all">
                <div class="whxx_left">
                    种养殖规模({{product.scaleUnitName}}):
                </div>
                <div class="whxx_right">
                    {{product.scaleAmount}}
                </div>
            </div>
            <div class="whxx_all">
                <div class="whxx_left">
                    年产值(万元):
                </div>
                <div class="whxx_right">
                    {{product.productionValue}}
                </div>
            </div>
            <div class="whxx_all">
                <div class="whxx_left">
                    年产量({{product.productionUnitName}}):
                </div>
                <div class="whxx_right">
                    {{product.productionAmount}}
                </div>
            </div>

            <div style="margin-top:20px;margin-bottom: 20px;">
                <button @click="updateInfo" type="primary"
                    style="width:100px;height:35px;line-height:35px;font-size:14px;color: #fff;background-color: #02994f">修改信息</button>
            </div>
        </div>
        <uni-popup ref="popup" type="center">
            <div class="re-buy-main">
                <div class="title"><span @click="close_reBuy">
                        <image src="../../static/img/close.png" mode="widthFix"></image>
                    </span></spam>复购直通车说明</div>
                <div class="info">
                    <p>开启复购直通车后，</p>
                    <p>消费者可在合格证扫码页购买您的产品</p>
                </div>
                <div class="img">
                    <image src="https://jlsyncphgzqn.jikeruan.com/img/ynyp_alert.png" mode="widthFix"></image>
                </div>
            </div>
        </uni-popup>
    </view>
</template>

<script>
    import api from '../../api/product.js';
    import dictApi from '../../api/dict.js';
    import productStorageApi from '../../api/productStorage.js';
    import ckUploadImg from '@/components/ck-uploadImg/ck-uploadImg.vue';
    import swiperImg from '@/components/zhtx-swiper/swiper.vue'
    export default {
        components: {
            ckUploadImg,
            swiperImg
        },
        data() {
            return {
                form: {
                    name: '',
                    address: '',
                    entId: '',
                    productSortCode: '',
                    productSortName: '',
                    province: '',
                    city: '',
                    county: '',
                    longitude: '',
                    latitude: '',
                    productIntroduction: '',
                    productCertificationCode: '',
                    productCertificationName: ''
                },
                pictureUrlView: [],
                pictureUrl: [],
                pictureFileList: [],
                CertificationUrlView: [],
                CertificationUrl: [],
                CertificationFileList: [],
                CertificationName: [],
                productCertification: [],
                product: {
                    electricityLink: "",
                    scaleUnitName: "",
                    scaleAmount: "",
                    productionValue: "",
                    productionUnitName: "",
                    productionAmount: "",
                },
                productStorageList: [],
                imageMode: "aspectFit",
                fg_switch: 0
            }
        },
        onLoad: function(option) {
            var that = this;
            that.pageData = JSON.parse(decodeURIComponent(option.textObj))
            that.form = that.pageData
            that.fg_switch = parseInt(that.pageData.reBuyVisible)
            uni.setNavigationBarTitle({
                title: '产品信息'
            });
        },
        onShow: function() {
            var that = this;
            that.pictureUrlView = []
            that.pictureUrl = []
            that.pictureFileList = []
            that.CertificationUrlView = []
            that.CertificationUrl = []
            that.CertificationFileList = []
            that.getData();
            that.findAttachment();
        },
        methods: {
            open_reBuy() {
                this.$refs.popup.open('center')
            },
            close_reBuy() {
                this.$refs.popup.close()
            },
            getData: function() {
                let that = this;
                that.product = {
                    electricityLink: "",
                    scaleUnitName: "",
                    scaleAmount: "",
                    productionValue: "",
                    productionUnitName: "",
                    productionAmount: "",
                };
                api.getProduct(that.form.id).then(res => {
                    console.log(res)
                    if (res.code != 0) {
                        uni.showToast({
                            title: "请求数据失败请重试"
                        })
                        return;
                    } else {
                        that.product = res.data;
                        that.CertificationName = that.product.productCertificationName.split(',');
                        that.getStorageData(that.product.id);
                    }
                })
            },
            getStorageData: function(productId) {
                let that = this;
                let param = {
                    productId: productId,
                };
                productStorageApi.findList(param).then(res => {
                    if (res.code != 0) {
                        uni.showToast({
                            title: "请求数据失败请重试"
                        })
                        return;
                    }
                    if (res.data) {
                        that.productStorageList = res.data
                    } else {
                        that.productStorageList = []
                    }
                })
            },
            updateInfo() {
                var that = this;
                var textObj = encodeURIComponent(JSON.stringify(that.product))
                uni.navigateTo({
                    url: '../product/product-edit?textObj=' + textObj,
                })
            },
            findAttachment() {
                var that = this;
                api.findProductAttachment({
                    id: that.form.id
                }).then(res => {
                    if (res.data.fileList) {
                        res.data.fileList.forEach((item) => {
                            let swiper = {};
                            if (item.fileType == 'productPicture') {
                                swiper.img = item.fileUrl;
                                that.pictureUrlView.push(swiper);
                                that.pictureUrl.push(item.fileUrl)
                                that.pictureFileList.push(item)
                            } else if (item.fileType == 'productCertPic') {
                                swiper.img = item.fileUrl;
                                that.CertificationUrlView.push(swiper);
                                that.CertificationUrl.push(item.fileUrl)
                                that.CertificationFileList.push(item)
                            }
                        })
                    }
                })
            },
        }
    }
</script>

<style scoped>
    .tjbz_cont {
        width: 97%;
        margin: 8px auto;
        border: 1px solid #ccc;
        border-radius: 5px;
        position: relative;
        box-shadow: 2px 2px 4px #ccc;
    }

    .tjbz_img {
        position: absolute;
        right: 0;
        top: 0;
    }

    .num_left {
        width: 45%;
        float: left;
        font-weight: bold;
    }

    .num_right {
        width: 55%;
        float: left;
    }
</style>