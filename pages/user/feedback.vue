<template>
	<view>
		<form @submit="formSubmit">
			<div class="content_all">
				<div style="color: #02994F;">
					请描述具体问题:
				</div>
				<div>
					<textarea style="width: 96%;margin: 10px auto;border: 1px solid #e0e0e0;" placeholder="请描述您的问题与建议" name="content"
					 maxlength="200" />
				</div>
				<div style="color: #02994F;">
					联系电话:
				</div>
				<div>
					<input class="uni-input" maxlength="20" placeholder="请输入您的联系电话" id="phone" name="phone" :value="phone"/>
				</div>
				<div style="margin-top: 50px;margin-bottom: 50px;">
					<button form-type="submit" type="primary" style="width:100px;height:35px;line-height:35px;font-size:14px;color: #fff;background-color: #02994f;" :disabled="submitDisabled">提交反馈</button>
				</div>
			</div>
		</form>
	</view>
</template>

<script>
	import api from '../../api/feedback.js';
	var  graceChecker = require("../../common/graceui-dataChecker/graceChecker.js");
	export default {
		data() {
			return {
				memberId:'',
				phone:'',
				submitDisabled:false,//防止重复提交
			}
		},
		onLoad: function(option) {
			let userInfoStorage = uni.getStorageSync('userInfo');
			this.memberId = userInfoStorage.id;
			this.phone = userInfoStorage.phoneNumber;
		},
		methods: {
			formSubmit: function(e) {
				var rule = [
					{ name: "content", checkType: "string", checkRule: "1,200", errorMsg: "具体问题长度为1-200位" },
					{ name: "phone", checkType: "phoneAndTel", checkRule: "", errorMsg: "请填写正确的联系电话" },
				];
				let that = this;
				let feedback = e.detail.value;
				feedback.memberId = that.memberId;
				var checkRes = graceChecker.check(feedback, rule);
				if(!checkRes){
					uni.showToast({ title: graceChecker.error, icon: "none" });
					return;
				}
				that.submitDisabled = true;
				uni.showLoading({
					title: '信息提交中',
					mask:true,
				});
				api.saveFeedback(feedback).then(res => {
					if(res.code == 0){
						uni.showToast({
							//icon:'none',
							title: '信息提交成功',
							duration: 2000
						});
						setTimeout(()=>{
							uni.hideLoading();
							uni.reLaunch({
								url: '/pages/user/user',
							});
						},2000);
					}
				})
			},
		}
	}
</script>
