<template>
	<view class="" >
		<view v-if="!token" class="my_top">
			<view class="my_photo">
				<view class="my_phtot_img">
					<image :src="imgPath+'img/index/img18.png'" alt="" style="width: 80px;height: 80px;border-radius: 40px;"/>
				</view>
			</view>
			<view class="my_phtot_p" @tap="gotoLogin">
				<p style="font-size: 16px;font-weight: bold;width: 90%;margin: 0 auto;">
					点击登录
				</p>
			</view>
		</view>
		<view v-else>
			<view v-if="showFlag" class="my_top">
				<view class="my_photo">
					<view class="my_phtot_img">
						<image :src="headImg" alt="" style="width: 80px;height: 80px;border-radius: 40px;"/>
					</view>
				</view>
				<view class="my_phtot_p">
					<p style="font-size: 16px;font-weight: bold;width: 90%;margin: 0 auto;">
						{{name|nameFormat}}
					</p>
					<p style="font-size: 14px; width: 90%;margin: 0 auto;padding-top:8px;">
						{{detail}}
					</p>
				</view>
			</view>
			<view v-else class="my_top">
				<view class="my_photo">
					<view class="my_phtot_img">
						<image :src="imgPath+'img/index/img18.png'" alt="" style="width: 80px;height: 80px;border-radius: 40px;"/>
					</view>
				</view>
				<view class="my_phtot_p">
					<p style="font-size: 16px;font-weight: bold;width: 90%;margin: 0 auto;">
						未维护主体信息
					</p>
				</view>
			</view>
			<view class="my_list">
				<ul>
			
					<li @tap="toCheckPhone">
						<p>更换手机号</p>
						<!-- <image :src="imgPath+'img/img22.png'" alt="" style="height: 15px;"/> -->
					</li>
					<li @tap="toToast()">
						<p>密码修改</p>
						<!-- <image :src="imgPath+'img/img22.png'" alt="" style="height: 15px;"/> -->
					</li>
					<li @tap="toFeedback()">
						<p>意见反馈</p>
						<!-- <image :src="imgPath+'img/img22.png'" alt="" style="height: 15px;"/> -->
					</li>
					<li @tap="toEntChange()" v-if="ent.businessType !== '2'">
						<p>主体信息变更申请</p>
						<!-- <image :src="imgPath+'img/img22.png'" alt="" style="height: 15px;"/> -->
					</li>
				</ul>
			</view>
			<view class="tcdl">
				<!-- 		    <input type="button" value="退出登录" @tap="bindLogout" style="margin-left: 35%;">
			     -->			<button type="primary" style="color: #fff;background-color: #02994f;" @tap="bindLogout">退出登录</button>
			</view>
		</view>
		<tarbar current="3"></tarbar>
	</view>

</template>

<script>
	import api from '../../api/user.js';
	import {
		mapState,
		mapMutations
	} from 'vuex'
  import Tarbar from "../../components/tabbar/tarbar.vue";
	var config = require('../../common/config.js')
	export default {
    components: {Tarbar},
		computed: {
			...mapState(['userInfo'])
		},
		data() {
			return {
				imgPath:config.IMG_PATH,
				showFlag:false,
				name:"",
				detail:"",
				headImg:"",
				token:uni.getStorageSync("token"),
        ent: uni.getStorageSync("ent")
			}
		},
		onShow() {
			console.log("onShow")
			if(!this.token){
				return ;
			}
			let that=this;
			let ent=uni.getStorageSync("ent");
			if(ent){
        that.ent = ent;
				that.name=ent.name;
				that.detail=ent.detail;
				let wechatUser=uni.getStorageSync("wechatUser");
				that.headImg=that.imgPath+"img/index/img18.png";
				if(wechatUser){
					that.headImg=wechatUser.avatarUrl;
				}
				that.showFlag=true;
			}
			
		},
		onLoad() {
			console.log("onLoad")
		},
		onReady(){
			console.log("onReady")
		},
		onTabItemTap(e) {
			// tab 点击时执行，此处直接接收单击事件
			console.log(e)
		},
		created(){
			
		},
		methods: {
			...mapMutations(['logout']),
			bindLogout() {
				uni.showLoading({
					title: '退出中',
					mask:true,
				});

				api.loginOut().then(res => {
					this.logout();
					/* uni.reLaunch({
						url: '../login/index',
					}); */
					uni.hideLoading();
					this.$Router.replaceAll("/pages/index/index")
				})
			},
			getUser() {
				api.getUser().then(res => {
					uni.showToast({
						title: res.data.name + ":" + res.data.id,
					})
				})
			},
			toCheckPhone(){
				/* uni.navigateTo({
					url: '../user/checkOldPhone',
				}); */
				if(!this.verifyFrozen()){
					return ;
				}
				this.$Router.push("/pages/user/checkOldPhone")
			},
			toToast(){
				uni.showToast({
					icon:'none',
					title: '功能正在努力开发中，敬请期待',
					duration: 2000
				});
			},
			gotoLogin() {
				uni.navigateTo({
					url: '../login/index'
				});
			},
			toFeedback(){
				if(!this.verifyFrozen()){
					return ;
				}
				this.$Router.push("/pages/user/feedback")
			},
			toEntChange(){
				let ent=uni.getStorageSync("ent");
				if (ent.examineStatus != '1'){
					uni.showModal({
						title: '系统提示',
						content: '主体信息审核通过后才可申请',
						mask: true,
						showCancel:false,
						success: (res) => {
							
						}
					});
					return ;
				}
				if(!this.verifyFrozen()){
					return ;
				}
				uni.showModal({
				    title: '系统提示',
				    content: '主体信息变更后需重新提交至当地业务主管部门进行审核，审核通过后方可进行开证操作。',
					mask: true,
				    success: function (res) {
				        if (res.confirm) {
							uni.navigateTo({
								url: '/pages/entChange/chooseType',
							})
				        } 
				    }
				});
			},
			verifyFrozen(){
				//验证主体冻结状态
				let that = this;
				let ent = uni.getStorageSync("ent");
				if (ent.frozenFlag == '1'){
					uni.showModal({
						icon: "none",
						content: '您的账号已冻结，请联系技术服务人员取消冻结',
						mask: true,
						showCancel:false,
						success: function (res) {
							that.$Router.replaceAll("/pages/index/index")
						}
					});
					return false;
				}
				return true;
			}
		},
		filters: {
			nameFormat(value) {
				if(value.length>20){
					let start = value.slice(0, 15)
					return `${start}...`
				}
				return value
			},
			phoneFormat(value) {
				let start = value.slice(0, 3)
				let end = value.slice(-4)
				return `${start}****${end}`
			}
		},
	}
</script>

<style>

</style>
