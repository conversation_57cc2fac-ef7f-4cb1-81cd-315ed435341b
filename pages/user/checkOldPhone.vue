<template>
	<view class="">
		<image :src="imgPath+'img/kjjl.png'" alt="" class="bg_img" />
		<view v-if="showFlag" class="my_top">
		    <view class="my_photo">
		        <view class="my_phtot_img">
		            <image :src="headImg" alt="" style="width: 80px;height: 80px;border-radius: 40px;"/>
		        </view>
		    </view>
		    <view class="my_phtot_p">
		        <p style="font-size: 16px;font-weight: bold;width: 90%;margin: 0 auto;">
		            {{name|nameFormat}}
		        </p>
		        <p>
		            {{detail}}
		        </p>
		    </view>
		</view>
		<view v-else class="my_top">
			<view class="my_photo">
				<view class="my_phtot_img">
					<image :src="imgPath+'img/index/img18.png'" alt="" style="width: 80px;height: 80px;border-radius: 40px;"/>
				</view>
			</view>
			<view class="my_phtot_p">
				<p style="font-size: 16px;font-weight: bold;width: 90%;margin: 0 auto;">
					未维护主体信息
				</p>
			</view>
		</view>
		<view style="color: grey;margin-left: 10%;">
			<view>请输入{{phoneNumber|phoneFormat}}收到的短信验证码</view>
		</view>
		<view class="btn-row">
			<view class="input-row">
				<image :src="imgPath+'img/icon2.png'" style="width: 15px;height: 22px;">
				<m-input class="m-input-yzm" type="text" v-model="captcha" placeholder="输入验证码"></m-input>
				<button type="default" size="mini" @tap="onLiderVerify" :disabled="disabled" class="yzm">{{countdownText}}</button>
			</view>
			
			<slider-verify :isShow="sliderVerifyFLag" @touchSliderResult="verifyResult" ref="verifyElement"></slider-verify>
			<view>
				<button type="primary" style="width:100px;height:35px;line-height:35px;font-size:14px;margin-top: 10px;color: #fff;background-color: #02994f;" @tap="toUpdateNewPhone">下一步</button>
			</view>
		</view>
	</view>
</template>

<script>
	import api from '../../api/login.js';
	import userApi from '../../api/user.js';
	import mInput from '../../components/m-input.vue'
	import sliderVerify from '@/components/slider-verify/slider-verify.vue';
	var  graceChecker = require("../../common/graceui-dataChecker/graceChecker.js");
	var config = require('../../common/config.js')
	export default {
		components: {
			mInput,
			'slider-verify': sliderVerify,
		},
		data() {
			return {
				imgPath:config.IMG_PATH,
				showFlag:false,
				name:"",
				detail:"",
				headImg:"",
				phoneNumber: '',
				captcha: '',
				countdown: 60,
				disabled: false,
				countdownText: "获取验证码",
				timer: '',
				sliderVerifyFLag: false ,//滑块验证,
			}
		},
		onLoad() {
			this.phoneNumber = uni.getStorageSync("userInfo").phoneNumber;
		},
		created(){
			console.log("created");
			let that=this;
			let ent=uni.getStorageSync("ent");
			if(ent){
				that.name=ent.name;
				that.detail=ent.detail;
				let wechatUser=uni.getStorageSync("wechatUser");
				that.headImg=that.imgPath+"img/index/img18.png";
				if(wechatUser){
					that.headImg=wechatUser.avatarUrl;
				}
				that.showFlag=true;
			}
			
		},
		methods: {
			onGetCaptcha() {
				let rule = [
				    { name: "phoneNumber", checkType: "notnull", errorMsg: "请填写手机号" },
					{ name: "phoneNumber", checkType: "phoneno", errorMsg: "手机号格式错误" },
				];
				let validateData={
					phoneNumber:this.phoneNumber
				}
				let checkRes = graceChecker.check(validateData, rule);
				if(!checkRes){
					uni.showToast({ title: graceChecker.error, icon: "none" });
					return;
				}
				
				let that = this;
				return new Promise((resolve, reject) => {
					that.disabled = true; //禁用点击
					api.getCaptcha(that.phoneNumber).then(res => {
						if (res.code == 1) {
							uni.showToast({
								icon: 'none',
								title: res.message
							});
							return;
						}
						that.captcha = res.data;
						that.countdownText = that.countdown + "秒后可重试";
						that.timer = setInterval(that.onCountDown, 1000);
						resolve();
					})

				})
			},
			// 倒计时
			onCountDown() {
				let that = this;
				if (that.countdown <= 1) {
					that.disabled = false;
					that.countdown = 60;
					that.countdownText = '获取验证码';
					clearInterval(that.timer);
				} else {
					that.countdownText = --that.countdown + "秒后可重试";
				}
			},
			toUpdateNewPhone() {
				uni.showLoading({
				    title: '加载中',
					mask:true,
				});
				let rule = [
				    { name: "phoneNumber", checkType: "notnull", errorMsg: "请填写手机号" },
					{ name: "phoneNumber", checkType: "phoneno", errorMsg: "手机号格式错误" },
					{ name: "captcha", checkType: "notnull", errorMsg: "请填写验证码" },
					{ name: "captcha", checkType: "captcha", errorMsg: "请输入4位数字验证码" },
				];
				let validateData={
					phoneNumber:this.phoneNumber,
					captcha:this.captcha
				}
				let checkRes = graceChecker.check(validateData, rule);
				
				if(!checkRes){
					uni.hideLoading();
					uni.showToast({ title: graceChecker.error, icon: "none" });
					return;
				}
				
				let param = {
					phoneNumber: this.phoneNumber,
					captcha: this.captcha,
				}
				userApi.checkCaptcha(param).then(res => {
					if (res.code == 1) {
						uni.hideLoading();
						uni.showToast({
							icon: 'none',
							title: res.message
						});
						return;
					}
					uni.hideLoading();
					this.$Router.push("/pages/user/editPhone")
				})
			},
			onLiderVerify(){
				let rule = [
				    { name: "phoneNumber", checkType: "notnull", errorMsg: "请填写手机号" },
					{ name: "phoneNumber", checkType: "phoneno", errorMsg: "手机号格式错误" },
				];
				let validateData={
					phoneNumber:this.phoneNumber,
				}
				let checkRes = graceChecker.check(validateData, rule);
				
				if(!checkRes){
					uni.showToast({ title: graceChecker.error, icon: "none" });
					return;
				}
				
				this.sliderVerifyFLag = true;
			},
			verifyResult(res) {
				this.sliderVerifyFLag = false;
				if (res) { 
					//校验通过
					this.onGetCaptcha();
				} else {
					// 校验失败,点击关闭按钮
					/* uni.showToast({
						icon: 'none',
						title: "滑动验证失败",
						mask: true,
						duration: 1500
					}); */
				}
			}
		},
		filters: {
			nameFormat(value) {
				if(value.length>20){
					let start = value.slice(0, 15)
					return `${start}...`
				}
				return value
			},
			phoneFormat(value) {
				let start = value.slice(0, 3)
				let end = value.slice(-4)
				return `${start}****${end}`
			}
		},
	}
</script>

<style lang="scss" scoped>
	.input-row {
		width: 100%;
		height: 50px;
		border-bottom: 1px solid #eeeeee;

		image {
			float: left;
			width: 15px;
			height: 22px;
			margin-top: 14px;
		}

		.m-input {
			float: left;
			width: 75%;
			height: 50px;
			line-height: 50px;
		}

		.m-input-yzm {
			float: left;
			width: 45%;
			height: 50px;
			line-height: 50px;
		}

		.yzm {
			float: right;
			/* width: 35%; */
			/* height: 50px;
			line-height: 50px; */
			background: none;
			border: none;
			font-size: 14px;
			/* color:#174cbb ; */
			margin-top: 10px;

		}
	}

</style>
