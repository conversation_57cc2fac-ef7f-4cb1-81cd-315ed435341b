<template>
	<view>
		<web-view :src="shopUrl"></web-view>
	</view>
</template>

<script>
	var config = require('../../common/config.js')
	export default {
		data() {
			return {
				shopUrl: config.SHOP_PATH,
			}
		},
		onLoad(options) {
			this.shopUrl+=uni.getStorageSync("userInfo").id;
			this.shopUrl+="/"+options.param;
			this.shopUrl+="?m="+new Date().getTime();;
			console.log(this.shopUrl);
		},
	}
</script>

<style>
</style>
