<template>
    <view v-if="showFlag">
        <form @submit="formSubmit">
            <div class="content_all">
                <div class="whxx_all">
                    <div class="whxx_left">
                        <span>*</span>产品名称:
                    </div>
                    <div class="whxx_right">
                        <picker name="productSortCode" @change="bindPickerChange" :value="index" :range="array"
                            :range-key="'name'">
                            <view class="uni-input">
                                {{array[index]?array[index].name:'请选择产品名称'}}
                            </view>
                        </picker>
                    </div>
                </div>


                <div class="whxx_all" style="display: flex;align-items: center;">
                    <div class="whxx_left">
                        <div style="float: left;">复购直通车:</div>
                        <div class="fg_icon" @click="open_reBuy">
                            <image src="../../static/img/help.png"></image>
                        </div>
                    </div>
                    <div class="whxx_right" style="min-height: 36px;padding: 0;">
                        <span @click="checkSwitch">
                            <switch :checked="fgShow" height="10" @change="changeSwitch" :disabled="fgDisabled"
                                style="transform: scale(0.6,0.6); float: right; margin-top: 3px;" />
                        </span>

                    </div>
                </div>
                <div class="whxx_all" style="display: flex;align-items: center;" v-if="fgShow">
                    <div class="whxx_left">
                        <span></span>复购商品:
                    </div>
                    <div class="whxx_right" style="display: flex;align-items: center;">
                        {{form.reBuyProductName?form.reBuyProductName:'未关联商品'}}
                    </div>
                </div>


                <div class="whxx_all">
                    <div class="whxx_left">
                        <span>*</span>产品数量/重量:
                    </div>
                    <div class="whxx_right">
                        <input class="uni-input" maxlength="20" name="productNum" type="digit" model="form.productNum"
                            placeholder="请输入产品数量/重量">
                    </div>
                </div>
                <div class="whxx_all">
                    <div class="whxx_left">
                        <span>*</span>产品单位:
                    </div>
                    <div class="whxx_right">
                        <picker name="productUnitCode" @change="bindDictPickerChange" :value="indexForDict"
                            :range="dictArray" :range-key="'label'">
                            <view class="uni-input">
                                {{dictArray[indexForDict]?dictArray[indexForDict].label:'请选择产品单位'}}
                            </view>
                        </picker>
                    </div>
                </div>
                <div class="whxx_all">
                    <div class="whxx_left">
                        <span>*</span>生产日期:
                    </div>
                    <div class="whxx_right">
                        <biaofun-datetime-picker placeholder="请选择生产日期" fields="day" :end="endDateFun"
                            :defaultValue="endDateFun" @change="changeDatetimePicker"></biaofun-datetime-picker>
                    </div>
                </div>
<!--                <div class="whxx_all" v-if="form.productSortCode !== '33'">
                    <div class="whxx_left">
                        <span>*</span>开具数量:
                    </div>
                    <div class="whxx_right">
                        <input class="uni-input" maxlength="4" name="printCount" type="number" :value="form.printCount"
                            placeholder="请输入开具数量">
                    </div>
                </div>-->
                <!-- <div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>开具日期:
					</div>
					<div class="whxx_right">
						{{date}}
					</div>
				</div> -->
                <div class="whxx_all" v-if="inspectionSituationEnbale">
                    <div class="whxx_left">
                        <span>*</span>承诺依据:
                    </div>
                    <div class="whxx_right">
                        <checkbox-group @change="bindInspectionPickerChange">
                            <view class="news-item" v-for="(item,index) in inspectionArray" :key="index"
                                style="width: 100%;float: left;">
                                <label class="radio">
                                    <div style="margin: 0 auto 10px;">
                                        <checkbox :value="item.value+''"
                                            :checked="form.inspectionSituationList.indexOf(item.value)>=0 "
                                                  :disabled="form.productSortCode === '33' && item.value === 'is03'"
                                            style="float: left;transform:scale(0.5);margin-top: -5px;" />
                                        <p style="font-size: 12px;float: left;">{{item.label}}</p>
                                    </div>
                                </label>
                            </view>
                        </checkbox-group>
                    </div>
                </div>
                <div class="promise">
                    <div class="promise_top">
                        <view>
                            <evan-checkbox v-model="promiseFlag" primary-color="#09BB07">我承诺对生产销售的食用农产品:</evan-checkbox>
                        </view>
                    </div>
                    <p>1.不使用禁用农药兽药、停用兽药和非法添加物</p>
                    <p>2.常规农药兽药残留不超标</p>
                    <p>3.对承诺的真实性负责</p>
                </div>
                <div class="promise" v-if="promiseFlag">
                    <view v-if="autographFlag">
                        <view style="width:90%;margin-top:20px;margin-left:5%;margin-right:5%;">
                            <view style="display: flex;flex-direction:row;height:30px;">
                                <view style="width:45%;display: flex;flex-direction:row;">
                                    <view
                                        style="font-size:16px;color:#01BE6E;height:30px;line-height:30px;font-size:14px;">
                                        电子签名</view>
                                </view>
                            </view>
                        </view>
                        <view style="width:90%;margin-top:10px;margin-left:5%;margin-right:5%;">
                            <image :src="autographImg" style="width:100%;height:200px;" />
                        </view>
                    </view>
                </div>
                <div style="margin-bottom: 20px;">
                    <button form-type="submit" type="primary"
                        style="width:100px;height:35px;line-height:35px;font-size:14px;color: #fff;background-color: #02994f;">合格证开具</button>
                </div>
            </div>
        </form>
        <uni-popup ref="popup" type="center">
            <div class="re-buy-main">
                <div class="title"><span @click="close_reBuy">
                        <image src="../../static/img/close.png" mode="widthFix"></image>
                    </span>复购直通车说明</div>
                <div class="info">
                    <p>开启复购直通车后，</p>
                    <p>消费者可在合格证扫码页购买您的产品</p>
                </div>
                <div class="img">
                    <image src="https://jlsyncphgzqn.jikeruan.com/img/ynyp_alert.png" mode="widthFix"></image>
                </div>
            </div>
        </uni-popup>
    </view>
</template>

<script>
    import api from '../../api/certificate';
    import productInspectionApi from '@/api/productInspection.js';
    import BiaofunDatetimePicker from "../../components/biaofun-datetime-picker/biaofun-datetime-picker";
    import dictApi from "../../api/dict";
    import bluetooth from "../../utils/bluetooth";
    var graceChecker = require("../../common/graceui-dataChecker/graceChecker.js");
    var config = require('../../common/config.js')
    export default {
        components: {
            BiaofunDatetimePicker
        },
        data() {
            return {
                form: {
                    productId: "", //产品id
                    productName: "", //产品名称
                    productionDate: "", //生产日期
                    productNum: "", //请输入产品数量/重量
                    productUnitCode: "", //单位code
                    productUnitName: "", //单位名称
                    printCount: "", //打印数量
                    productDetail: "", //产地
                    inspectionSituation: "", //检测情况
                    productSortCode: "",
                    sampleNo: "",
                    inspectionSituationList: [], //承诺依据多选
                },
                date: new Date().toISOString().slice(0, 10),
                showFlag: false,
                array: [],
                dictArray: [],
                index: '',
                indexForDict: '',
                promiseFlag: false,
                autographFlag: false,
                autographImg: "",
                indexForInspection: '',
                inspectionArray: [],
                inspectionSituationEnbale: config.INSPECTION_SITUATION_ENABLED,
                fgShow: false,
                fgDisabled: true,
                fgSwitch: false
            };
        },
        computed: {
            endDateFun() {
                var year = new Date().getFullYear(); //年
                if (year < 1900) year = year + 1900;
                var month = new Date().getMonth() + 1; //月
                if (month < 10) month = '0' + month;
                var day = new Date().getDate(); //日
                if (day < 10) day = '0' + day;
                var hour = new Date().getHours(); //小时
                if (hour < 10) hour = '0' + hour;
                var minute = new Date().getMinutes(); //分钟
                if (minute < 10) minute = '0' + minute;
                var second = new Date().getSeconds(); //秒
                if (second < 10) second = '0' + second;
                var str = year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
                return str;
            }
        },
        methods: {
            open_reBuy() {
                this.$refs.popup.open('center')
            },
            close_reBuy() {
                this.$refs.popup.close()
            },
            checkSwitch() {
                if (this.form.productId == '') {
                    uni.showToast({
                        title: '请先选择产品',
                        icon: 'none'
                    })
                    return
                }
                if (this.fgSwitch == false) {
                    uni.showToast({
                        title: '本产品未开启直通车',
                        icon: 'none'
                    })
                    return
                }
                this.fgDisabled = false
            },
            changeSwitch(e) {
                this.fgShow = e.detail.value
                this.form.reBuyVisible = e.detail.value ? 1 : 0
            },
            bindPickerChange(e) {
                this.index = e.target.value;
                var selected = this.array[this.index]; //获取选中的数组
                this.form.productId = selected.id; //选中的id
                this.form.productName = selected.name; //选中的名字
                this.form.productDetail = selected.detail; //选中的名字
                this.form.productSortCode = selected.productSortCode; //分类code
                this.form.sampleNo = selected.currentSampleNo; //样品编号
                this.form.reBuyProductId = selected.reBuyProductId
                this.form.reBuyProductName = selected.reBuyProductName
                this.form.reBuyVisible = parseInt(selected.reBuyVisible)
                this.fgShow = this.form.reBuyVisible == 1 ? true : false
                this.fgSwitch = this.form.reBuyVisible == 1 ? true : false
                if (!this.form.fgSwitch) {
                    this.fgDisabled = true
                } else {
                    this.fgDisabled = false
                }

                //人参品类 必须有委托检测合格证书 -- by Wlq 20250115
                if (this.form.productSortCode === '33' && this.form.inspectionSituationList.indexOf("is03")<0) {
                  this.form.inspectionSituationList.push("is03")
                }


            },
            bindDictPickerChange(e) {
                this.indexForDict = e.target.value;
                var selected = this.dictArray[this.indexForDict]; //获取选中的数组
                this.form.productUnitCode = selected.value; //选中的id
                this.form.productUnitName = selected.label; //选中的名称
            },
            bindInspectionPickerChange(e) {
                //console.log(e);
                this.form.inspectionSituationList = e.target.value;
                let inspectionSituationList2 = []
                if (this.form.inspectionSituationList.length > 1) {
                    for (let i = 0; i < this.inspectionArray.length; i++) {
                        if (this.form.inspectionSituationList.indexOf(this.inspectionArray[i].value) >= 0) {
                            inspectionSituationList2.push(this.inspectionArray[i].value)
                        }
                    }
                    this.form.inspectionSituationList = inspectionSituationList2
                }

                /*
                this.indexForInspection = e.target.value;
                var selected = this.inspectionArray[this.indexForInspection];
                this.form.inspectionSituation = selected.value; */
            },
            changeDatetimePicker(res) {
                this.form.productionDate = res.f1;
            },
            getDict() {
                dictApi.findDict({
                    type: 'product_unit_code'
                }).then(res => {
                    if (res.code === 0) {
                        this.dictArray = res.data;
                    }
                })
            },
            getInspectionDict() {
                dictApi.findDict({
                    type: 'inspection_situation_code'
                }).then(res => {
                    if (res.code === 0) {
                        this.inspectionArray = res.data;
                        // if(!this.form.inspectionSituation){
                        // 	this.form.inspectionSituation = this.inspectionArray[0].value;
                        // }
                        if (this.form.inspectionSituationList.length == 0) {
                            this.form.inspectionSituationList.push(this.inspectionArray[0].value);
                        }
                    }
                })
            },
            getProduct() {
                let that = this
                api.findProducts().then(res => {
                    if (res.code === 0) {
                        if (!res.data) {
                            uni.showModal({
                                title: '提示',
                                content: '请先维护产品信息',
                                mask: true,
                                success: function(res) {
                                    if (res.confirm) {
                                        that.$Router.replaceAll("pages/product/product-list")
                                    } else if (res.cancel) {
                                        that.$Router.replaceAll("/pages/index/index")
                                    }
                                }
                            });

                        }
                        that.array = res.data;
                    }
                })
            },
            formSubmit(e) {
                let that = this;
                that.form.productNum = e.detail.value.productNum;
                that.form.printCount = "1"
                let rule = [{
                        name: "productId",
                        checkType: "notnull",
                        errorMsg: "请选择产品名称"
                    },
                    {
                        name: "productNum",
                        checkType: "notnull",
                        errorMsg: "请输入产品数量/重量"
                    },
                    {
                        name: "productNum",
                        checkType: "reg",
                        checkRule: "^[0-9]+([.]{1}[0-9]{1,2})?$",
                        errorMsg: "产品数量/重量格式错误:限制带2位小数的数字"
                    },
                    {
                        name: "productUnitCode",
                        checkType: "notnull",
                        errorMsg: "请选择产品单位"
                    },
                    {
                        name: "productionDate",
                        checkType: "notnull",
                        errorMsg: "请选择生产日期"
                    },
                ];
                let validateData = {
                    productId: that.form.productId,
                    productionDate: that.form.productionDate,
                    productNum: that.form.productNum,
                    productUnitCode: that.form.productUnitCode,
                    inspectionSituation: that.form.inspectionSituation,
                }
                let checkRes = graceChecker.check(validateData, rule);
                if (!checkRes) {
                    uni.showToast({
                        title: graceChecker.error,
                        icon: "none",
                        duration: 1500,
                        success() {
                            setTimeout(() => {}, 1500);
                        }
                    });
                    return;
                }
                if (parseFloat(that.form.productNum) == 0) {
                    uni.showToast({
                        title: "您输入的产品数量/重量为0，请重新输入",
                        icon: "none",
                        duration: 1500,
                        success() {
                            setTimeout(() => {}, 1500);
                        }
                    });
                    return;
                }
                if (that.inspectionSituationEnbale) {
                    if (!that.form.inspectionSituationList || that.form.inspectionSituationList.length == 0) {
                        uni.showToast({
                            title: '请选择承诺依据',
                            icon: "none",
                            duration: 1500,
                            success() {
                                setTimeout(() => {}, 1500);
                            }
                        });
                        return;
                    }
                }
                if (!that.promiseFlag) {
                    uni.showToast({
                        title: "请勾选承诺书",
                        icon: "none",
                        duration: 1500,
                        success() {
                            setTimeout(() => {}, 1500);
                        }
                    });
                    return;
                }

                /**
                 * 新需求
                 * 人参类产品的检测情况：除了选择“无检测信息”时，需要校验检测报告是否存在
                 * 1、不存在检测报告，给出提示，确认默认选择“无检测信息”
                 * 2、无通过的检测报告，给出提示,确认默认选择“无检测信息”
                 * 3、有通过的检测报告，直接进入下页面
                 * 2021年3月9日19:55:10
                 * lxy
                 */
                /* if(that.form.productSortCode=="33" && that.form.inspectionSituation!="is01"){
                	let param={
                		"productId":that.form.productId,
                		"inspectionSituation":that.form.inspectionSituation,
                	}
                	productInspectionApi.findNewestList(param).then(res => {
                		if(res.code==1){
                			uni.showToast({
                				icon: 'none',
                				title: res.message
                			});
                			return;
                		}
                		let resultList=res.data;
                		if(resultList==null || resultList.length==0){
                			uni.showModal({
                				icon: "none",
                				title: '系统提示',
                				content: '未查询到检测结果，是否继续打印？\n在“承诺依据证明”中完善检测信息',
                				mask: true,
                				success: function(res) {
                					if (res.confirm) {
                						that.form.inspectionSituation = that.inspectionArray[0].value;
                						that.toShowCertificate();
                					}
                				}
                			});
                		}else{
                			let passFlag=false;//合格标识
                			resultList.forEach(function(item, index) {
                				if(item.inspectionResult=="0"){
                					passFlag=true;
                				}
                			})
                			if(!passFlag){
                				uni.showModal({
                					icon: "none",
                					title: '系统提示',
                					content: '您最新检测信息结果为不合格，请确认是否继续打印',
                					mask: true,
                					success: function(res) {
                						if (res.confirm) {
                							that.form.inspectionSituation = that.inspectionArray[0].value;
                							that.toShowCertificate();
                						}
                					}
                				});
                				return ;
                			}
                			that.toShowCertificate();
                		}
                	})
                }else{
                	that.toShowCertificate();
                } */
                /**
                 *	新需求
                 *	①默认自我承诺；
                 *	②选择内部质量控制时，系统自动选取最近一次的数据，若无数据则提示“未查询到内部质量控制，请核对信息”，点击确定后跳转到承诺依据证明页面，点击取消停留在当前页面；
                 *	③选择自检合格、委托检测合格时，系统自动选取对应承诺依据证明下的最近一次的合格的数据，若无数据则提示“未查询到检测结果，请核对信息”，点击确定后跳转到承诺依据证明页面，点击取消停留在当前页面；若没有合格的数据则提示“未查询到合格的检测结果，请核对信息”，点击确定后跳转到承诺依据证明页面，点击取消停留在当前页面；
                 *	2022年1月5日9:02:26
                 *	lxy
                 *
                 * 	更新的需求
                 *  1、“委托检测”改为“委托检测合格”
                	2、“自我检测”改为“自行检测合格”
                	3、“内部质量控制”改为“质量安全控制符合要求”，默认选中“质量安全控制符合要求”
                	4、去掉自我承诺选项
                	5、展示顺序改为“质量安全控制符合要求、自行检测合格、委托检测合格”
                	6、承诺依据支持多选
                	7、点击“打印预览”时，没有质量安全控制符合要求承诺依据，提示“请填写质量安全控制符合要求依据”；没有自行检测合格承诺依据，提示“请填写自行检测合格依据”；没有委托检测合格承诺依据，提示“请填写委托检测合格依据”；如果同时勾选了多个承诺依据，视缺少的承诺依据项，提示“请填写质量安全控制符合要求依据、自行检测合格依据、委托检测合格依据”，缺少哪个承诺依据项，提示哪个承诺依据
                	*	2023年10月10日15:02:26
                	*	LiuBin				 */
                //				if(that.form.inspectionSituation!="is01"){
                let param = {
                    "productId": that.form.productId,
                    "inspectionSituationList": that.form.inspectionSituationList,
                }
                //获取相应类型的承诺记录
                productInspectionApi.findNewestListByInspectionSituationList(param).then(res => {
                    if (res.code == 1) {
                        uni.showToast({
                            icon: 'none',
                            title: res.message
                        });
                        return;
                    }

                    let resData = res.data;

                    if (resData.pass) {
                      that.toSaveCertificate();
                    } else {
                        uni.showModal({
                            icon: "none",
                            title: '系统提示',
                            content: resData.msg,
                            mask: true,
                            success: function(res) {
                                if (res.confirm) {
                                    that.toInspection();
                                }
                            }
                        });
                    }
                });
                // productInspectionApi.findNewestList(param).then(res => {
                // 	if(res.code==1){
                // 		uni.showToast({
                // 			icon: 'none',
                // 			title: res.message
                // 		});
                // 		return;
                // 	}
                // 	let resultList=res.data;
                // 	let msg="检测结果";
                // 	if(that.form.inspectionSituation=="is05"){
                // 		msg="内部质量控制";
                // 	}
                // 	//无记录给提示
                // 	if(resultList==null || resultList.length==0){
                // 		uni.showModal({
                // 			icon: "none",
                // 			title: '系统提示',
                // 			content: '未查询到'+msg+'，请核对信息',
                // 			mask: true,
                // 			success: function(res) {
                // 				if (res.confirm) {
                // 					that.toInspection();
                // 				}
                // 				//that.form.inspectionSituation = that.inspectionArray[0].value;
                // 				//that.toShowCertificate();
                // 			}
                // 		});
                // 		return ;
                // 	}
                // 	//委托检测合格、自检合格判断检测结果
                // 	if(that.form.inspectionSituation=="is03" || that.form.inspectionSituation=="is04"){
                // 		//通过标识
                // 		let passFlag=false;//合格标识
                // 		resultList.forEach(function(item, index) {
                // 			if(item.inspectionResult=="0"){
                // 				passFlag=true;
                // 			}
                // 		})
                // 		//判断通过标识
                // 		if(!passFlag){
                // 			uni.showModal({
                // 				icon: "none",
                // 				title: '系统提示',
                // 				content: '未查询到合格的检测结果，请核对信息',
                // 				mask: true,
                // 				success: function(res) {
                // 					if (res.confirm) {
                // 						//that.form.inspectionSituation = that.inspectionArray[0].value;
                // 						that.toInspection();
                // 					}
                // 				}
                // 			});
                // 			return ;
                // 		}
                // 	}
                // 	that.toShowCertificate();
                // })
                // }else{
                // 	that.toShowCertificate();
                // }
            },
            toSaveCertificate() {
              let that = this;
              console.log(that.form)
              uni.showModal({
                title: '请确认是否开具合格证',
                content: '确认后信息将不能修改',
                showCancel: true,
                success: (res) => {
                  if (res.confirm) {
                    uni.showLoading({
                      title: '开具中',
                      mask: true,
                    });
                    api.saveCertificate(that.form).then(res => {
                      uni.hideLoading();
                      if (res.code == 1) {
                        uni.hideLoading();
                        uni.showToast({
                          icon: 'none',
                          title: res.message
                        });
                        return;
                      }
                      uni.redirectTo({
                        url: '/pages/certificate/certificateImgView?showType=0&id=' + res.data.id
                      });
                    })
                  }
                }
              });
            },
            toShowCertificate() {
                let that = this;
                let bleDevice = that.$store.state.bleDevice;
                if (!bleDevice || !bleDevice.name) {
                    uni.showToast({
                        title: '请先连接蓝牙打印机',
                        icon: "none",
                        mask: true,
                        duration: 1500,
                        success() {
                            setTimeout(() => {
                                that.$Router.push("/pages/device/searchDevice")
                            }, 1500);
                        }
                    });
                    return;
                }
                that.$store.commit("setPrintData", that.form);
                that.$Router.push({path: "/pages/certificate/showCertificateBaishan", query: {printType: "0"}});
            },
            toInspection() {
                let that = this;
                that.$Router.replaceAll("/pages/inspection/chooseType");
            },
            checkBox(e, item) {
                let that = this;
                let box = (item.checked = !item.checked);

            },
            getDefault() {
                var year = new Date().getFullYear(); //年
                if (year < 1900) year = year + 1900;
                var month = new Date().getMonth() + 1; //月
                if (month < 10) month = '0' + month;
                var day = new Date().getDate(); //日
                if (day < 10) day = '0' + day;
                var hour = new Date().getHours(); //小时
                if (hour < 10) hour = '0' + hour;
                var minute = new Date().getMinutes(); //分钟
                if (minute < 10) minute = '0' + minute;
                var second = new Date().getSeconds(); //秒
                if (second < 10) second = '0' + second;
                var str = year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
                return str;
            }
        },
        onLoad() {
            this.getProduct();
            this.getDict();
            if (this.inspectionSituationEnbale) {
                this.getInspectionDict();
            }
            this.form.productionDate = this.date; //生产日期默认值 当日
        },
        onShow() {
            if (!this.showFlag) {
                return;
            }

            let ent = uni.getStorageSync("ent");
            if (ent.entType == "1") {
                this.autographFlag = true;
                this.autographImg = "data:image/png;base64," + ent.autograph;
            }

        },
        created() {
            /**
             * 判断逻辑
             * 1、判断当前ent缓存，如果不存在，提示需要先维护主体信息
             * 2、判断当前主体信息是否需要补入基础信息采集数据
             * 3、判断主体审核状态，审核中，跳转至主体查看页面
             * 4、判断主体审核状态，如果examineStatus == '90'，那么说明此主体是变更暂存，跳转至变更页面
             * 5、以上逻辑都通过后显示当前页面功能
             */
            let that = this;
            let ent = uni.getStorageSync("ent");
            if (!ent) {
                uni.showModal({
                    title: '先维护主体信息',
                    content: '确定前往维护主体信息?',
                    mask: true,
                    success: function(res) {
                        if (res.confirm) {
                            that.$Router.replaceAll("/pages/ent/chooseType")
                        } else if (res.cancel) {
                            that.$Router.replaceAll("/pages/index/index")
                        }
                    }
                });
            } else if (ent.basicFlag == '1' && ent.basicEnterFlag == '0') {
                uni.showModal({
                    icon: "none",
                    content: '未在基础数据采集系统内查询到您的信息,请前往填写详细信息',
                    mask: true,
                    showCancel: false,
                    success: function(res) {
                        that.$Router.replaceAll("/pages/basic/basicEntForm")
                    }
                });
            } else if (ent.examineStatus == '0' || ent.examineStatus == '-1') {
                uni.showModal({
                    icon: "none",
                    content: '主体信息审核中，前往主体信息查看审核状态？',
                    mask: true,
                    success: function(res) {
                        if (res.confirm) {
                            that.$Router.replaceAll("/pages/ent/chooseType")
                        } else if (res.cancel) {
                            that.$Router.replaceAll("/pages/index/index")
                        }
                    }
                });
            } else if (ent.examineStatus == '90') { //变更暂存状态：跳转至变更页面
                uni.redirectTo({
                    url: "/pages/entChange/viewCheck",
                })
                return;
            } else if (ent.frozenFlag == '1') {
                uni.showModal({
                    icon: "none",
                    content: '您的账号已冻结，请联系技术服务人员取消冻结',
                    mask: true,
                    showCancel: false,
                    success: function(res) {
                        that.$Router.replaceAll("/pages/index/index")
                    }
                });
            } else {
                that.showFlag = true;
            }

        },

    }
</script>
<style scoped lang="less">
    /deep/.content.data-v-5863a200 {
        text-align: left;
    }

    /deep/.evan-checkbox__label {
        font-size: 12px;
    }

    .promise {
        width: 96%;
        margin: 15px auto;
        font-size: 12px;
        color: #888888;

        p {
            padding: 5px 0;
        }
    }

    .promise_top {
        width: 100%;
        padding: 15px 0;
    }
</style>
