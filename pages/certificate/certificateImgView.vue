<template>
  <view class="certificate-img-view">
    <view class="save-success-msg" v-if="showType === '0' && loadComplete">
      <view class="left-img">
        <image :src="imgPath+'img/img50.png'" style="width: 70px;height: 70px;"/>
      </view>
      <view class="right-msg">
        <view class="msg-1">电子证已自动为您保存</view>
        <view class="msg-2">可前往【首页-开具记录】查看</view>
      </view>
    </view>
    <div class="hgz_all" id="poster">
      <view id="canvas_er">
        <canvas style="width: 70px;height:70px;" id="canvas" ref="canvasRef" canvas-id="canvas"></canvas>
      </view>
      <div class="myCanvas_wrap">
        <canvas class="myCanvas" canvas-id="mycanvas"></canvas>
      </div>
      <div class="pic_show">
        <image :src="filePath" v-if="loadComplete" />
      </div>

    </div>
    <view class="bottom-buttons-view">
      <view class="btn-left btn-download" @click="saveImage()">
        <view class="btn-icon">
          <image :src="imgPath+'img/img52.png'" style="width: 25px;height: 25px;"/>
        </view>
        <view class="btn-msg">
          保存到相册
        </view>
      </view>
      <view class="btn-left btn-download" @click="shareImage()">
        <view class="btn-icon">
          <image :src="imgPath+'img/img53.png'" style="width: 25px;height: 25px;"/>
        </view>
        <view class="btn-msg">
          分享合格证
        </view>
      </view>
      <view class="btn-left btn-download" @click="printAgain()">
        <view class="btn-icon">
          <image :src="imgPath+'img/img54.png'" style="width: 25px;height: 25px;"/>
        </view>
        <view class="btn-msg">
          打印合格证
        </view>
      </view>
      <!--      <button type="primary" class="btn-right">-->
      <!--        继续开证-->
      <!--      </button>-->
    </view>
    <uni-popup ref="printDetailPopup" type="bottom">
      <div class="print-detail-popup">
        <div class="title">
            <span @click="closePopup">
              <image src="../../static/img/close.png" mode="widthFix"></image>
            </span>
        </div>
        <form @submit="toPrint">
          <div class="whxx_all">
            <div class="whxx_left">
              <span>*</span>打印数量:
            </div>
            <div class="whxx_right">
              <input class="uni-input" maxlength="4" name="printCount" type="number" :value="printCount"
                     placeholder="请输入打印数量">
            </div>
          </div>
          <div class="btn-bottom">
            <button type="primary" form-type="submit"
                    style="width:100px;height:35px;line-height:35px;font-size:14px;color: #fff;background-color: #02994f;">确认打印</button>
          </div>
        </form>
      </div>
    </uni-popup>
  </view>

</template>

<script>
import api from '../../api/certificate.js';
import UQRCode from '@/utils/uqrcode.js';
import {
  formatDate
} from '@/common/formatDate.js'
import graceChecker from "../../common/graceui-dataChecker/graceChecker";

var config = require('../../common/config.js')
export default {
  data() {
    return {
      certificate: {},
      imgPath: config.IMG_PATH,
      canvasWidth: 320, // 默认canvas宽高
      canvasHeight: 480,
      screenWidth: null, // 设备宽度
      imageDatd: '',
      id: '',
      viewCertificateUrl: config.BASE_PATH, //公共路径
      printCount: "",
      //展示类型 0：首次 1：再次
      showType: "0",
      loadComplete: false,
      filePath: "",
      ctx: null
    }
  },
  onLoad: function (option) {
    uni.setNavigationBarTitle({
      title: '合格证查看'
    });
    this.getScreenSize()
    if (option.showType) {
      this.showType = option.showType
    }
    if (option.id) {
      this.id = option.id
      this.getCertificate(option.id)
    }


    uni.showLoading({
      title: '数据加载中'
    });

  },
  methods: {
    getCertificate(id) {
      let that = this;
      api.getCertificateForElectronicShow(id).then(res => {
        console.log('sss', res)
        if (res.code === 0) {
          that.certificate = res.data;
          that.drawErWeiMa(that.certificate)
        }

      })
    },
    getScreenSize() {
      return new Promise((resolve, reject) => {
        uni.getSystemInfo({
          success: (res) => {
            resolve(res)
          },
        });
      })
    },
    getImageInfo(imgSrc) {
      return new Promise((resolve, errs) => {
        uni.getImageInfo({
          src: imgSrc,
          success: function (image) {
            resolve(image);
          },
          fail(err) {
            errs(err);
          }
        });
      });
    },

    async setCanvas(data, erweiImg) {
      let screenInfo = await this.getScreenSize()
      let windowWidth = screenInfo.windowWidth
      let smallScreen = false
      if (windowWidth > 390) {
        windowWidth = 390
      }
      let leftOffset = 0;
      let smallOffset = 0
      if (windowWidth >= 331) {
        leftOffset = 10
      } else if (windowWidth <= 330) {
        smallOffset = -5
      }
      // 创建画布对象
      this.ctx = uni.createCanvasContext('mycanvas', this);
      //绘制标题背景图
      let img_bg = await this.getImageInfo(
          this.imgPath + 'img/img39.png')
      this.drawImage(img_bg.path, 0, 0, windowWidth, 500);

      try {
        //绘制标题背景图
        let img = await this.getImageInfo(
            this.imgPath + 'img/img34.png')
        this.drawImage(img.path, 15, 20, windowWidth - 30, 40);
      } catch (e) {
        //TODO handle the exception
      }

      try {
        let imgSelected = await this.getImageInfo(
            this.imgPath + 'img/img40.png')
        let imgUnSelected = await this.getImageInfo(
            this.imgPath + 'img/img41.png')
        this.makeText('编号：' + data.certificateNoList[0].fullNumber, windowWidth / 2 - 10, 82, 'center',
            '#aaaaaa', '14px 黑体')
        this.makeText('我承诺对生产销售的食用农产品', 15 + leftOffset, 112, 'left', '#3e825c', 'bold 14px 黑体')
        this.drawImage(imgSelected.path, 15 + leftOffset, 123, 12, 10);
        // this.makeText('☑', 15 + leftOffset, 132, 'left', '#3e825c', 'bold 12px 黑体')
        this.makeText('不使用禁用农药兽药、停用兽药和非法添加物', 30 + leftOffset, 132, 'left', '#040404', 'bold 12px 黑体')
        //this.makeText('☑', 15 + leftOffset, 152, 'left', '#3e825c', 'bold 12px 黑体')
        this.drawImage(imgSelected.path, 15 + leftOffset, 143, 12, 10);
        this.makeText('常规农药兽药残留不超标', 30 + leftOffset, 152, 'left', '#040404', 'bold 12px 黑体')
        //this.makeText('☑', 15 + leftOffset, 172, 'left', '#3e825c', 'bold 12px 黑体')
        this.drawImage(imgSelected.path, 15 + leftOffset, 163, 12, 10);
        this.makeText('对承诺的真实性负责', 30 + leftOffset, 172, 'left', '#040404', 'bold 12px 黑体')
        this.makeText('承诺依据', 15 + leftOffset, 201, 'left', '#3e825c', 'bold 14px 黑体')
        if (data.inspectionSituationList) {
          if (data.inspectionSituationList.includes('is05')) {
            //this.makeText('☑', 15 + leftOffset, 219, 'left', '#3e825c', 'bold 12px 黑体')
            this.drawImage(imgSelected.path, 15 + leftOffset, 210, 12, 10);
          } else {
            //this.makeText('☐', 15 + leftOffset, 219, 'left', '#3e825c', 'bold 12px 黑体')
            this.drawImage(imgUnSelected.path, 15 + leftOffset, 210, 12, 10);
          }
          this.makeText('质量安全控制符合要求', 27 + leftOffset, 219, 'left', '#040404', 'bold 12px 黑体')
          let weiOffset = 0
          if (smallOffset != 0) {
            weiOffset = smallOffset - 5
          }
          if (data.inspectionSituationList.includes('is04')) {
            //this.makeText('☑', 158 + leftOffset + weiOffset, 219, 'left', '#3e825c', 'bold 12px 黑体')
            this.drawImage(imgSelected.path, 158 + leftOffset + weiOffset, 210, 12, 10);
          } else {
            //this.makeText('☐', 158 + leftOffset + weiOffset, 219, 'left', '#3e825c', 'bold 12px 黑体')
            this.drawImage(imgUnSelected.path, 158 + leftOffset + weiOffset, 210, 12, 10);
          }
          this.makeText('自行检测合格', 169 + leftOffset + weiOffset, 219, 'left', '#040404', 'bold 12px 黑体')
          let ziOffset = 0
          if (smallOffset != 0) {
            ziOffset = smallOffset - 15
          }
          if (data.inspectionSituationList.includes('is03')) {
            //this.makeText('☑', 252 + leftOffset + ziOffset, 219, 'left', '#3e825c', 'bold 12px 黑体')
            this.drawImage(imgSelected.path, 252 + leftOffset + ziOffset, 210, 12, 10);
          } else {
            //this.makeText('☐', 252 + leftOffset + ziOffset, 219, 'left', '#3e825c', 'bold 12px 黑体')
            this.drawImage(imgUnSelected.path, 252 + leftOffset + ziOffset, 210, 12, 10);
          }
          this.makeText('委托检测合格', 264 + leftOffset + ziOffset, 219, 'left', '#040404', 'bold 12px 黑体')
        } else {
          //this.makeText('☐', 15 + leftOffset, 219, 'left', '#3e825c', 'bold 12px 黑体')
          this.drawImage(imgUnSelected.path, 15 + leftOffset, 210, 12, 10);
          this.makeText('质量安全控制符合要求', 27 + leftOffset, 219, 'left', '#040404', 'bold 12px 黑体')
          let weiOffset = 0
          if (smallOffset != 0) {
            weiOffset = smallOffset - 5
          }
          // this.makeText('☐', 158 + leftOffset + weiOffset, 219, 'left', '#3e825c', 'bold 12px 黑体')
          this.drawImage(imgUnSelected.path, 158 + leftOffset + weiOffset, 210, 12, 10);
          this.makeText('自行检测合格', 169 + leftOffset + weiOffset, 219, 'left', '#040404', 'bold 12px 黑体')
          let ziOffset = 0
          if (smallOffset != 0) {
            ziOffset = smallOffset - 20
          }
          //this.makeText('☐', 252 + leftOffset + ziOffset, 219, 'left', '#3e825c', 'bold 12px 黑体')
          this.drawImage(imgUnSelected.path, 252 + leftOffset + ziOffset, 210, 12, 10);
          this.makeText('委托检测合格', 264 + leftOffset + ziOffset, 219, 'left', '#040404', 'bold 12px 黑体')
        }
      } catch (e) {
        console.log(e)
      }
      // this.makeText('承诺达标合格证', 190, 52, 'center', '#fff', 'bold 26px 黑体')
      //公章
      let gongzhangPath = ''
      if (data.sealPicList && data.sealPicList.length > 0) {
        gongzhangPath = data.sealPicList[0].filePath
        if (gongzhangPath) {
          try {
            let gongzhang = await this.getImageInfo(
                this.imgPath + gongzhangPath)
            this.drawImage(gongzhang.path, windowWidth - 105, 280, 90, 90);
          } catch (e) {
            console.log(e)
          }

        }
      }

      try {
        let img1 = await this.getImageInfo(
            this.imgPath + 'img/img35.png')
        this.drawImage(img1.path, 15 + leftOffset, 230, windowWidth - 30 - leftOffset - leftOffset, 10);
      } catch (e) {

      }
      this.makeText('产 品 名 称 ', 15 + leftOffset, 262, 'left', '#3e825c', 'bold 14px 黑体')
      this.makeText('：', 95 + leftOffset, 262, 'left', '#3e825c', 'bold 14px 黑体')
      let productName = data.productName
      if (productName.length > 13) {
        productName = productName.slice(0, 13)
      }
      this.makeText(productName, 125 + leftOffset, 262, 'left', '#040404',
          'normal 14px 黑体')

      this.makeText('数量(重量)', 15 + leftOffset, 290, 'left', '#3e825c', 'bold 14px 黑体')
      this.makeText('：', 95 + leftOffset, 290, 'left', '#3e825c', 'bold 14px 黑体')
      this.makeText(data.productNum + data.productUnitName, 125 + leftOffset, 290, 'left', '#040404',
          'normal 14px 黑体')

      let address = data.productDetail
      let offsetTop = 0
      this.makeText('产       地 ', 15 + leftOffset, 318, 'left', '#3e825c', 'bold 14px 黑体')
      this.makeText('： ', 95 + leftOffset, 318, 'left', '#3e825c', 'bold 14px 黑体')
      let maxLength = 13
      if (smallOffset != 0) {
        maxLength = 11
      }

      if (address.length > maxLength) {
        offsetTop = 28
        this.makeText(address.slice(0, maxLength - 1), 125 + leftOffset, 318, 'left', '#040404',
            'normal 14px 黑体')
        this.makeText(address.slice(maxLength - 1, 27), 125 + leftOffset, 346,
            'left',
            '#040404', 'normal 14px 黑体')
      } else {
        offsetTop = 0
        this.makeText(address, 125 + leftOffset, 318, 'left', '#040404', 'normal 14px 黑体')
      }

      this.makeText('生产者盖章', 15 + leftOffset, 346 + offsetTop, 'justify', '#3e825c', 'bold 14px 黑体')
      this.makeText('或  签  名 ', 15 + leftOffset, 374 + offsetTop, 'left', '#3e825c', 'bold 14px 黑体')
      this.makeText('： ', 95 + leftOffset, 360 + offsetTop, 'left', '#3e825c', 'bold 14px 黑体')
      if (data.entType == '1') {
        if (data.autograph && data.autograph.autograph) {
          let imagedataBuffer = data.autograph.autograph
          let imgQM = await this.getBase64ImageUrl(imagedataBuffer);
          this.drawImage(imgQM, 115 + leftOffset, 330 + offsetTop, 100, 60);
        }
      } else {
        let entName = data.entName
        if (entName.length > maxLength) {
          this.makeText(entName.slice(0, maxLength - 1), 125 + leftOffset, 351 + offsetTop, 'left', '#040404',
              'normal 14px 黑体')
          this.makeText(entName.slice(maxLength - 1, 27), 125 + leftOffset, 368 + offsetTop,
              'left',
              '#040404', 'normal 14px 黑体')
        } else {
          this.makeText(entName, 125 + leftOffset, 362 + offsetTop, 'left', '#040404', 'normal 14px 黑体')
        }
        // this.makeText(data.entName, 125 + leftOffset, 362 + offsetTop, 'left', '#040404',
        //     'normal 14px 黑体')
      }

      this.makeText('联 系 方 式 ', 15 + leftOffset, 402 + offsetTop, 'left', '#3e825c', 'bold 14px 黑体')
      this.makeText('：', 95 + leftOffset, 402 + offsetTop, 'left', '#3e825c', 'bold 14px 黑体')
      this.makeText(data.entContactsPhone, 125 + leftOffset, 402 + offsetTop, 'left', '#040404',
          'normal 14px 黑体')

      this.makeText('开 具 日 期 ', 15 + leftOffset, 430 + offsetTop, 'left', '#3e825c', 'bold 14px 黑体')
      this.makeText('：', 95 + leftOffset, 430 + offsetTop, 'left', '#3e825c', 'bold 14px 黑体')
      let createDate = data.createDate
      if (createDate) {
        createDate = this.formatDate(createDate)
      } else {
        createDate = ''
      }
      this.makeText(createDate, 125 + leftOffset, 430 + offsetTop, 'left', '#040404', 'normal 14px 黑体')

      try {
        let img2 = await this.getImageInfo(
            this.imgPath + 'img/img37.png')
        this.drawImage(img2.path, 15, 450 + offsetTop, windowWidth - 30, 10);

        let ewm = await this.getImageInfo(
            erweiImg)
        let erweiOffset = 0
        if (smallOffset != 0) {
          erweiOffset = 15
        }
        this.drawImage(ewm.path, windowWidth - 100 + erweiOffset, 375 + offsetTop, 70, 70);
      } catch (e) {
      }

      this.ctx.draw(false, async () => {
        this.filePath = await this.canvasToImage();
        uni.hideLoading();
        this.loadComplete = true;
      })
    },
    makeText(text, x, y, align, color, font) { //文字
      this.ctx.font = !font ? '24px 仿宋' : font;
      this.ctx.fillStyle = !color ? '#4a4a4a' : color;
      this.ctx.textAlign = !align ? 'left' : align;
      this.ctx.fillText(text, x, y);
    },
    drawImage(imageResource, sx, sy, sWidth, sHeight ) {
      this.ctx.drawImage(imageResource, sx, sy, sWidth, sHeight)
    },
    canvasToImage() {
      return new Promise(async (resolve, reject) => {
        //生成海报
        let screenInfo = await this.getScreenSize()
        let windowWidth = screenInfo.windowWidth
        if (windowWidth > 390) {
          windowWidth = 390
        }
        setTimeout(() => {
          uni.canvasToTempFilePath({
            canvasId: 'mycanvas',
            width: windowWidth,
            height: 500,
            success: (res) => {
              console.log(res.tempFilePath);
              // res.tempFilePath为生成的图片临时文件路径，可以将其保存到本地或上传至服务器
              resolve(res.tempFilePath);
            }
          }, this);
        }, 1000)

      })
    },

    saveImage() {
      uni.saveImageToPhotosAlbum({
        filePath: this.filePath,
        success: (response) => {
          console.log(response);
          uni.hideToast()
          uni.showToast({
            title: '保存成功'
          })
        },
        fail: (response) => {
          uni.openSetting({
            success: (response) => {
              if (!response.authSetting[
                  'scope.writePhotosAlbum'
                  ]) {
                uni.showModal({
                  title: '提示',
                  content: '获取权限成功，再次点击图片即可保存',
                  showCancel: false
                })
              } else {
                uni.showModal({
                  title: '提示',
                  content: '获取权限失败，无法保存',
                  showCancel: false
                })
              }
            }
          })
        }
      })

    },
    toPrint(e) {
      this.printCount = e.detail.value.printCount;
      let rule = [
          {
          name: "printCount",
          checkType: "notnull",
          errorMsg: "请输入开具数量"
        },
        {
          name: "printCount",
          checkType: "reg",
          checkRule: "^[0-9]{1,4}$",
          errorMsg: "打印数量格式错误:限制1-4位数字"
        }
      ];
      let validateData = {
        printCount: this.printCount
      }
      let checkRes = graceChecker.check(validateData, rule);
      if (!checkRes) {
        uni.showToast({
          title: graceChecker.error,
          icon: "none",
          duration: 1500,
          success() {
            setTimeout(() => {}, 1500);
          }
        });
        return;
      }
      if (parseInt(this.printCount) === 0) {
        uni.showToast({
          title: "您输入的打印数量为0，请重新输入",
          icon: "none",
          duration: 1500,
          success() {
            setTimeout(() => {}, 1500);
          }
        });
        return;
      }
      if (parseInt(this.printCount) > 1000) {
        uni.showToast({
          title: "单次打印数量最大为1000张",
          icon: "none",
          duration: 1500,
          success() {
            setTimeout(() => {}, 1500);
          }
        });
        return;
      }
      let that = this;
      let bleDevice = that.$store.state.bleDevice;
      if (!bleDevice || !bleDevice.name) {
        uni.showToast({
          title: '请先连接蓝牙打印机',
          icon: "none",
          mask: true,
          duration: 1500,
          success() {
            setTimeout(() => {
              that.$Router.push("/pages/device/searchDevice")
            }, 1500);
          }
        });
        return;
      }
      that.certificate.printCount = that.printCount;
      that.$store.commit("setPrintData", that.certificate);
      //初始化页面
      that.closePopup();
      that.printCount = "";

      that.$Router.push({path: "/pages/certificate/showCertificateBaishan", query: {printType: "1"}});
    },
    shareImage() {
      wx.showShareImageMenu({
        path: this.filePath
      })
    },
    printAgain() {
      this.$refs.printDetailPopup.open();
    },
    closePopup() {
      this.$refs.printDetailPopup.close();
    },
    formatDate(time) {
      if (!time) {
        return '';
      }
      time = time.replace("-", "/").replace("-", "/");
      let date = new Date(time)
      return formatDate(date, 'yyyy-MM-dd') //年-月-日 时分
    },
    async drawErWeiMa(cer) {
      // 获取uQRCode实例
      var qr = new UQRCode();
      // 设置二维码内容
      qr.data = this.viewCertificateUrl + 'a/bas/certificate/viewScanCertificateNo?id=' + cer
          .certificateNoList[0].id;
      // 设置二维码大小，必须与canvas设置的宽高一致
      qr.size = 70;
      // 调用制作二维码方法
      qr.make();
      // 获取canvas上下文
      var canvasContext = uni.createCanvasContext('canvas', this); // 如果是组件，this必须传入
      // 设置uQRCode实例的canvas上下文
      qr.canvasContext = canvasContext;
      // 调用绘制方法将二维码图案绘制到canvas上
      await qr.drawCanvas()
      setTimeout(() => {
        uni.canvasToTempFilePath({
          canvasId: 'canvas',
          success: (res) => {
            this.setCanvas(cer, res.tempFilePath)
          }
        }, this);
      }, 500);


    },
    //把base64转换成图片
    getBase64ImageUrl(base64Url) {
      /// 获取到base64Data
      let code = base64Url; // 后台返回的base64图片，没有带data:image/png;base64,的前缀。
      let src = `data:image/png;base64,${code}`;
      const fsm = wx.getFileSystemManager(); // 获取文件管理器
      const buffer = wx.base64ToArrayBuffer(code); //  将 base64 字符串转成 ArrayBuffer 对象
      const fileName = wx.env.USER_DATA_PATH + '/qm.png'; // 文件系统中的用户目录路径 （本地路径）

      return new Promise((resolve, reject) => {
        fsm.writeFile({
          filePath: fileName, // 要写入的文件路径 (本地路径)
          data: buffer, // 要写入的文本或二进制数据
          encoding: "base64", // 指定写入文件的字符编码
          success() {
            resolve(fileName)
          }
        });

      })


      /// 拼接请求头，data格式可以为image/png或者image/jpeg等，看需求
      //const base64ImgUrl = "data:image/png;base64," + base64Url;
      /// 得到的base64ImgUrl直接给图片:src使用即可

    },


  },
  filters: {}
}
</script>

<style lang="scss" scoped>
.certificate-img-view {
  background: url('https://jlsyncphgzqn.jikeruan.com/img/img38.png') no-repeat center top;
  background-size: 100% auto;
  height: calc(100vh - constant(safe-area-inset-bottom));
  height: calc(100vh - env(safe-area-inset-bottom));
  position: relative;
}

.save-success-msg {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;

  .right-msg {
    color: #ffffff;
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .msg-1 {
    font-size: 16px;
  }

  .msg-2 {
    font-size: 12px;
  }
}

.myCanvas_wrap {
  position: fixed;
  top: -9999999999777rpx;
}
.pic_show {
  display: flex;
  justify-content: center;
  image {
    width: 390px;
    height: 520px;
  }
}


.myCanvas {
  width: 390px;
  height: 520px;
}

.hgz_all {
  width: 100%;
  margin: 0;
  position: relative;

}

.hgz_cont {
  width: 100%;
  box-shadow: 2px 2px 4px #ccc;
  // background: url("@/static/img/images1.png") no-repeat;
  background-size: 100% 100%;
  margin: 15px auto;
  overflow: auto;
  border: 1px solid #f3f3f3;
}

.hgz_top {
  width: 100%;
  height: 75px;
  // background: url("@/static/img/images2.png") no-repeat;
  background-size: 100% 100%;
  text-align: center;
  color: #fff;
}

.hgz_list {
  width: 96%;
  margin: 5px auto;
}

.hgz_list table {
  width: 100%;
}

.hgz_list table tr td {
  padding: 5px 0;
  font-size: 12px;
  color: #4c4646;
}

.hgz_list table tr td img {
  width: 80px;
  float: left;
  margin: 0 5px;
}

.hgz_list table tr > td:first-child {
  padding-right: 5%;
  font-weight: bold;
  color: #557E5B;
  text-align-last: justify;
  text-align: justify;

}

.hgz_cn {
  width: 96%;
  margin: 0 auto 0px;
}

.hgz_cn_top {
  width: 100%;
  height: 30px;
  line-height: 30px;
  color: #155737;
  font-size: 14px;
  font-weight: bold;
}

.hgz_cn_list {
  width: 100%;
}

.hgz_cn_list ul li {
  /* background: #dedede; */
  padding: 2px;
  /* margin-bottom: 3px; */
  width: 98%;
  overflow: auto;
}

.hgz_cn_list ul li input {
  float: left;
  margin-top: 3px;
  width: 10%;
}

.hgz_cn_list ul li p {
  float: left;
  color: #000;
  font-size: 12px;
  width: 98%;
  /* font-weight:bold; */
}

#canvas_er {
  position: fixed;
  top: -9999999999999rpx;
}

.bottom-buttons-view {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 6vh;
  display: flex;
  align-items: center;
  justify-content: space-around;

  .btn-left {
    display: flex;
    flex-direction: column;
    font-size: 8px;
    align-items: center;
    gap: 2px;
  }

  .btn-right {
    width: 40%;
    height: 35px;
    line-height: 35px;
    font-size: 14px;
    color: #02994f;
    background-color: #fff;
    border: solid 1px #02994f;
    margin: 0;
  }
}

.print-detail-popup {
  background: #fff;
  padding: 20px;
  height: 30vh;
  position: relative;
  .title {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
    span {
      float: right;
      font-size: 20px;
      font-family: Arial, Helvetica, sans-serif;
    }
    image {
      width: 25px;
    }
  }
  .btn-bottom {
    position: absolute;
    bottom: 20px;
    width: calc(100% - 40px);
  }
}
</style>
