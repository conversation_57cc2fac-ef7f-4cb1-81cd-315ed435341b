<template>
    <view>
        <view class="certificate-text">
            <view class="title">食用农产品合格证</view>
            <view class="number">编号：XXXXXXXXXXXXXXXXXXXXXX</view>
            <view class="content">
                <view>
                    <view>食用农产品名称：{{getPrintData.productName}}</view>
                    <view>数量（重量）：{{getPrintData.productNum + getPrintData.productUnitName}}</view>
                    <view>生产者：{{ent.name}}</view>
                    <view>联系方式：{{ent.contactsPhone}}</view>
                    <view>开具日期：{{today}}</view>
                    <view>产地：{{getPrintData.productDetail}}</view>
                </view>
                <view class="code-div">
                    <view>
                        <tki-qrcode
                                ref="qrcode"
                                :val="url"
                                size="130"
                                background="#ffffff"
                                :onval="true"
                                :loadMake="true"/>
                    </view>
                    <view>扫描二维码</view>
                    <view>查看更多信息</view>
                </view>
            </view>
            <view class="doc">
                <view>我承诺对产品质量安全以及合格证真实性负责</view>
                <view>✔不使用禁限用农药兽药</view>
                <view>✔不使用非法添加物</view>
                <view>✔遵守农药安全间隔期、兽药休药期规定</view>
                <!-- <view>✔销售的食用农产品符合农药兽药残留食品安全国家标准</view> -->
            </view>
        </view>
        <div style="margin-bottom: 20px;">
            <button @click="print" type="primary" style="width:100px;height:35px;line-height:35px;font-size:14px;color: #fff;background-color: #02994f">打印合格证</button>
        </div>
    </view>
</template>

<script>
    import tkiQrcode from "@/components/tki-qrcode/tki-qrcode.vue"
    import {mapGetters} from "vuex";
    import bluetooth from "../../utils/bluetooth";
    import api from "../../api/certificate";
    export default{
        components: {tkiQrcode},
        data() {
            return {
                printData: {},
                url: "",
                ent: {},
                today: ""
            }
        },
        computed: {
            ...mapGetters(["getBleDevice", "getPrintData"])
        },
        methods: {
            print() {
				uni.showLoading({
				    title: '加载中',
					mask:true,
				});
                api.saveCertificate(this.getPrintData).then(res => {
                    console.log(res)
                    if (res.code == 1) {
                        uni.hideLoading();
                        uni.showToast({
                            icon: 'none',
                            title: res.message
                        });
                        return;
                    }
                    this.$store.commit("setPrintDetailData", res.data);
                    bluetooth.chooseBlueTypeLabel(2).then(res => {
                        uni.hideLoading();
                        uni.navigateBack({  //uni.navigateTo跳转的返回，默认1为返回上一级
                            delta: 2
                        });
                    });
                })


            },

        },
        mounted() {
            this.url = "此二维码仅为预览使用";

            this.ent = uni.getStorageSync('ent');

            let date = new Date();
            this.today = date.getFullYear()+"-" + (date.getMonth()+1) + "-" + date.getDate();
        }
    }
</script>


<style scoped lang="less">

    .certificate-text {
        font-size: 25rpx;
        padding: 30rpx;
    }
    .title {
        text-align: center;
        font-size: 50rpx;
    }
    .number {
        text-align: right;
    }

    .content {
        display: flex;
        justify-content: space-between;
    }

    .code-div {
        text-align: center;
        padding-right: 15rpx;
    }

    .doc {
        padding-top: 15rpx;
    }
</style>