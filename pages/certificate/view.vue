<template>
    <view class="certificate-detail" v-show="pageShow">
        <div class="content_all">
            <div class="whxx_all">
                <div class="whxx_left">
                    产品名称:
                </div>
                <div class="whxx_right">
                    {{certificate.productName}}
                </div>
            </div>
            <div class="whxx_all" style="display: flex;align-items: center;">
                <div class="whxx_left">
                    <div style="float: left;">复购直通车:</div>
                    <div class="fg_icon" @click="open_reBuy">
                        <image src="../../static/img/help.png"></image>
                    </div>
                </div>
                <div class="whxx_right" style="min-height: 36px;padding: 0;">
                    <switch :checked="parseInt(certificate.reBuyVisible)" height="10" :disabled="true"
                        style="transform: scale(0.6,0.6); float: right; margin-top: 3px;" />
                </div>
            </div>
            <div class="whxx_all" style="display: flex;align-items: center;" v-if="parseInt(certificate.reBuyVisible)">
                <div class="whxx_left">
                    <span></span>复购商品:
                </div>
                <div class="whxx_right" style="display: flex;align-items: center;">
                    {{certificate.product.reBuyProductName?certificate.product.reBuyProductName:'未关联商品'}}
                </div>
            </div>
            <div class="whxx_all">
                <div class="whxx_left">
                    产品数量/重量:
                </div>
                <div class="whxx_right">
                    {{certificate.productNum}}{{certificate.productUnitName}}
                </div>
            </div>
            <div class="whxx_all">
                <div class="whxx_left">
                    生产日期:
                </div>
                <div class="whxx_right">
                    {{certificate.productionDate | formatDate}}
                </div>
            </div>
            <div class="whxx_all">
                <div class="whxx_left">
                    开具数量:
                </div>
                <div class="whxx_right">
                    {{certificate.printCount}}
                </div>
            </div>
            <div class="whxx_all">
                <div class="whxx_left">
                    开具日期:
                </div>
                <div class="whxx_right">
                    {{certificate.createDate?certificate.createDate:'' | formatDate}}
                </div>
            </div>
            <div class="whxx_all">
                <div class="whxx_left">
                    承诺依据:
                </div>
                <div class="whxx_right">
                    <checkbox-group>
                        <view v-if="certificate.inspectionSituation">

                            <p v-if="certificate.inspectionSituation=='is01'" style="font-size: 12px;float: left;">自我承诺
                            </p>
                            <view v-else class="news-item" v-for="(item,index) in inspectionArray" :key="index"
                                style="width: 100%;float: left;">
                                <label class="radio">
                                    <div style="margin: 0 auto 10px;">
                                        <checkbox
                                            :checked="certificate.inspectionSituation && certificate.inspectionSituation==item.value "
                                            disabled="true" color="#09BB07" style="float: left;margin-top: -5px;" />
                                        <p style="font-size: 12px;float: left;">{{item.label}}</p>
                                    </div>
                                </label>
                            </view>
                        </view>
                        <view v-else class="news-item" v-for="(item,index) in inspectionArray" :key="index"
                            style="width: 100%;float: left;">
                            <label class="radio">
                                <div style="margin: 0 auto 10px;">
                                    <checkbox
                                        :checked="certificate.inspectionSituationList && certificate.inspectionSituationList.indexOf(item.value)>=0 "
                                        disabled="true" color="#09BB07" style="float: left;margin-top: -5px;" />
                                    <p style="font-size: 12px;float: left;">{{item.label}}</p>
                                </div>
                            </label>
                        </view>
                    </checkbox-group>
                </div>
            </div>
            <div class="promise">
                <div class="promise_top" style="overflow: auto;">
                    <radio checked="true" style="float: left;"></radio>
                    <p style="float: left;">我承诺对生产销售的食用农产品</p>
                </div>
                <p>1.不使用禁用农药兽药、停用兽药和非法添加物</p>
                <p>2.常规农药兽药残留不超标</p>
                <p>3.对承诺的真实性负责</p>
            </div>

            <div class="whxx_all_hist" style="margin:10px auto;">
                <view v-if="autographFlag">
                    <view style="width:90%;margin-top:20px;margin-left:5%;margin-right:5%;">
                        <view style="display: flex;flex-direction:row;height:30px;">
                            <view style="width:45%;display: flex;flex-direction:row;">
                                <view style="font-size:16px;color:#01BE6E;height:30px;line-height:30px;font-size:14px;">
                                    电子签名</view>
                            </view>
                        </view>
                    </view>
                    <view style="width:90%;margin-top:10px;margin-left:5%;margin-right:5%;">
                        <image :src="autographImg" style="width:100%;height:200px;" />
                    </view>
                </view>
            </div>
          <div style="margin-bottom: 20px;">
            <button @click="openCetrificateView()" type="primary"
                    style="width:100px;height:35px;line-height:35px;font-size:14px;color: #02994f;background-color: #fff;border: solid 1px #02994f;">查看合格证</button>
          </div>
        </div>
        <uni-popup ref="popup" type="center">
            <div class="re-buy-main">
                <div class="title"><span @click="close_reBuy">
                        <image src="../../static/img/close.png" mode="widthFix"></image>
                    </span></spam>复购直通车说明</div>
                <div class="info">
                    <p>开启复购直通车后，</p>
                    <p>消费者可在合格证扫码页购买您的产品</p>
                </div>
                <div class="img">
                    <image src="https://jlsyncphgzqn.jikeruan.com/img/ynyp_alert.png" mode="widthFix"></image>
                </div>
            </div>
        </uni-popup>
    </view>
</template>

<script>
    import api from '../../api/certificate.js';
    import dictApi from "../../api/dict";
    import {
        formatDate
    } from '@/common/formatDate.js'
    export default {
        data: function() {
            return {
                pageShow: false,
                certificate: {
                    type: Object,
                    productionDate: "",
                    createDate: "",
                },
                //autograph:"",
                autographFlag: false,
                autographImg: "",
                inspectionArray: [],
                certificateId: ""
            }

        },
        onLoad: function(option) {
            uni.setNavigationBarTitle({
                title: '开具记录'
            });
            this.certificateId = option.id
            let ent = uni.getStorageSync("ent");
            this.getInspectionDict();
            if (ent.entType == "1") {
                this.autographFlag = true;
                this.autographImg = "data:image/png;base64," + ent.autograph;
            }
        },
        onShow: function() {
            this.getCertificate(this.certificateId);
        },
        methods: {
            open_reBuy() {
                this.$refs.popup.open('center')
            },
            close_reBuy() {
                this.$refs.popup.close()
            },
            getCertificate: function(id) {
                var that = this;
                api.getCertificate(id).then(res => {
                    that.certificate = res.data;
                    console.log('that.certificate', that.certificate)
                    //that.autograph=res.data.autograph;
                    that.pageShow = true;
                })
            },
            back: function() {
                this.pageShow = false
            },
            getInspectionDict() {
                dictApi.findDict({
                    type: 'inspection_situation_code'
                }).then(res => {
                    if (res.code === 0) {
                        this.inspectionArray = res.data;
                    }
                })
            },
            openCetrificateView() {
              uni.navigateTo({
                url: '/pages/certificate/certificateImgView?showType=1&id=' + this.certificate.id
              });
            }
        },
        filters: {
            formatDate(time) {
                if (!time) {
                    return '';
                }
                time = time.replace("-", "/").replace("-", "/");
                let date = new Date(time)
                return formatDate(date, 'yyyy-MM-dd') //年-月-日 时分
            }
        }
    }
</script>

<style scoped lang="less">
    /deep/.evan-checkbox__label {
        font-size: 12px;
    }

    .promise {
        width: 96%;
        margin: 15px auto;
        font-size: 12px;
        color: #888888;

        p {
            padding: 5px 0;
        }
    }

    .promise_top {
        width: 100%;
        padding: 15px 0;
    }
</style>
