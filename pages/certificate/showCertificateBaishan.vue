<template>
    <view style="overflow-y: auto;">
        <view style="font-size: 12px;margin: 15px 0 5px 5%;">提示：请注意打印机纸张尺寸与打印尺寸是否相符</view>
        <!-- <button type="default" @click="fold" hover-class="none" style="width:100px;height:35px;margin-right: 10%;margin-bottom:10px;line-height:35px;font-size:14px;color: #02994f;background-color: #FFFFFF;">{{foldTitle}}<uni-icons :type="iconType" color="#02994f"></uni-icons></button> -->
        <view @click="fold"
            style="width:100px;height:35px;margin-right: 5%;line-height:35px;font-size:16px;color: #01BE6E;float: right;">
            <p style="float:left">{{foldTitle}}</p>
            <image :src="iconPath" style="width: 20px;height: 15px;margin-left: 0px;float:left;margin-top:10px"></image>
        </view>
        <view class="menu_all">
            <view class="menu_left">
                <view style="font-size: 14px;text-align: center;">请选择已连接的打印机</view>
                <view class="menu_list">
                    <view class="text-area menu_left_cont" v-for="(item,index) in deviceShowList" :key="index">
                        <view :class="[item.name == useDevice?'menu_left_on':'']" @click="deviceChange(item)"
                            style="position: relative;">
                            <image v-if="item.name == useDevice" :src="imgPath+'img/current.png'"
                                style="width: 20px;height: 20px;" class="menu_img"></image>
                            <image :src="item.imageUrl" alt=""
                                style="width: 150rpx;height: 150rpx;margin: 15px 0 5px;" />
                            <p class="menu_title">{{item.name}}</p>
                        </view>
                    </view>
                </view>
            </view>
            <view class="menu_right">
                <view style="font-size: 14px;text-align: center;">请选择打印模板样式</view>
                <view style="width: 100%;overflow: auto;">
                    <radio-group @change="templateRadioChange">
                        <view class="news-item" v-for="(item,index) in printTemplateShowList" :key="index"
                            style="width: 96%;margin: 0px 2%;overflow: auto;">
                            <label class="radio">
                                <div style="width: 100%;height: 126px; margin: 0 auto 10px;"
                                    :class="[item.code == selectTemplate?'menu_right_on':'']">
                                    <image :src="item.imageUrl" alt="" style="width: 70%;height: 110px;margin: 8px 15%;"
                                        @click="previewImg(item.imageUrl)" />
                                </div>
                                <div style="margin: 0 auto 10px;">
                                    <radio :value="item.code+''" :checked="item.code == selectTemplate"
                                        style="float: left;transform:scale(0.6);margin-top: -5px;" />
                                    <p style="font-size: 12px;float: left;">{{item.name}}</p>
                                </div>
                            </label>
                        </view>
                    </radio-group>
                </view>

            </view>
        </view>
        <div style="margin-top: 50px;margin-bottom: 20px;width: 100%;">
            <button @click="print" type="primary"
                style="width:100px;height:35px;line-height:35px;font-size:14px;color: #fff;background-color: #02994f">打印合格证</button>
        </div>
    </view>

</template>

<script>
    import tkiQrcode from "@/components/tki-qrcode/tki-qrcode.vue"
    import {
        mapGetters
    } from "vuex";
    import bluetooth from "../../utils/bluetooth";
    import api from "../../api/certificate";
    import certificateConfigApi from "../../api/certificateConfig.js";
    var config = require('../../common/config.js')
    export default {
        components: {
            tkiQrcode
        },
        data() {
            return {
                imgPath: config.IMG_PATH,
                //长春市打印机、标签
                deviceList_2201: [],
                //吉林市打印机、标签
                deviceList_2202: [],
                //白山打印机、标签
                deviceList_2206: [],
                //延吉市打印机、标签
                deviceList_2224: [],
                //通用打印机、标签
                deviceList_general: [],
                deviceList: [],
                deviceShowList: [],
                printTemplateList: [],
                printTemplateShowList: [],
                useDevice: "",
                selectTemplate: "",
                foldTitle: "更多选择",
                //打印方式 首次：0  再次：1
                printType:"0"
            }
        },
        onLoad() {
            if (this.$Route.query.printType) {
                this.printType = this.$Route.query.printType
            }
            let ent = uni.getStorageSync("ent");
            //根据主体信息所在区划 设置不同设备数据
            /* if (ent.city == "2201") { //长春市
            	this.deviceList = this.deviceList_2201;
            } else if (ent.city == "2202") { //吉林市
            	this.deviceList = this.deviceList_2202;
            } else if (ent.city == "2206") { //白山市 
            	this.deviceList = this.deviceList_2206;
            } else if (ent.city == "2224") { //延吉市 
            	this.deviceList = this.deviceList_2224;
            } else { //通用地区
            	this.deviceList = this.deviceList_general;
            } */
            let param = {
                area: {
                    code: ent.city,
                }
            }
            certificateConfigApi.getConfig(param).then(res => {
                if (res.code == 1) {
                    uni.showModal({
                        title: '提示',
                        content: res.message,
                        mask: true,
                        showCancel: false,
                        success: function(res) {
                            uni.hideLoading();
                            uni.navigateBack({
                                delta: 2
                            });
                        }
                    });
                }
                this.deviceList = res.data;
                this.useDevice = uni.getStorageSync("useDevice") ? uni.getStorageSync("useDevice") : this
                    .deviceList[0].name;
                this.printTemplateSet();
                this.deviceShowList = this.deviceList.filter(item => item.name === this.useDevice);
            })

        },
        computed: {
            ...mapGetters(["getBleDevice", "getPrintData"]),
            iconPath() {
                let path = this.foldTitle == "更多选择" ? "img/arrowdown.png" : "img/arrowup.png";
                return this.imgPath + path;
            }
        },
        methods: {
            //打印机选择 2020年12月31日9:07:21 lxy
            deviceChange(e) {
                this.useDevice = e.name;
                this.printTemplateSet();
            },
            //模板选择 2020年12月31日9:07:35 lxy
            templateRadioChange(e) {
                this.selectTemplate = e.target.value;
            },
            //模板设置 2020年12月31日9:07:49 lxy
            printTemplateSet() {
                let item = this.deviceList.filter(obj => obj.name === this.useDevice)[0];
                this.printTemplateList = item.templateList;
                this.selectTemplate = uni.getStorageSync("selectTemplate") ? uni.getStorageSync("selectTemplate") : item
                    .templateList[0].code;
                if (uni.getStorageSync("selectTemplate")) {
                    this.selectTemplate = uni.getStorageSync("selectTemplate");
                    //判断缓存里已选择的标签，是否在当前模板集合里，没有设置默认值
                    let resultList = item.templateList.filter(template => template.code == this.selectTemplate);
                    if (resultList.length == 0) {
                        this.selectTemplate = item.templateList[0].code;
                    }
                } else {
                    this.selectTemplate = item.templateList[0].code;
                }
                if (this.foldTitle == "更多选择") {
                    this.printTemplateShowList = item.templateList.filter(template => template.code == this
                        .selectTemplate);
                } else if (this.foldTitle == "收起") {
                    this.printTemplateShowList = item.templateList;
                }
            },
            //打印 2020年12月31日9:08:03 lxy
            print() {
                uni.showLoading({
                    title: '加载中',
                    mask: true,
                });
                let that = this;
                let bleDevice = this.$store.state.bleDevice;
                if (!bleDevice || !bleDevice.name) {
                    uni.showToast({
                        title: '请先连接蓝牙打印机',
                        icon: "none",
                        mask: true,
                        duration: 1500,
                        success() {
                            setTimeout(() => {
                                that.$Router.push("/pages/device/searchDevice")
                            }, 1500);
                        }
                    });
                    return;
                }
                //保存合格证 2020年12月31日9:09:50 lxy
                console.log('this.getPrintData', this.getPrintData)
                // 人参合格证生成电子证，此处调用再次打印方法 --by Wlq
                if (this.printType === "0") {
                    api.saveCertificate(this.getPrintData).then(res => {
                        if (res.code == 1) {
                            uni.hideLoading();
                            uni.showToast({
                                icon: 'none',
                                title: res.message
                            });
                            return;
                        }
                        this.doPrint(res);
                    })
                } else {
                    api.printCertificateAgain(this.getPrintData).then(res => {
                        if (res.code == 1) {
                            uni.hideLoading();
                            uni.showToast({
                              icon: 'none',
                              title: res.message
                            });
                            return;
                        }
                        this.doPrint(res);
                    })
                }
            },
            doPrint(res) {
              console.log('printDetail', res.data)
              uni.setStorageSync("useDevice", this.useDevice);
              uni.setStorageSync("selectTemplate", this.selectTemplate);
              this.$store.commit("setPrintDetailData", res.data);
              let device = this.deviceList.filter(item => item.name === this.useDevice)[0];
              bluetooth.chooseBlueTypeLabel(this.selectTemplate, device.type, device.command).then(res => {
                uni.hideLoading();
                if (this.printType === "0") {
                  uni.navigateBack({
                    delta: 2
                  });
                } else {
                  uni.navigateBack({
                    delta: 1
                  });
                }
              });
            },
            //图片预览 2020年12月31日9:08:49 lxy
            previewImg(imgUrl) {
                let _this = this;
                let imgsArray = [];
                imgsArray[0] = imgUrl
                uni.previewImage({
                    current: 0,
                    urls: imgsArray
                });
            },
            //展开、收起方法 2020年12月31日9:09:27 lxy
            fold() {
                if (this.foldTitle == "更多选择") {
                    this.foldTitle = "收起";
                    this.deviceShowList = this.deviceList;
                    this.printTemplateShowList = this.printTemplateList;
                } else if (this.foldTitle == "收起") {
                    this.foldTitle = "更多选择";
                    this.deviceShowList = this.deviceList.filter(item => item.name === this.useDevice);
                    this.printTemplateShowList = this.printTemplateList.filter(template => template.code == this
                        .selectTemplate);
                }
            }
        },
        mounted() {}
    }
</script>


<style scoped lang="less">
    .certificate-text {
        font-size: 25rpx;
        padding: 35rpx;
    }

    .title {
        text-align: center;
        font-size: 50rpx;
        color: #fff;
    }

    .number {
        text-align: right;
    }

    .content {
        display: flex;
        justify-content: space-between;
    }

    .code-div {
        text-align: center;
        padding-right: 15rpx;
    }

    .doc {
        padding-top: 30rpx;
        font-size: 20rpx;
    }

    .menu_all {
        width: 100%;
        overflow: auto;
    }

    .menu_left {
        width: 50%;
        float: left;
    }

    .menu_right {
        width: 50%;
        float: left;
    }

    .menu_left_on {
        background-color: #EAF5F1;
        border: 1px dashed #2AA83A;
    }

    .menu_right_on {
        background-color: #EAF5F1;
    }

    .green {
        background-color: #fff;
    }

    .menu_list {
        width: 96%;
        margin: 0 auto;
        //box-shadow: 2px 2px 4px #909090;

    }

    .menu_left_cont {
        width: 96%;
        margin: 0 auto 10px;
        text-align: center;
    }

    .menu_title {
        width: 100%;
        text-align: center;
        font-size: 12px;
        padding-bottom: 10px;
    }

    .menu_img {
        position: absolute;
        top: 0;
        right: 0;
    }

    button::after {
        border: none;
    }
</style>