<template>
	<view class="uni-popup-share">
		<view class="uni-share-content">
			<view class="uni-share-content-box">
				<view class="whxx_all">
					<view class="whxx_left">请选择产品:</view>
					<view class="whxx_right">
						<picker name="productSortCode" @change="bindPickerChange" :value="index" :range="products" :range-key="'name'">
							<view class="uni-input">
								{{products[index]?products[index].name:'请选择产品'}}
							</view>
						</picker>
					</view>
				</view>
				<view class="whxx_all">
					<view class="whxx_left">请选时间范围:</view>
				</view>
				<view class="whxx_all_hist" >
					<biaofun-datetime-picker class="all" ref="startDateRef" placeholder="起始时间" fields="day" @change="changeStartDate"></biaofun-datetime-picker>
				</view>
				<view class="whxx_all_hist" >
					<biaofun-datetime-picker class="all" ref="endDateRef" placeholder="结束时间" fields="day" @change="changeEndDate"></biaofun-datetime-picker>
				</view>

			</view>
		</view>
		<view class="uni-share-button-box">
			<button class="uni-share-button" type="default" size="default" style="color: #000;width: 100px;float:right;height: 40px;line-height: 40px;font-size:14px;" @click="close" >重置</button>
			<button class="uni-share-button" type="primary" size="default" style="color: #fff;width: 100px;float:right;height: 40px;line-height: 40px;font-size:14px;background-color: #02994f;" @click="select">筛选</button>
		</view>
	</view>
</template>

<script>
	import api from '../../api/certificate';
	import BiaofunDatetimePicker from "../../components/biaofun-datetime-picker/biaofun-datetime-picker";
	export default {
		inject: ['popup'],
		data() {
			return {
				index: '',
				productId:null,
				startDate:null,
				endDate:null,
				date: new Date().toISOString().slice(0, 10),
			}
		},
		props: {
			products: {
				type: Array,
				required:true,
			},
		},
		methods: {
			// 产品选择
			bindPickerChange(e) {
			    this.index = e.target.value;
			    var selected = this.products[this.index];    //获取选中的数组
			    this.productId = selected.id;          //选中的id
			},
			/* 时间选择 */
			changeStartDate(res) {
			    this.startDate = res.f1;
				if(this.startDate && this.endDate){
					let stDate = new Date(this.startDate).getTime();
					let enDate = new Date(this.endDate).getTime();
					if(stDate>enDate){
						this.startDate=''
						this.$refs.startDateRef.dateStr=''
						uni.showToast({
							icon: 'none',
							title: '起始时间不能大于结束时间'
						});
					}
				}

			},
			changeEndDate(res) {
			    this.endDate = res.f1;
				if(this.startDate && this.endDate){
					let stDate = new Date(this.startDate).getTime();
					let enDate = new Date(this.endDate).getTime();
					if(stDate>enDate){
						this.endDate = ''
						this.$refs.endDateRef.dateStr=''
						uni.showToast({
							icon: 'none',
							title: '结束时间不能小于起始时间'
						});
					}
				}

			},

			/**
			 * 选择内容
			 */
			select() {
				let productId = this.productId;
				let startDate = this.startDate;
				let endDate = this.endDate;
				this.$emit('select', {
					productId,
					startDate,
					endDate
				},
				/* , () => {
					this.popup.close()
				} */)
				this.popup.close()
				//this.clear();
			},
			/**
			 * 关闭窗口
			 */
			close() {
				this.clear();
				let productId = this.productId;
				let startDate = this.startDate;
				let endDate = this.endDate;
				this.$emit('select', {
					productId,
					startDate,
					endDate
				},
				/* , () => {
					this.popup.close()
				} */)
				//this.popup.close()
			},
			//清空选择项
			clear(){
				this.index=null;
				this.productId=null;
				this.startDate=null;
				this.endDate=null;
				this.$refs.startDateRef.clear();
				this.$refs.endDateRef.clear();
			}
		}
	}
</script>
<style lang="scss" scoped>
	.uni-popup-share {
		background-color: #fff;
	}
	.uni-share-title {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		align-items: center;
		justify-content: center;
		height: 40px;
	}
	.uni-share-title-text {
		font-size: 14px;
		color: #666;
	}
	.uni-share-content {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		justify-content: center;
		padding-top: 10px;
	}
	
	.uni-share-content-box {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		flex-wrap: wrap;
		width: 360px;
	}
	
	.uni-share-content-item {
		width: 90px;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		justify-content: center;
		padding: 10px 0;
		align-items: center;
	}
	
	.uni-share-content-item:active {
		background-color: #f5f5f5;
	}
	
	.uni-share-image {
		width: 30px;
		height: 30px;
	}
	
	.uni-share-text {
		margin-top: 10px;
		font-size: 14px;
		color: #3B4144;
	}
	
	.uni-share-button-box {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		padding: 10px 15px;
	}
	.select{
		background-color: rgb(1, 190, 110);
	}
	.all {
		width: 100%;
		float: left;
		padding:10px 0;
		font-size: 12px;
		color: #888888;
	}
</style>
