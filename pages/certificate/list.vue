<template>
	<view v-if="showFlag">
		<image :src="imgPath+'img/kjjl.png'" alt="" style="position: absolute;top: 0;left: 0;width: 100%;z-index: -99;height: 100%;">
		<div class="kjjl_top">
			<div class="kj_num">
				<image :src="imgPath+'img/print.png'" alt="">
				<p>合格证开具<span style="margin: 0 10px;color:#328b58;font-size: 30px;">{{summary.printAmount}}</span>张</p>
			</div>
			<div class="kj_number">
				<p>
					开具次数:
					<span class="flex-table-box">{{summary.dataAmount}}</span>次
				</p>
			</div>
			<div class="kj_number">
				<p>
					最近一次开具时间:
					<span class="flex-table-box">{{summary.lastCreateDate}}</span>
				</p>
			</div>
		</div>
		<view class="cpxx_title">
			<view class="product_all">
				<view class="cpxx_left" style="margin: 0 6%;">产品名称</view>
			</view>
			<view class="number_all" @click="printCountOrder()">
				<view class="cpxx_left" style="margin: 0 2%;" >开具数量</view>
				<view class="cpxx_right" style=" display:inline-block;margin-top:10px;">
					<view class="jt_all">
						<view v-if="countUpShow" class="triangle-up"></view>
					</view>
					<view class="jt_all">
						<view v-if="countDownShow" class="triangle-down"></view>
						<view v-if="countDown"  style="border-top: 7px solid #fff;" class="triangle-down"></view>
					</view>
				</view>
			</view>
			<view class="date_all"  @click="dateOrder()">
				<view class="cpxx_left" style="margin: 0 6%;">开具时间</view>
				<view class="cpxx_right" style=" display:inline-block;margin-top:10px;">
					<view class="jt_all">
						<view v-if="addUpShow" class="triangle-up"></view>
					</view>
					<view class="jt_all">
						<view v-if="addDownShow" class="triangle-down"></view>
						<div v-if="addDown" class="triangle-down" style="border-top: 7px solid #fff;"></div>
					</view>
				</view>
			</view>
			<view class="manage_all" style="float: right;">
				<view @click="querySelectShow()">筛选</view>
			</view>
		</view>
		<view v-if="listDataEmpty">
			<view class="emptyData">暂无数据</view>
		</view>
		<view v-else>
			<view class="news-item" v-for="(item,index) in list" :key="index">
				<view class="news-intr" @click="onView(item.id)">
					<view class="flex-table">
						<div style="float:left;width: 90px; overflow: hidden;white-space: nowrap;text-overflow: ellipsis;height: 50px;display: flex;justify-content: center;">{{item.productName|nameFormat}}</div>
						<div style="float:left;width: 24%;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;text-align: center;">{{item.printCount}} </div>
						<div style="float:left;width: 35%;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;text-align: center;">{{item.createDate | formatDate}}</div>
					</view>
				</view>
				<uni-icons type="arrowright" size="17" style="color:grey;margin-top:0px;margin-right: 5px;"></uni-icons>
			</view>
			<uni-load-more :status="status" :content-text="contentText" />
		</view>
		<uni-popup ref="popupShare" type="share">
			<QuerySelection :products="products" @select="select"></QuerySelection>
		</uni-popup>
		
	</view>
</template>

<script>
	import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';
	import api from '../../api/certificate.js';
	import QuerySelection from '@/pages/certificate/querySelection.vue'
	import {formatDate} from '@/common/formatDate.js'
	var config = require('../../common/config.js')
	export default {
		components: {
			uniLoadMore,
			QuerySelection,
			
		},
		data: function() {
			return {
				imgPath:config.IMG_PATH,
				showFlag:false,
				order: true,
				addUpShow: true,
				addDownShow: true,
				countUpShow: true,
				countDownShow: true,
				summary:{},
				list: [],
				param: {
					productId:'',
					beginCreateDate:'',
					endCreateDate:'',
					sortName:'',
					sortOrder: '',
				},
				total: 0, //总数
				refreshing: false, //为true表示正在刷新
				fetchPageNum: 1, //当前页数
				loading: true, // 为true表示加载中
				products:[],
				status: 'noMore',
				contentText: { // 加载提示
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				countDown: false,
				addDown: false,
				listDataEmpty:false,
				
			}

		},
		onLoad: function(option) {
			uni.setNavigationBarTitle({
				title: '开具记录'
			});
		},
		onShow: function() {
			this.list=[];
			this.fetchPageNum=1;
			this.refreshing = true;
			if(this.showFlag){
				this.getData();
				this.getSummaryInfoData();
			}
		},
		onReachBottom() {
			if (this.list.length < this.total) {
				this.status = 'loading';
				this.getData();
			} else {
				this.status = 'noMore'; //没有数据时显示‘没有更多’
			}

		},
		onPullDownRefresh() {
			this.refreshing = true;
			this.getData();
		},
		created(){
			let that=this;
			let ent=uni.getStorageSync("ent");
			if(!ent){
				uni.showModal({
				    title: '先维护主体信息',
				    content: '确定前往维护主体信息?',
					mask: true,
				    success: function (res) {
				        if (res.confirm) {
							that.$Router.replaceAll("/pages/ent/chooseType")
				        } else if (res.cancel) {
				            that.$Router.replaceAll("/pages/index/index")
				        }
				    }
				});
			}else if (ent.basicFlag == '1' && ent.basicEnterFlag == '0'){
				uni.showModal({
					icon: "none",
				    content: '未在基础数据采集系统内查询到您的信息,请前往填写详细信息',
					mask: true,
					showCancel:false,
				    success: function (res) {
						that.$Router.replaceAll("/pages/basic/basicEntForm")
				    }
				});
			}else if (ent.frozenFlag == '1'){
				uni.showModal({
					icon: "none",
				    content: '您的账号已冻结，请联系技术服务人员取消冻结',
					mask: true,
					showCancel:false,
				    success: function (res) {
						that.$Router.replaceAll("/pages/index/index")
				    }
				});
			}else{
				that.showFlag=true;
			}
		},
		methods: {
			getSummaryInfoData: function() {
				let that = this;
				api.getSummaryInfo().then(res => {
					if (res.code == 1) {
						uni.showToast({
							icon: 'none',
							title: res.message
						});
						return;
					}
					that.summary=res.data;
				})
			},	
			getData: function() {
				let that = this;
				that.loading = true;
				let pageNo = that.refreshing ? 1 : that.fetchPageNum;
				api.findList(pageNo, that.param).then(res => {
					console.log("res---",res)
					if (res.code != 0) {
						uni.showToast({
							title: "请求数据失败请重试"
						})
						return;
					} else {
						that.total = res.data.count;
						if (that.refreshing) {
							that.refreshing = false;
							uni.stopPullDownRefresh();
							that.list = res.data.list?res.data.list:[];
							that.fetchPageNum = res.data.next;
							if(that.list.length>0){
								that.listDataEmpty = false
							}else{
								that.listDataEmpty = true
							}
						} else {
							that.list = that.list.concat(res.data.list);
							that.fetchPageNum = res.data.next;
						}
					}
				})
			},
			//开具时间排序
			dateOrder: function() {
				let that = this;
				that.countUpShow = true;
				that.countDownShow = true;
				that.countDown = false;
				that.addDownShow = false;
				that.param.sortName = 'createDate';
				if (that.order) {
					that.addUpShow = true;
					that.addDown = false;
					that.order = false;
					that.param.sortOrder = "0"
				} else {
					that.addDown = true;
					that.addUpShow = false;
					that.order = true;
					that.param.sortOrder = '1';
				}
				that.refreshing = true;
				that.getData();

			},
			//打印数量排序
			printCountOrder: function(item) {
				let that = this;
				that.addDownShow = true;
				that.addDown = false;
				that.addUpShow = true;
				that.countDownShow = false;
				that.param.sortName = 'printCount';
				if (that.order) {
					that.countUpShow = true;
					that.countDown = false;
					that.order = false;
					that.param.sortOrder = "0"
				} else {
					that.countDown = true;
					that.countUpShow = false;
					that.order = true;
					that.param.sortOrder = "1"
				}
				that.refreshing = true;
				that.getData();
			},
			
			//查看
			onView: function(id) {
				uni.navigateTo({
					url: '../certificate/view?id=' + id,
				})
			},
			querySelectShow:function(){
				this.getProduct();
				this.$refs.popupShare.open();
				
			},
			select:function (map) {
				let that =this;
				that.param.productId=map.productId;
				that.param.beginCreateDate=map.startDate;
				that.param.endCreateDate=map.endDate;
				that.refreshing = true;
				that.getData();
				//this.$refs.popupShare.close()
			},
			getProduct:function(){
			    api.findProducts().then(res => {
			        if (res.code === 0) {
			            this.products = res.data;
			        }
			    })
			},
		},
		filters:{
		    formatDate (time){
				time = time.replace("-", "/").replace("-", "/");
				let date = new Date(time)
				return formatDate(date,'yyyy-MM-dd') //年-月-日 时分
		    },
			nameFormat(value) {
				if(value.length>8){
					let start = value.slice(0, 8)
					return `${start}...`
				}
				return value
			},
		}
	}
</script>

<style lang="scss" scoped>
	
	.news-item {
		height: 50px;
		line-height: 50px;
		margin-left: 30rpx;
		margin-right: 30rpx;
		display: flex;
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		/* padding-top: 30rpx;
		padding-bottom: 30rpx; */
		border-bottom: 1px #e0e0e0 solid;
	}
	
	.news-item .news-intr {
		flex: 1;
		padding-right: 20rpx;
		/* display: flex; */
		flex-direction: column;
		justify-content: space-between;
	}
	
	.news-intr .news-title {}
	
	.news-intr .news-info {
		display: flex;
		font-size: 18rpx;
		color: #999999;
	}
	
	.news-info .news-ago {
		text-align: left;
	}
	
	.news-info .news-type {
		text-align: right;
		flex: 1;
	}
	
	.news-item .news-image {
		flex: 1;
		max-width: 240rpx;
		max-height: 160rpx;
	}
	
	.news-item .news-image image {
		max-width: 100%;
		max-height: 100%;
	}
	.triangle-up {
		margin-top: 3px;
		/*display:inline-block;*/
		width: 0;
		height: 0;
		border-left: 5px solid transparent;
		border-right: 5px solid transparent;
		border-bottom: 7px solid #fff;
		/* background: #fff; */
	}

	.triangle-down {
		margin-top: 3px;
		/*display:inline-block;*/
		width: 0;
		height: 0;
		border-left: 5px solid transparent;
		border-right: 5px solid transparent;
		border-top: 7px solid #9dd6b7;
		/* background: #9dd6b7; */
	}
	.emptyData{text-align: center;font-size:16px}
</style>
