<template>
	<view></view>
</template>

<script>
	import md5 from '../../utils/md5.js';
	export default {
		components: {},
		data() {
			return {}
		},
		onLoad: function(options) {
			let obj = {
				version: '1.0.0', //4
				charset: 'UTF-8', //2
				signMethod: 'MD5',
				signature: '',
				appNo: '220501763920001', //1
				settleNo: options.settleNo, //3
			}
			let data = this.getSignature(obj);
			uni.request({
				url: 'https://cfp.zhuisuguanjia.com/hsh/out/queryOrderBySettleNo', //仅为示例，并非真实的接口地址
				method: 'post',
				data: data,
				header: {
					"Content-Type": "application/x-www-form-urlencoded"
				},
				success(res) {
					let prepayData = JSON.parse(res.data.prepayData)

					uni.requestPayment({
						provider: 'jkrpay',
						appId: 'wxef85757ba4006c16',
						timeStamp: prepayData.timeStamp,
						nonceStr: prepayData.nonceStr,
						package: prepayData.package,
						signType: prepayData.signType,
						paySign: prepayData.paySign,
						success: function(res) {
							uni.showToast({
								title: '用户支付成功',
								icon: "none",
								mask: true,
								duration: 1500,
								success() {
									setTimeout(() => {
										uni.reLaunch({
											url: '/pages/shop/shop?param=1'
										})
									}, 1500);
								}
							});
						},
						fail: function(res) {
							console.log(res)
							let msg='未知错误';
							if(res.errMsg.indexOf("cancel") >0){
								msg='用户取消支付'
							}else if(res.errMsg.indexOf("fail") >0){
								msg='支付失败'
							}
							uni.showToast({
								title: msg,
								icon: "none",
								mask: true,
								duration: 1500,
								success() {
									setTimeout(() => {
										uni.reLaunch({
											url: '/pages/shop/shop?param=0'
										})
									}, 1500);
								}
							});
							
						},
						complete: function(res) {
							
						}
					})
				},
				fail(res){
					console.log(res)
				}
			})

		},
		methods: {
			getSignature: function(data) {
				let signatureStr = "appNo=" + data.appNo + "&charset=" + data.charset + "&settleNo=" + data.settleNo + "&version=" +
					data.version + "&" + md5("20200825001")
				data.signature = md5(signatureStr)
				return data;
			}
		}
	}
</script>

<style>
</style>
