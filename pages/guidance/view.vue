<template>
	<view>
		<view class="content">
			<view class="big-title">{{guidance.title}}</view>
			<view class="small-title">
				<view class="small1">{{guidance.label}}</view>
				<view class="small2">{{guidance.source}}</view>
				<view class="small3">{{guidance.editDate | dateFilter}}</view>
			</view>
			<view class="img">
				<img :src="filePath" style="width:250px;height: 150px;" />
			</view>
			<view class="middle-content">
				<rich-text :nodes="content" style="color: #333333;"></rich-text>
			</view>
		</view>
	</view>
</template>

<script>
	import api from '@/api/guidance.js';
	export default {
		data: function() {
			return {
				id: "",
				guidance: {},
				filePath:"",
				content:""
			}
		},
		onLoad: function(option) {
			uni.setNavigationBarTitle({
				title: '农业技术指导'
			});
			this.id = option.id;
		},
		onShow: function() {
			this.fetchPageNum = 1;
			this.refreshing = true;
			this.getGuidance();
		},
		methods: {
			getGuidance: function() {
				api.getGuidance(this.id).then(res => {
					this.guidance = res.data;
					this.filePath = res.data.filePath;
					//this.content = res.data.content
					//处理富文本 图片溢出显示问题
					var richtext=  res.data.content;
					const regex = new RegExp('<img', 'gi');
					richtext= richtext.replace(regex, `<img style="max-width: 100%;"`);
					this.content = richtext;
						
				})
			},
		},
		filters:{
			dateFilter:function(time){
				if(time){
					time = time.replace("-", "/").replace("-", "/");
					var date = new Date(time),
					year = date.getFullYear(),
					month = date.getMonth() + 1,
					day = date.getDate();
					return year+'-'+month+'-'+day;
				}else{
					return "";
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	page {
	  background-color:#FFFFFF;
	  overflow: hidden;
	}
	.content{
		box-shadow: 2px 2px 4px #ccc;
		background: #fff;
		margin: 10px;
		padding: 10px 10px;
		height: 1300rpx;
		.big-title{
			text-align: center;
			font-size: 16px;
			color: #333333;
			font-weight: bold;
		}
		.small-title{
		    font-size: 12px;
		    margin-top: 20px;
			display: inline-block;
			width: 100%;
			.small1{
				width: 33%;
				float: left;
				color: #888888;
				border-right:1px solid #888;
				text-align: center;
			}
			.small2{
				width: 33%;
				float: left;
				color: #888888;
				border-right:1px solid #888;
				text-align: center;
			}
			.small3{
				width: 33%;
				float: left;
				color: #888888;
				text-align: center;
			}
		}
		.img{
			margin: 10px 0;
			text-align: center;
		}
		.middle-content{
			font-size: 14px;
			overflow: auto;
			height: 820rpx;
		}
	}
</style>
