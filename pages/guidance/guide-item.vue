<template>
	<view class="content-item">
		<view class="item-left">
<!--			<img :src="filePath | fileFilter" />-->
			<img :src="filePath | fileFilter" class="img_class"  style="width:60px;height: 60px;"/>
		</view>
		<view class="item-middle">
			<view class="big-title"> {{content.title}} </view>
			<view class="small-title">
				<view class="small1"> {{content.label}} </view>
				<view class="small2">{{content.source}}</view>
				<view class="small3">{{content.editDate | dateFilter}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data: function() {
			return {
				filePath:this.content.filePath
			}
		},
		props: {
		  content: {
				type: Object
			},
	    },
		methods: {
		},
		filters:{
			dateFilter:function(time){
				time = time.replace("-", "/").replace("-", "/");
				var date = new Date(time),
				year = date.getFullYear(),
				month = date.getMonth() + 1,
				day = date.getDate();
				return year+'-'+month+'-'+day;
			},
			fileFilter:function (filePath) {
				return filePath + '?imageView2/2/w/300/h/300/q/100';
			}
		}
	}
</script>

<style scoped lang="scss">
	page {
	  background-color:#FFFFFF;
	}
	.content-item{
		width: 90%;
		margin-left: 5%;
		display: inline-block;
		border-bottom: 1px solid #e0e0e0;
		.item-left{
			width: 25%;
			float: left;
		}
		.item-middle{
			width: 75%;
			float: left;
			.big-title{
				font-size: 16px;
			}
			.small-title{
				font-size: 12px;
				margin-top: 20px;
				.small1{
					width: 33%;
					float: left;
					color: #888888;
					/* border-right:1px solid #888; */
					text-align: left;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
				.small2{
					width: 33%;
					float: left;
					color: #888888;
					border-left:1px solid #888;
					border-right:1px solid #888;
					text-align: center;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
				.small3{
					width: 32%;
					float: left;
					color: #888888;
					text-align: right;
				}
			}
		}
	}
	.img_class{
		image-rendering: -webkit-optimize-contrast;
		image-rendering: -moz-crisp-edges;
		image-rendering: crisp-edges;
	}
</style>
