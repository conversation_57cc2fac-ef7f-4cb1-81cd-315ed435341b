<template>
	<view>
		<view class="search">
			<view class="search-title">
				<input v-model="title" maxlength="15" placeholder="输入标题" @input="clearInput"/>
				<div v-if="showClearIcon" name="clearIcon" style="width: 30px;display: flex; justify-content: center;position: absolute;right: 10px;" @click="clearIcon">
					<uni-icons color="#c0c4cc" size="20" type="clear"  />
				</div>
			</view>
			<view class="search-btn" @click="serch()">搜索</view>
		</view>
		<div :class="list && list.length>0?'content_all':''" style="margin-top: 65px;">
			<view v-if="listDataEmpty">
				<view class="emptyData">暂无数据</view>
			</view>
			<view  class="content-list">
				<view v-for="(item,index) in list" :key="item.id">
					<view @click="view(item.id)">
						<guide-item :content="item"></guide-item>
					</view>
				</view>
				<uni-load-more :status="status" :content-text="contentText" />
			</view>
		</div>
		
	</view>
</template>

<script>
	import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';
	import api from '@/api/guidance.js'
	import guideItem from '@/pages/guidance/guide-item.vue'
	export default {
		components:{
			'guide-item':guideItem,
			uniLoadMore
		},
		data: function() {
			return {
				list: [],
				total: 0, //总数
				refreshing: false, //为true表示正在刷新
				fetchPageNum: 1, //当前页数
				loading: true, // 为true表示加载中
				title:"",
				filePath:"",
				status: 'noMore',
				contentText: { // 加载提示
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				listDataEmpty:false,
				showClearIcon:false,
			}
		},
		onLoad: function(option) {
			uni.setNavigationBarTitle({
				title: '农业技术指导'
			});
			// this.getData();
		},
		onShow: function() {
			this.list=[];
			this.fetchPageNum=1;
			this.refreshing = true;
			this.getData();
		},
		onReachBottom() {
			if (this.list.length < this.total) {
				this.status = 'loading';
				this.getData();
			} else {
				this.status = 'noMore'; //没有数据时显示‘没有更多’
			}
		},
		onPullDownRefresh() {
			console.log('下拉刷新');
			this.refreshing = true;
			this.getData();
		},
		methods: {
			getData: function() {
				let that = this;
				that.loading = true;
				let param = {
					title: that.title
				};
				that.status = 'loading';
				that.listDataEmpty = false
				let pageNo = that.refreshing ? 1 : that.fetchPageNum;
				api.findGuidanceList(pageNo, param).then(res => {
					if(res.code ==0){
						that.total = res.data.count;
						
						if (that.refreshing) {
							that.list=[];
							that.refreshing = false;
							uni.stopPullDownRefresh();
							if(res.data.list){
								that.list = res.data.list;
							}else{
								that.listDataEmpty = true
							}
							that.fetchPageNum = res.data.next;
							if(that.list.length<that.total){
								that.status = 'more';
							}else{
								if(that.total ==0){
									that.status = 'more';
								}else{
									that.status = 'noMore';
								}
								
							}
						} else {
							that.list = that.list.concat(res.data.list);
							that.fetchPageNum = res.data.next;
							if(that.list.length<that.total){
								that.status = 'more';
							}else{
								if(that.total ==0){
									that.status = 'more';
								}else{
									that.status = 'noMore';
								}
							}
						}
					}
				}, err => {
					uni.showToast({
						title: "请求数据失败请重试"
					})
					return;
				})
			},
			view: function(id) {
				uni.navigateTo({
					url: "./view?id="+id,
				})
			},
			serch:function(){
				this.refreshing = true;
				this.getData();
			},
			clearIcon: function() {
				let that = this
				
				that.title = ""
				
				
			   that.showClearIcon = false;
			   
			   this.refreshing = true;
			   this.getData();
			},
			clearInput: function(event) {
				if (event.detail.value.length > 0) {
					this.showClearIcon = true;
				} else {
					this.showClearIcon = false;
				}
			},
		},
		filters:{
			imgFilter:function(filePath){
				return filePath;
			}
		}
	}
</script>

<style scoped lang="scss">
	page {
	  background-color:#FFFFFF;
	}
	.emptyData{
		 padding: 48rpx;
		 font-size:16px;
		 text-align: center;
		 color: #646464;
	}
	.loadMore{
		width: 100%;
		font-size: 16px;
		cursor: pointer;
		text-align: center;
		line-height: 86.4rpx;
		color: #646464;
	}
	.search{
		padding: 10px 5%;
		width: 90%;
		height: 40px;
		position: fixed;
		top:0px;
		z-index: 99999;
		background-color: #fff;
	}
	.search-title{
		float: left;
		padding: 12rpx;
		background-color: #fff;
		border: 1px solid #64d474;
		border-radius: 5px;
		width:70%;
		font-size: 16px;
		text-align: left;
		color: #808080;
		display: flex;
		position: relative;
	}
	.search-btn{
		float: right;
		padding: 12rpx;
		background-color: #64d474;
		border: 1px solid #64d474;
		border-radius: 5px;
		width: 20%;
		font-size: 16px;
		text-align: center;
		color: #fff;
	}
	.content-list{
		width: 100%;
		margin-top: 10px;
	}
	
</style>
