<template>
	<view>
		<view class="content">
			<view v-if="short">
				<view class="short">{{video.content}}</view>
				<view class="open" @click="openContent">展开</view>
			</view>
			<view v-if="!short">
				<view class="more">{{video.content}}</view>
				<view class="open" @click="upContent">收起</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data: function() {
			return {
				short:true,
			}
		},
		props: {
			video: {
				type: Object
			},
	    },
		methods:{
			openContent:function(){
				this.short=false;
			},
			upContent:function(){
				this.short=true;
			},
		},
	}
</script>

<style scoped lang="scss">
	page {
	  background-color:#FFFFFF;
	}
	.content{
		width: 100%;
		font-size: 14px;
		display: inline-block;
		.short{
			width: 90%;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			float: left;
		}
		.more{
			width: 100%;
			text-align: justify;
		}
		.open{
			width: 10%;
			color: #0055ff;
			float: left;
		}
	}
</style>
