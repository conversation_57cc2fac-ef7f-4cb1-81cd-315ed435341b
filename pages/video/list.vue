<template>
	<view>
		<view v-if="list.length==0">
			<view class="emptyData">暂无数据</view>
		</view>
		<view v-else class="list-content">
			<view v-for="(video,index) in list" :key="index" class="content-item">
				<view class="title">{{video.title}}</view>
				<view>
					<video :id="video.id" :title="video.title" :src="video.videoPath" :poster="video.posterPath"
					show-mute-btn class="video" object-fit="cover" enable-play-gesture @play="playVideo">
					</video>
				<!-- 	<video :id="index" :title="video.title" :src="video.videoPath" :poster="video.posterPath | fileFilter"
						   show-mute-btn class="video" object-fit=cover enable-play-gesture @play="playVideo" @fullscreenchange="fullScreenChange">
					</video> -->
				</view>
				<video-item :video="video"></video-item>
				<view class="label_date">
					<view class="label">{{video.label}}</view>
					<view class="date">{{video.updateDate | dateFilter}}</view>
				</view>
			</view>
			<uni-load-more :status="status" :content-text="contentText" />
		</view>
		
	</view>
</template>

<script>
	import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';
	import api from '@/api/video.js';
	import videoItem from '@/pages/video/video-item.vue'

	export default {
		components:{
			'video-item':videoItem,
			uniLoadMore
		},
		data: function() {
			return {
				list: [],
				total: 0, //总数
				refreshing: false, //为true表示正在刷新
				fetchPageNum: 1, //当前页数
				loading: true, // 为true表示加载中
				name:"",
				filePath:"",
				playIndex:"0",
				video:null,
				false:false,
				fullScreen:false,
				status: 'noMore',
				contentText: { // 加载提示
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
			}

		},
		onLoad: function(option) {
			uni.setNavigationBarTitle({
				title: '视频培训'
			});
			//默认播放第一个视频
			// this.video = uni.createVideoContext(this.playIndex);
			// this.video.play();
		},
		onShow: function() {
			this.list=[];
			this.fetchPageNum=1;
			this.refreshing = true;
			this.getData();
		},
		onReachBottom() {
			if (this.list.length < this.total) {
				this.getData();
				this.status = 'loading';
			} else {
				this.status = 'noMore'; //没有数据时显示‘没有更多’
			}

		},
		onPullDownRefresh() {
			this.refreshing = true;
			this.getData();
		},
		methods: {
			getData: function() {
				let that = this;
				that.loading = true;
				let param = {
					title: that.name
				};
				let pageNo = that.refreshing ? 1 : that.fetchPageNum;
				api.findVideoList(pageNo, param).then(res => {
					if (res.code != 0) {
						uni.showToast({
							title: "请求数据失败请重试"
						})
						return;
					} else {
						that.total = res.data.count;
						if (that.refreshing) {
							that.refreshing = false;
							uni.stopPullDownRefresh();
							that.list = res.data.list;
							that.fetchPageNum = res.data.next;
						} else {
							that.list = that.list.concat(res.data.list);
							that.fetchPageNum = res.data.next;
						}
					}
				})
			},
			playVideo:function(e){
				/* let index = e.target.id;  
				this.playIndex = index;  
				let newVideo = uni.createVideoContext(String(index));  
				if (this.video !== newVideo) {
					if(this.video !== null){
						this.video.pause();  
					}
				}  
				this.video = newVideo;  
				this.video.requestFullScreen(); */
				 // 获取当前视频id
				let currentId = e.currentTarget.id
				// uni.createVideoContext获取视频上下文对象
				this.videoContent = uni.createVideoContext(currentId)
				// 获取json对象并遍历, 停止非当前视频
				let list = this.list
				for (let i = 0; i < list.length; i++) {
				  let temp = list[i].id
				  if (temp !== currentId) {
					uni.createVideoContext(temp).pause()
				  }
				}
			},
			fullScreenChange:function(e){
				this.fullScreen = !this.fullScreen;
				if(this.fullScreen){
					this.video.play();
				}else{
					this.video.pause();  
				}
			}
		},
		filters:{
			dateFilter:function(time){
				time = time.replace("-", "/").replace("-", "/");
				var date = new Date(time),
				year = date.getFullYear(),
				month = date.getMonth() + 1,
				day = date.getDate();
				return year+'-'+month+'-'+day;
			},
			fileFilter:function (filePath) {
				return filePath + '?imageView2/q/50';
			}
		},
	}
</script>

<style scoped lang="scss">
	page {
	  background-color:#FFFFFF;
	}
	.emptyData{
		 padding: 48rpx;
		 font-size: 16px;
		 text-align: center;
		 color: #646464;
	}
	.loadMore{
		width: 100%;
		font-size: 16px;
		cursor: pointer;
		text-align: center;
		line-height: 86.4rpx;
		color: #646464;
	}
	.title{
		font-size: 16px;
		margin-bottom: 5px;
	}
	.list-content{
		.content-item{
			width: 90%;
			margin:20px 5% 0;
			padding-bottom: 10px;
			display: inline-block;
			border-bottom: 1px solid #e0e0e0;
		}
	}
	.label_date{
		font-size: 12px;
		color: #888888;
		.label{
			width: 70%;
			float: left;
		}
		.date{
			width: 30%;
			float: left;
		}
	}
	.video{
		width: 100%;
	}
</style>
