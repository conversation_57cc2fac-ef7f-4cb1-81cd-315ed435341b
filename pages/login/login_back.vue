<template>
	<view class="content">
		<view class="input-group">
			<view class="input-row border">
				<text class="title">账号：</text>
				<m-input class="m-input" type="text" clearable focus v-model="account" placeholder="请输入账号"></m-input>
			</view>
			<view class="input-row">
				<text class="title">密码：</text>
				<m-input type="password" displayable v-model="password" placeholder="请输入密码"></m-input>
			</view>
		</view>
		<view class="btn-row">
			<button type="primary" class="primary" @tap="bindLogin">登录</button>
			<button type="primary" class="primary" @tap="toPhoneLogin">手机登录</button>
			<button class="confirm-btn" open-type="getUserInfo" @getuserinfo="wxLogin" :disabled="logining">微信授权登录</button>
		</view>
	</view>
</template>

<script>
	import api from '../../api/login.js';
	import {
		mapState,
		mapMutations
	} from 'vuex'
	import mInput from '../../components/m-input.vue'

	export default {
		components: {
			mInput
		},
		data() {
			return {
				account: '',
				password: '',
				logining: false,
			}
		},
		computed: mapState(['userInfo']),
		onLoad() {
			/* console.log("vuex属性 开始");
			console.log(this.userInfo);
			console.log("vuex属性 结束"); */
		},
		methods: {
			...mapMutations(['login']),
			bindLogin() {
				/**
				 * 客户端对账号信息进行一些必要的校验。
				 * 实际开发中，根据业务需要进行处理，这里仅做示例。
				 */
				if (!this.account) {
					uni.showToast({
						icon: 'none',
						title: '请输入账号'
					});
					return;
				}
				if (!this.password.length) {
					uni.showToast({
						icon: 'none',
						title: '请输入密码'
					});
					return;
				}

				let loginUser = {
					username: this.account,
					password: this.password
				}
				api.login(loginUser).then(res => {
					if (res.code === 1) {
						uni.showToast({
							icon: 'none',
							title: res.message,
							mask: true,
							duration: 1500
						});
						return;
					}
					let userInfo = {
						"loginUser": loginUser,
						"token": res.data.token,
						"principal": res.data.principal,
						"hasLogin": true
					}
					this.login(userInfo);
					uni.showToast({
						icon: 'none',
						title: '登录成功',
						mask: true,
						duration: 1500
					});
					uni.reLaunch({
						url: '/pages/index/index',
					});
					console.log("跳转")
				})
			},
			wxLogin(e) {
				console.log("wxLogin")
				console.log(e)
				const that = this;
				that.logining = true;
				let userInfo = e.detail.userInfo;
				uni.getProvider({
					service: "oauth",
					success: (res) => {
						//支持微信
						console.log(res)
						if (~res.provider.indexOf('weixin')) {
							uni.login({
								provider: "weixin",
								success: (login_res) => {
									let code = login_res.code;
									console.log(login_res)
									
									
									uni.getUserInfo({
										success(info_res) {
											console.log(info_res)
											let xmLoginParam={
												code:code,
												rawData: info_res.rawData
											}
											api.wxLogin(xmLoginParam).then(res => {
												console.log(res)
											})
											/* uni.request({
												url: 'http://localhost:8080/wxlogin',
												method: "POST",
												header: {
													'content-type': 'application/x-www-form-urlencoded'
												},
												data: {
													code: code,
													rawData: info_res.rawData
												},
												success(res) {
													if (res.data.status == 200) {
														that.$store.commit('login', userInfo);
														// uni.setStorageSync("userInfo",userInfo);
														// uni.setStorageSync("skey", res.data.data);
													} else {
														console.log('服务器异常')
													}
												},
												fail(error) {
													console.log(error)
												}
											}) */
											//uni.hideLoading()
											//uni.navigateBack()
										}
									})

								},
								fail: () => {
									uni.showToast({
										title: "微信登录授权失败",
										icon: "none"
									});
								}
							})
						} else {
							uni.showToast({
								title: '请先安装微信或升级版本',
								icon: "none"
							});
						}
					},

				})

			},
			toPhoneLogin(){
				uni.navigateTo({
					url: "phoneLogin",
				})
			}
		},
		onReady() {

		}
	}
</script>

<style>
	.action-row {
		display: flex;
		flex-direction: row;
		justify-content: center;
	}

	.action-row navigator {
		color: #007aff;
		padding: 0 10px;
	}

	.oauth-row {
		display: flex;
		flex-direction: row;
		justify-content: center;
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
	}

	.oauth-image {
		position: relative;
		width: 50px;
		height: 50px;
		border: 1px solid #dddddd;
		border-radius: 50px;
		margin: 0 20px;
		background-color: #ffffff;
	}

	.oauth-image image {
		width: 30px;
		height: 30px;
		margin: 10px;
	}

	.oauth-image button {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		opacity: 0;
	}
</style>
