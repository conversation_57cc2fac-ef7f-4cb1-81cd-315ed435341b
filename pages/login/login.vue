<template>
	<view class="content">
		<image :src="imgPath+'img/logo_bg.jpg'" alt="" class="bg_img" style="z-index: 0;top: 0;" />
		<div class="logo">
			<p>食用农产品合格证服务</p>
		</div>
		<view class="btn-row" style="width: 75%;">
			<view class="input-group">
				<view class="input-row">
					<image :src="imgPath+'img/icon1.png'">
					<m-input class="m-input" type="number" clearable focus v-model="phoneNumber" placeholder="输入手机号"></m-input>
				</view>
				<view class="input-row">
					<image :src="imgPath+'img/icon2.png'">
					<m-input class="m-input-yzm" type="number" v-model="captcha" placeholder="输入验证码"></m-input>
					<button type="default" size="mini" @tap="onLiderVerify" :disabled="disabled" class="yzm" style="padding: 0 1em;">{{countdownText}}</button>
				</view>
			</view>
			<view class="agree">
				<checkbox-group @change="changeAgree">
					<view>
						<checkbox :value="agree" style="transform:scale(0.7)"/>
						我已阅读并同意<span style="color: #09BB07;" @click="agreement()">《用户服务协议》</span>及<span style="color: #09BB07;" @click="privacy">《隐私政策》</span>
					</view>
				</checkbox-group>
			</view>
			<slider-verify :isShow="sliderVerifyFLag" @touchSliderResult="verifyResult" ref="verifyElement"></slider-verify>
			<view class="dl_all">
				<button v-if="showPrivacy" type="primary" class="primary wxdl" @tap="onGetPrivacy">登录</button>
				<button v-else type="primary" class="primary wxdl" @tap="onBindLogin">登录</button>
				<!-- <button v-if="authFlag" open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber">获取微信手机号</button> -->
			</view>
		</view>
		<privacy-popup ref="privacyPopup" @confirm="onPrivacyConfirm"></privacy-popup>
	</view>
</template>

<script>
	import api from '../../api/login.js';
	import mInput from '../../components/m-input.vue'
	import sliderVerify from '@/components/slider-verify/slider-verify.vue';
	import PrivacyPopup from "@/pages/login/privacyPopup.vue";
	var graceChecker = require("../../common/graceui-dataChecker/graceChecker.js");
	var config = require('../../common/config.js')
	export default {
		components: {
			mInput,
			'slider-verify': sliderVerify,
			PrivacyPopup,
		},
		data() {
			return {
				imgPath:config.IMG_PATH,
				phoneNumber: '',
				captcha: '',
				authFlag: false,
				countdown: 60,
				disabled: false,
				countdownText: "获取验证码",
				timer: '',
				sliderVerifyFLag: false, //滑块验证,
				agree: false,
				showPrivacy: getApp().globalData.showPrivacy,
			}
		},
		async onLoad() {
			/* let openId = uni.getStorageSync("openId");
			if (openId) {
				this.authFlag = true;
			} */
		},
		methods: {
			//隐私确认
			onPrivacyConfirm() {
				this.showPrivacy = false;
				this.onBindLogin();
			},
			onGetPrivacy() {
				if (getApp().globalData.showPrivacy) {
					this.$refs.privacyPopup.$refs.popup.open();
					return;
				}
			},
			changeAgree(){
				this.agree = !this.agree
			},
			agreement(){
				uni.navigateTo({
				  url: '/pages/login/agreementView'
				});
			},
			privacy(){
				uni.navigateTo({
				  url: '/pages/login/privacyView'
				});
			},
			onGetCaptcha() {
				let rule = [{
						name: "phoneNumber",
						checkType: "notnull",
						errorMsg: "请填写手机号"
					},
					{
						name: "phoneNumber",
						checkType: "phoneno",
						errorMsg: "手机号格式错误"
					},
				];
				let validateData = {
					phoneNumber: this.phoneNumber
				}
				let checkRes = graceChecker.check(validateData, rule);
				if (!checkRes) {
					uni.showToast({
						title: graceChecker.error,
						icon: "none"
					});
					return;
				}

				let that = this;
				return new Promise((resolve, reject) => {
					that.disabled = true; //禁用点击
					api.getCaptcha(that.phoneNumber).then(res => {
						if (res.code == 1) {
							that.disabled = false; //禁用点击
							uni.showToast({
								icon: 'none',
								title: res.message
							});
							return;
						}
						that.captcha = res.data;
						that.countdownText = that.countdown + "秒后可重试";
						that.timer = setInterval(that.onCountDown, 1000);
						resolve();
					})
				})
			},
			// 倒计时
			onCountDown() {
				let that = this;
				if (that.countdown <= 1) {
					that.disabled = false;
					that.countdown = 60;
					that.countdownText = '获取验证码';
					clearInterval(that.timer);
				} else {
					that.countdownText = --that.countdown + "秒后可重试";
				}
			},
			onBindLogin() {
				uni.showLoading({
				    title: '登录中',
					mask:true,
				});
				let rule = [{
						name: "phoneNumber",
						checkType: "notnull",
						errorMsg: "请填写手机号"
					},
					{
						name: "phoneNumber",
						checkType: "phoneno",
						errorMsg: "手机号格式错误"
					},
					{
						name: "captcha",
						checkType: "notnull",
						errorMsg: "请填写验证码"
					},
					{
						name: "captcha",
						checkType: "captcha",
						errorMsg: "请输入4位数字验证码"
					},
				];
				let validateData = {
					phoneNumber: this.phoneNumber,
					captcha: this.captcha
				}
				let checkRes = graceChecker.check(validateData, rule);

				if (!checkRes) {
					uni.hideLoading();
					uni.showToast({
						title: graceChecker.error,
						icon: "none"
					});
					return;
				}
				
				if(!this.agree){
					  uni.showToast({
						title: '请勾选服务协议',
						icon: 'none'
					  });
					  return;
				  }
				  
				let loginUser = {
					phoneNumber: this.phoneNumber,
					captcha: this.captcha,
					code: uni.getStorageSync("openId")
				}
				let that = this;
				return new Promise((resolve, reject) => {
					api.login(loginUser).then(res => {
						if (res.code === 1) {
							uni.hideLoading();
							uni.showToast({
								icon: 'none',
								title: res.message,
								mask: true,
								duration: 1500
							});
							return;
						}
						uni.setStorageSync('token', res.data.token);
						uni.setStorageSync('userInfo', res.data.userInfo);
						//uni.setStorageSync('ent', res.data.ent);
						uni.hideLoading();
						uni.showToast({
							icon: 'none',
							title: '登录成功',
							mask: true,
							duration: 1500
						});
						this.$Router.replaceAll("/pages/index/index")
						resolve();
					})
				})
			},
			onGetPhoneNumber(e) {
				let that = this;
				return new Promise((resolve, reject) => {
					if (e.detail.errMsg == "getPhoneNumber:ok") {
						console.log('用户点击了接受');
						let iv = e.detail.iv
						let encryptedData = e.detail.encryptedData
						let param = {
							iv: iv,
							encryptedData: encryptedData,
							sessionKey: uni.getStorageSync("sessionKey"),
						}
						api.getWechatPhoneNumber(param).then(async res => {
							if (res.code === 1) {
								uni.showToast({
									icon: 'none',
									title: res.message,
									mask: true,
									duration: 1500
								});
								return;
							}
							that.phoneNumber = res.data.phoneNumber;
							resolve()
						})
					} else {
						console.log('用户点击了拒绝');
					}
				})
			},
			onLiderVerify() {

				let rule = [{
						name: "phoneNumber",
						checkType: "notnull",
						errorMsg: "请填写手机号"
					},
					{
						name: "phoneNumber",
						checkType: "phoneno",
						errorMsg: "手机号格式错误"
					},
				];
				let validateData = {
					phoneNumber: this.phoneNumber
				}
				let checkRes = graceChecker.check(validateData, rule);

				if (!checkRes) {
					uni.showToast({
						title: graceChecker.error,
						icon: "none"
					});
					return;
				}

				this.sliderVerifyFLag = true;
			},
			verifyResult(res) {
				this.sliderVerifyFLag = false;
				if (res) {
					//校验通过
					this.onGetCaptcha();
				} else {
					// 校验失败,点击关闭按钮
					/* uni.showToast({
						icon: 'none',
						title: "滑动验证失败",
						mask: true,
						duration: 1500
					}); */
				}
			}
		},
		onReady() {

		}
	}
</script>

<style lang="scss" scoped>
	.dl_all {
		width: 80%;
		margin: 20px auto 0;

		.wxdl {
			width: 100%;
			background-image: linear-gradient(to left, #269536, #27d776);
			border-radius: 25px;
			margin-top: 20px;
		}
	}

	.input-row {
		width: 100%;
		height: 50px;
		border-bottom: 1px solid #eeeeee;

		image {
			float: left;
			width: 15px;
			height: 22px;
			margin-top: 14px;
		}

		.m-input {
			float: left;
			width: 75%;
			height: 50px;
			line-height: 50px;
			font-size: 14px;
		}

		.m-input-yzm {
			float: left;
			width: 40%;
			height: 50px;
			line-height: 50px;
			font-size: 14px;
		}

		.yzm {
			float: right;
			/* width: 35%; */
			/* height: 50px;
			line-height: 50px; */
			background: none;
			border: none;
			font-size: 14px;
			/* color:#174cbb ; */
			margin-top: 10px;

		}
	}
	.agree{
		display: flex;
		font-size: 23upx;
		color: #919191;
		justify-content: space-between;
		margin-bottom: 20upx;
		margin-top: 20upx;
		margin-left: -5px;
	}
	/* .action-row {
		display: flex;
		flex-direction: row;
		justify-content: center;
	}

	.action-row navigator {
		color: #007aff;
		padding: 0 10px;
	}

	.oauth-row {
		display: flex;
		flex-direction: row;
		justify-content: center;
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
	}

	.oauth-image {
		position: relative;
		width: 50px;
		height: 50px;
		border: 1px solid #dddddd;
		border-radius: 50px;
		margin: 0 20px;
		background-color: #ffffff;
	}

	.oauth-image image {
		width: 30px;
		height: 30px;
		margin: 10px;
	}

	.oauth-image button {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		opacity: 0;
	} */
</style>
