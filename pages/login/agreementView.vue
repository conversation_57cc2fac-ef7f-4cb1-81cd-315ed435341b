<template>
	<view class="index">
		<view style="text-align: center;font-size: 38upx; font-weight: bold;">承诺达标合格证服务用户服务协议</view>
		<view style="text-align: right;font-weight: bold;padding: 30px 15px 5px 0px;">版本更新日期：2022年2月8日</view>
		<p>
			为使用承诺达标合格证服务小程序服务（以下简称“本服务”或小程序服务），您应当阅读并遵守《承诺达标合格证服务微信小程序用户协议》（以下简称“本协议”）。为了保障您（以下简称“用户”）的权益，请在使用小程序服务之前，详细阅读此服务协议所有内容，特别是加粗部分。当用户开始使用承诺达标合格证服务小程序（以下简称“小程序”、“平台”），则默认同意并签署了本协议。
		</p>
		<p>
			本协议内容包括协议正文、承诺达标合格证服务小程序已经发布的或将来可能发布的各类规则均为本协议不可分割的组成部分具有同等法律效力。除另行明确声明外，用户使用承诺达标合格证服务小程序服务均受本协议约束。
		</p>
		<p class="ft18">第一条 定义</p>
		<p>
			1、承诺达标合格证服务：是指承诺达标合格证服务经营者，即吉林省吉科软信息技术有限公司及其关联公司，作为平台经营者，目前我们为注册用户提供的服务包括但不限于承诺达标合格证开具服务等相关内容。
		</p>
		<p>
			2、承诺达标合格证服务规则：包括所有承诺达标合格证服务已经发布或将来可能发布、修订的各类规则、规范、规则解读、实施细则、通知、公告等。
		</p>
		
		<p class="ft18">第二条 承诺达标合格证服务小程序服务协议的修订</p>
		
		<p style="font-weight: bold;">
			承诺达标合格证服务小程序对本协议进行修改，相关内容将通过电子邮件或公告、电话等方式提示用户。用户在使用承诺达标合格证服务小程序服务时，可及时查阅了解。用户如继续使用承诺达标合格证服务小程序服务，则视为对修改内容的同意，当发生有关争议时，以最新的服务协议为准；用户在不同意修改内容的情况下，有权停止使用本协议涉及的服务。
		</p>
		<p class="ft18">第三条 承诺达标合格证服务小程序服务规范</p>
		<p style="font-weight: bold;">
			1、承诺达标合格证服务小程序是为用户提供承诺达标合格证开具服务的平台。承诺达标合格证服务小程序会对所涉及的用户信息进行严格审核，并在提交其身份、联系方式等真实信息，以进行核验、登记，建立登记档案。
		</p>
		<p>
			2、用户同意并保证：为了更好的为用户提供服务，承诺达标合格证服务小程序有权记录用户在线填写的所有信息，并提供给承诺达标合格证服务小程序的关联公司。用户保证信息准确、合法，同时承担因信息错误导致的后果。
		</p>
		<p class="ft18">第四条 承诺达标合格证服务小程序使用规则</p>
		<p>
			1、小程序在使用本服务前需要先进行注册和认证。用户开始注册使用小程序，用户授权登录即注册完成。
		</p>
		<p>
			2、用户注册成功后，平台将根据用户身份要素识别用户身份和授权登录。“身份要素”包括但不限于用户的账号名称、密码、短信校验码、手机号码、身份证件号码及人脸信息等。
		</p>
		<p>
			3、如用户发现账号遭他人非法使用，应立即通知平台。因黑客行为或用户自身保管疏忽导致账号、密码遭他人非法使用所发生的一切责任，均应由用户本人承担，平台不承担任何责任。
		</p>
		<p>
			4、用户在使用承诺达标合格证服务小程序服务时填写、登录、使用的账号名称、头像、个人简介等账号信息资料应遵守法律法规、社会主义制度、国家利益、公民合法权益、公共秩序、社会道德风尚和信息真实性等七条底线，不得在账号信息资料中出现违法和不良信息，且用户保证在填写、登录、使用账号信息资料时，不得有以下情形：
		</p>
		<p>
			<ul>
				<li>（1）违反宪法或法律法规规定的；</li>
				<li>（2）危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的；</li>
				<li>（3）损害国家荣誉和利益的，损害公共利益的；</li>
				<li>（4）煽动民族仇恨、民族歧视，破坏民族团结的；</li>
				<li>（5）破坏国家宗教政策，宣扬邪教和封建迷信的；</li>
				<li>（6）散布谣言，扰乱社会秩序，破坏社会稳定的；</li>
				<li>（7）散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的；</li>
				<li>（8）侮辱或者诽谤他人，侵害他人合法权益的；</li>
				<li>（9）含有法律、行政法规禁止的其他内容的。</li>
			</ul>
		</p>
		
		<p>
			5、若用户登录、使用账号头像、个人简介等账号信息资料存在违法和不良信息的，承诺达标合格证服务小程序有权采取通知限期改正、暂停使用等措施。对于冒用关联机构或社会名人登录、使用、填写账号名称、头像、个人简介，承诺达标合格证服务小程序有权取消该账号在平台上使用，并向政府主管部门进行报告。
		</p>
		<p style="font-weight: bold;">
			6、由于承诺达标合格证服务小程序上的商品的数量大、互联网技术因素等客观原因存在，承诺达标合格证服务小程序上显示的信息可能存在一定的滞后和误差，对此请用户知悉并理解。承诺达标合格证服务小程序会尽最大努力避免这种情况的发生。
		</p>
		<p class="ft18">第五条 其他约定</p>
		<p>
			1、责任范围
		</p>
		<p>
			承诺达标合格证服务小程序对不可抗力或其他合法范围内的不能预见、不能避免并不能克服的客观情况导致的损失不承担责任，包括：不可抗力因素、法律法规或政府指令的变更，因网络服务特性而特有的原因，例如境内外基础电信运营商的故障、计算机或互联网相关技术缺陷、互联网覆盖范围限制、计算机病毒、黑客攻击等因素，及其他合法范围内的不能预见、不能避免并不能克服的客观情况。
		</p>
		<p>
			2、服务中止、中断及终止：承诺达标合格证服务小程序根据自身商业决策等原因可能会选择中止、中断及终止承诺达标合格证服务小程序服务。如有此等情形发生，承诺达标合格证服务小程序会采取公告的形式通知用户。<span style="font-weight: bold;">经国家行政或司法机关的生效法律文书确认用户存在违法或侵权行为，承诺达标合格证服务小程序有权中止、中断或终止向用户提供服务；或者承诺达标合格证服务小程序根据自身的判断，认为用户的行为涉嫌违反本协议的协议或涉嫌违反法律法规的规定的，则承诺达标合格证服务小程序有权中止向用户提供服务，且无须为此向用户或任何第三方承担责任。</span>
		</p>
		<p>
			3、所有权及知识产权：
		</p>
		<p>
			<ul>
				<li>（1）<span style="font-weight: bold;">承诺达标合格证服务小程序上所有内容，包括但不限于文字、软件、声音、图片、录像、图表、网站架构、网站画面的安排、网页设计、在广告中的全部内容、商品以及其它信息均由承诺达标合格证服务小程序或其他权利人依法拥有其知识产权，包括但不限于商标权、专利权、著作权、商业秘密等。</span>非经承诺达标合格证服务小程序或其他权利人书面同意，用户不得擅自使用、修改、全部或部分复制、公开传播、改变、散布、发行或公开发表、转载、引用、链接、抓取或以其他方式使用本平台程序或内容。如有违反，用户同意承担由此给承诺达标合格证服务小程序或其他权利人造成的一切损失。</li>
				<li>（2）承诺达标合格证服务小程序尊重知识产权并注重保护用户享有的各项权利。</li>
			</ul>
		</p>
		
		<p style="font-weight: bold;">
			4、承诺达标合格证服务小程序保留删除平台上各类不符合法律政策或不真实的信息内容而无须通知用户的权利。
		</p>
		<p>
			5、通知：所有发给用户的通知都可通过电子邮件、短信、常规的信件或在承诺达标合格证服务小程序显著位置公告的方式进行传送。为使用户及时、全面了解承诺达标合格证服务小程序的各项服务，用户同意，承诺达标合格证服务小程序可以多次、长期向用户发送各类商业性短信息而无需另行获得用户的同意。
		</p>
		<p>
			6、本协议适用中华人民共和国大陆地区施行的法律。当本协议的任何内容与中华人民共和国法律相抵触时，应当以法律规定为准，同时相关协议将按法律规定进行修改或重新解释，而本协议其他部分的法律效力不变。
		</p>
		<p style="font-weight: bold;">
			7、用户在使用承诺达标合格证服务小程序服务过程中，与平台服务产生任何纠纷的，应与吉林省吉科软信息技术有限公司友好协商，若协商不成，应提交承诺达标合格证服务小程序关联公司的实际住所地法院诉讼。
		</p>
		<p>
			8、如果用户对本协议内容有任何疑问，请拨打承诺达标合格证服务小程序客服热线{{serviceTel}}。
		</p>
		<p>
			9、生效日期。本协议于2022年2月8日首次生效。
		</p>
		
		<p><audio style="display: none;" controls="controls"></audio></p>
	</view>
</template>

<script>
	var config = require('../../common/config.js')
	export default {
		data() {
			return {
				powerBy: config.POWER_BY,
				serviceTel: config.SERVICE_TEL,
			}
		},
		methods: {
			
		}
	}
</script>

<style scoped>
	.index {
		font-family:"宋体";
		font-size:14px;
		padding: 40upx;
		font-weight: normal;
	}
	p {
		text-indent:30px;
		line-height: 25px;
		padding: 10upx 10upx;
	}
	.ft18 { font-size:18px;}
</style>
