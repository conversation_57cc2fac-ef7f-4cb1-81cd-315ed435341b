<template>
	<view class="content">
		<image :src="imgPath+'img/logo_bg.jpg'" alt="" class="bg_img" style="z-index: 0;top: 0;" /> 
		<div class="logo">
			<p>食用农产品合格证服务</p>
		</div>
		<view class="btn-row">
			<button type="primary" class="primary sjdl" @tap="toLogin">手机登录</button>
			<button v-if="showPrivacy" type="primary" class="primary wxdl" @tap="onGetPrivacy">微信授权</button>
			<button v-else type="primary" class="primary wxdl" open-type="getUserInfo" @getuserinfo="onGetAuth">微信绑定</button>
		</view>
		<privacy-popup ref="privacyPopup" @confirm="onPrivacyConfirm"></privacy-popup>
	</view>
</template>

<script>
	import api from '../../api/login.js'; 
	import PrivacyPopup from "@/pages/login/privacyPopup.vue";
	var config = require('../../common/config.js')
	export default {
		components: {
			PrivacyPopup,
		},
		data() {
			return {
				imgPath:config.IMG_PATH,
				showPrivacy: getApp().globalData.showPrivacy,
			} 
		},
		onShow() {
			// #ifdef MP 
				wx.hideHomeButton()
			// #endif			
		},
		onLoad() {
		},
		methods: {
			//隐私确认
			onPrivacyConfirm() {
				this.showPrivacy = false;
				this.onGetAuth();
			},
			onGetPrivacy() {
				if (getApp().globalData.showPrivacy) {
					this.$refs.privacyPopup.$refs.popup.open();
					return;
				}
			},
			toLogin(){
				this.$Router.push('/pages/login/login');
				/* uni.navigateTo({
					url: '/pages/login/login',
				}); */
			},
			onGetAuth(e) {
				uni.showLoading({
				    title: '授权中',
					mask:true,
				});
				const that = this;
				//let userInfo = e.detail.userInfo;
				uni.getProvider({
					service: "oauth",
					success: (res) => {
						//支持微信
						if (~res.provider.indexOf('weixin')) {
							uni.login({
								provider: "weixin",
								success: (login_res) => {
									uni.getUserInfo({
										success(info_res) {
											uni.setStorageSync('wechatUser', JSON.parse(info_res.rawData));
											let param = {
												code: login_res.code,
												rawData:info_res.rawData,
											}
											 api.auth(param).then(auth_res => {
												if(auth_res.code==0){
													that.code=auth_res.data.openId;
													uni.setStorageSync('openId', auth_res.data.openId);
													uni.setStorageSync('sessionKey', auth_res.data.sessionKey);
													/* uni.reLaunch({
														url: '/pages/login/login',
													}); */
													uni.hideLoading();
													that.$Router.push('/pages/login/login');
												}
											}) 
										},
										fail: (e) => {
											uni.hideLoading();
											uni.showToast({
												title: "微信登录授权失败",
												icon: "none"
											});
										}
									}) 
								},
								fail: (e) => {
									uni.hideLoading();
									uni.showToast({
										title: "微信登录授权失败",
										icon: "none"
									});
								},

							})
						} else {
							uni.hideLoading();
							uni.showToast({
								title: '请先安装微信或升级版本',
								icon: "none"
							});
						}
					},
					fail:(e)=>{
					},
				})
			},
		},
		onReady() {

		}
	}
</script>

<style lang="scss" scoped>
	.sjdl {
		width: 100%;
		background-image: linear-gradient(to left,#5cb7f8,#2989cc);
		border-radius: 25px;
	}
	.wxdl {
		width: 100%;
		background-image: linear-gradient(to left,#269536,#27d776);
		border-radius: 25px;
		margin-top: 20px;
	}
</style>
