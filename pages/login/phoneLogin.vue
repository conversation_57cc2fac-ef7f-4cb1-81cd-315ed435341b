<template>
	<view class="content">
		<view class="input-group">
			<view class="input-row border">
				<text class="title">手机号：</text>
				<m-input class="m-input" type="number" clearable focus v-model="phoneNumber" placeholder="请输入手机号"></m-input>
			</view>
			<view class="input-row">
				<text class="title">验证码：</text>
				<m-input type="number" displayable v-model="captcha" placeholder="请输入验证码"></m-input>
			</view>
		</view>
		<view class="btn-row">
			<button type="primary" class="primary" @tap="getCaptcha">获取验证码</button>
			<button type="primary" class="primary" @tap="bindLogin">登录</button>
			<button class="confirm-btn" open-type="getUserInfo" @getuserinfo="wxLogin" :disabled="logining">微信授权获取用户信息</button>
			<button open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber">获取手机号</button>
		</view>
	</view>
</template>

<script>
	import api from '../../api/login.js';
	import {
		mapState,
		mapMutations
	} from 'vuex'
	import mInput from '../../components/m-input.vue'

	export default {
		components: {
			mInput
		},
		data() {
			return {
				phoneNumber: '',
				captcha: '',
				logining: false,
			}
		},
		computed: mapState(['userInfo']),
		onLoad() {
			/* console.log("vuex属性 开始");
			console.log(this.userInfo);
			console.log("vuex属性 结束"); */
		},
		methods: {
			...mapMutations(['login']),
			getCaptcha() {
				let that = this;
				if (!that.phoneNumber) {
					uni.showToast({
						icon: 'none',
						title: '请输入手机号'
					});
					return;
				}
				api.getCaptcha(that.phoneNumber).then(res => {
					console.log(res)
					that.captcha = res.data;
				})
			},
			bindLogin() {
				/**
				 * 客户端对账号信息进行一些必要的校验。
				 * 实际开发中，根据业务需要进行处理，这里仅做示例。
				 */
				if (!this.phoneNumber) {
					uni.showToast({
						icon: 'none',
						title: '请输入手机号'
					});
					return;
				}
				if (!this.captcha) {
					uni.showToast({
						icon: 'none',
						title: '请输入验证码'
					});
					return;
				}

				let loginUser = {
					phoneNumber: this.phoneNumber,
					captcha: this.captcha
				}
				api.login(loginUser).then(res => {
					if (res.code === 1) {
						uni.showToast({
							icon: 'none',
							title: res.message,
							mask: true,
							duration: 1500
						});
						return;
					}
					let userInfo = {
						"loginUser": loginUser,
						"token": res.data.token,
						"principal": res.data.principal,
						"hasLogin": true
					}
					this.login(userInfo);
					uni.showToast({
						icon: 'none',
						title: '登录成功',
						mask: true,
						duration: 1500
					});
					uni.reLaunch({
						url: '/pages/index/index',
					});
					console.log("跳转")
				})
			},
			wxLogin(e) {
				console.log("wxLogin")
				console.log(e)
				const that = this;
				that.logining = true;
				let userInfo = e.detail.userInfo;
				uni.getProvider({
					service: "oauth",
					success: (res) => {
						//支持微信
						console.log(res)
						if (~res.provider.indexOf('weixin')) {
							uni.login({
								provider: "weixin",
								success: (login_res) => {
									let code = login_res.code;
									console.log(login_res)
									uni.getUserInfo({
										success(info_res) {
											console.log(info_res)
											let xmLoginParam = {
												code: code,
												rawData: info_res.rawData
											}
											/* api.wxLogin(xmLoginParam).then(res => {
												console.log(res)
											}) */
										}
									})

								},
								fail: () => {
									uni.showToast({
										title: "微信登录授权失败",
										icon: "none"
									});
								}
							})
						} else {
							uni.showToast({
								title: '请先安装微信或升级版本',
								icon: "none"
							});
						}
					},

				})

			},
			onGetPhoneNumber(e) {
				if(e.detail.errMsg=="getPhoneNumber:ok"){
				        console.log('用户点击了接受');
						console.log(e);
				        //e.detail这里会有三个属性
				        //encryptedData
				        //errMsg   
				        //iv
				    }else{
				        console.log('用户点击了拒绝') ;  
				    }
			}
		},
		onReady() {

		}
	}
</script>

<style>
	.action-row {
		display: flex;
		flex-direction: row;
		justify-content: center;
	}

	.action-row navigator {
		color: #007aff;
		padding: 0 10px;
	}

	.oauth-row {
		display: flex;
		flex-direction: row;
		justify-content: center;
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
	}

	.oauth-image {
		position: relative;
		width: 50px;
		height: 50px;
		border: 1px solid #dddddd;
		border-radius: 50px;
		margin: 0 20px;
		background-color: #ffffff;
	}

	.oauth-image image {
		width: 30px;
		height: 30px;
		margin: 10px;
	}

	.oauth-image button {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		opacity: 0;
	}
</style>
