<template>
	<view>
		<form @submit="formSubmit">
			<view class="title_all">
				<image :src="imgPath+'img/index/img9.png'" alt="" />
				<p>录入员信息</p>
			</view>
			<div class="content_all">
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>录入员姓名:
					</div>
					<div class="whxx_right">
						<input class="uni-input" maxlength="20" name="enterName" v-model="form.enterName" placeholder="请填写录入员姓名">
					</div>
				</div>
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>录入员手机号:
					</div>
					<div class="whxx_right">
						<input class="uni-input" maxlength="20" type='number' name="enterTel" v-model="form.enterTel" placeholder="请填写录入员手机号" />
					</div>
				</div>
			</div>
			<view class="title_all">
				<image :src="imgPath+'img/index/img9.png'" alt="" />
				<p>基本信息</p>
			</view>
			<div class="content_all">
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>名称:
					</div>
					<div class="whxx_right">
						{{form.entName}}
					</div>
				</div>
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>养殖类型:
					</div>
					<div class="whxx_right">
						<radio-group @change="entTypeChange">
							<view class="news-item" v-for="(item,index) in entTypeList" :key="index" style="">
								<label class="radio">
									<div style="">
										<radio :value="index+''" :checked="item.value == form.entTypeCode" style="float: left;transform:scale(0.7);margin-top: -2px;" />
										<p style="font-size: 14px;float: left;">{{item.label}}</p>
									</div>
								</label>
							</view>
						</radio-group>
					</div>
				</div>
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>养殖品种:
					</div>
					<div class="whxx_right">
						<view @click="showfarmLivestock">{{form.farmValue?form.farmValue:'请选择养殖品种'}}</view>
						<basic-farm-select ref="basicFarmSelectRef" :selectList='farmLivestockList' @confirm="onBasicFarmSelectConfirm"></basic-farm-select>
					</div>
				</div>
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>身份证号:
					</div>
					<div class="whxx_right">
						<input class="uni-input" maxlength="20" name="idCard" type="idcard" v-model="form.idCard" placeholder="请填写负责人身份证号" />
					</div>
				</div>
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>负责人姓名:
					</div>
					<div class="whxx_right">
						<input class="uni-input" maxlength="20" name="legalPerson" v-model="form.legalPerson" placeholder="请填写负责人姓名" />
					</div>
				</div>
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>手机号:
					</div>
					<div class="whxx_right">
						{{form.telephone}}
					</div>
				</div>
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>所在区域:
					</div>
					<div class="whxx_right">
						{{form.areaAddress}}
					</div>
				</div>
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>所在街道、社区、村镇:
					</div>
					<div class="whxx_right">
						<picker @change="streetPickerChange" :range="streetList" :range-key="'name'">
							<view class="uni-input">
								{{streetList[form.streetIndex]?streetList[form.streetIndex].name:'请选择所在街道、社区、村镇'}}
							</view>
						</picker>
					</div>
				</div>
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>详细地址:
					</div>
					<div class="whxx_right">
						<input class="uni-input" maxlength="50" name="address" v-model="form.address" placeholder="请填写村（屯）或门牌号" style="width: 80%;float: left;" />
						<view style="float: left;width: 20%;" @click="clickMap">
							<image :src="imgPath+'img/location.png'" alt="" style="width: 20px;height: 20px;" />
						</view>
					</div>
				</div>
				<div class="whxx_all" v-if="form.entTypeCode=='et02'">
					<div class="whxx_left">
						<span v-if="!livestockCodeFlag">*</span>畜禽养殖代码:
					</div>
					<div class="whxx_right">
						<input v-if="!livestockCodeFlag" class="uni-input input_left" type='number' maxlength="20" name="livestockCode" v-model="form.livestockCode"
						 placeholder="请填写畜禽养殖代码" />
						<evan-checkbox v-model="livestockCodeFlag" :primaryColor="'#09BB07'" class="input_right">无</evan-checkbox>
					</div>
				</div>
				<div class="whxx_all" v-if="form.entTypeCode=='et02'">
					<div class="whxx_left">
						<span v-if="!cardNoFlag">*</span>统一社会信用代码:
					</div>
					<div class="whxx_right">
						<input v-if="!cardNoFlag" class="uni-input input_left" maxlength="18" name="cardNo" v-model="form.cardNo" placeholder="请填写统一社会信息代码" />
						<evan-checkbox v-model="cardNoFlag" :primaryColor="'#09BB07'" class="input_right">无</evan-checkbox>
					</div>
				</div>
				<div class="whxx_all" v-if="form.entTypeCode=='et02'">
					<div class="whxx_left">
						<span v-if="!preventionNoFlag">*</span>合格证号:
					</div>
					<div class="whxx_right">
						<input v-if="!preventionNoFlag" class="uni-input input_left" type='number' maxlength="20" name="preventionNo" v-model="form.preventionNo"
						 placeholder="请填写防疫条件合格证号" />
						<evan-checkbox v-model="preventionNoFlag" :primaryColor="'#09BB07'" class="input_right">无</evan-checkbox>
					</div>
				</div>
				<div class="whxx_all" v-if="form.entTypeCode=='et02'">
					<div class="whxx_left">
						<span v-if="!permitNoFlag">*</span>种畜经营许可证号:
					</div>
					<div class="whxx_right">
						<input v-if="!permitNoFlag" class="uni-input input_left" maxlength="30"  name="permitNo" v-model="form.permitNo" placeholder="请填写种畜经营许可证号" />
						<evan-checkbox v-model="permitNoFlag" :primaryColor="'#09BB07'" class="input_right">无</evan-checkbox>
					</div>
				</div>
				<div class="whxx_all" v-if="form.entTypeCode=='et02'">
					<div class="whxx_left">
						<span v-if="!pollutionNoFlag">*</span>排污许可证号:
					</div>
					<div class="whxx_right">
						<input v-if="!pollutionNoFlag" class="uni-input input_left" type='number' maxlength="30" name="pollutionNo" v-model="form.pollutionNo"
						 placeholder="请填写排污许可证号" />
						<evan-checkbox v-model="pollutionNoFlag" :primaryColor="'#09BB07'" class="input_right">无</evan-checkbox>
					</div>
				</div>
				<div class="whxx_all" v-if="form.entTypeCode=='et02'">
					<div class="whxx_left">
						<span>*</span>是否环评:
					</div>
					<div class="whxx_right">
						<picker @change="reportPickerChange" :range="reportList" :range-key="'label'">
							<view class="uni-input">
								{{reportList[form.reportIndex]?reportList[form.reportIndex].label:'请选择是否环评'}}
							</view>
						</picker>
					</div>
				</div>
				<div class="whxx_all" v-if="form.entTypeCode=='et02'">
					<div class="whxx_left">
						<span>*</span>是否存在违建用房:
					</div>
					<div class="whxx_right">
						<radio-group @change="illegalBuildingChange">
							<label class="radio">
								<label class="radio" style="font-size: 14px;float: left;"><radio :value="'1'" :checked="illegalBuildingFlag=='1'" style="float: left;transform:scale(0.7);margin-top: -2px;" />是</label>
								<label class="radio" style="font-size: 14px;float: left;"><radio :value="'0'" :checked="illegalBuildingFlag=='0'" style="float: left;transform:scale(0.7);margin-top: -2px;" />否</label>
							</label>
						</radio-group>
					</div>
				</div>
				<div class="whxx_all" v-if="form.entTypeCode=='et02'">
					<div class="whxx_left">
						<span>*</span>粪污处理措施:
					</div>
					<div class="whxx_right">
						<picker @change="dungFacilitiesPickerChange" :range="dungFacilitiesList" :range-key="'label'">
							<view class="uni-input">
								{{dungFacilitiesList[form.dungFacilitiesIndex]?dungFacilitiesList[form.dungFacilitiesIndex].label:'请选择粪污处理措施'}}
							</view>
						</picker>
					</div>
				</div>
				<div class="whxx_all" v-if="form.entTypeCode=='et02'">
					<div class="whxx_left">
						<span>*</span>粪污处理方法:
					</div>
					<div class="whxx_right">
						<view @click="showDungDeal">{{form.dungDealNames?form.dungDealNames:'请选择粪污处理方法'}}</view>
						<evan-checkbox-popup  ref="dungDealRef" :options="dungDealList" :optionValue="'id'" :value="form.dungDealSelectList" :title="' '" :primaryColor="'#09BB07'" @confirm="onDungDealConfirm" ></evan-checkbox-popup>
					</div>
				</div>
				<div class="whxx_all">
					<div class="whxx_left">
						<span>*</span>生产经营状态:
					</div>
					<div class="whxx_right">
						<radio-group @change="produceChange">
							<label class="radio">
								<label class="radio" style="font-size: 14px;float: left;"><radio :value="'1'" :checked="produceFlag=='1'" style="float: left;transform:scale(0.7);margin-top: -2px;" />生产</label>
								<label class="radio" style="font-size: 14px;float: left;"><radio :value="'0'" :checked="produceFlag=='0'" style="float: left;transform:scale(0.7);margin-top: -2px;" />停产</label>
							</label>
						</radio-group>
					</div>
				</div>
				<div class="whxx_all" v-if="form.produceFlag=='0'">
					<div class="whxx_left">
						<span>*</span>停产时间:
					</div>
					<div class="whxx_right">
						<biaofun-datetime-picker placeholder="请选择停产时间" fields="day" :end="endDateFun" :defaultValue="form.stopDate!=''?form.stopDate:endDateFun" @change="changeDatetimePicker"></biaofun-datetime-picker>
					</div>
				</div>
			</div>
			<div style="margin-bottom: 20px;">
				<button form-type="submit" type="primary" style="width:100px;height:35px;line-height:35px;font-size:14px;color: #fff;background-color: #02994f;">下一步</button>
			</div>
		</form>
	</view>
</template>

<script>
	var config = require('../../common/config.js');
	import dictApi from "../../api/dict";
	import areaApi from '../../api/area.js';
	import basicEntApi from '../../api/basicEnt.js';
	import BiaofunDatetimePicker from "@/components/biaofun-datetime-picker/biaofun-datetime-picker";
	import basicFarmSelect from "@/components/basic-farm-select/basic-farm-select"
	
	var graceChecker = require("../../common/graceui-dataChecker/graceChecker.js");
	export default {
		components: {
			BiaofunDatetimePicker,
			basicFarmSelect,
		},
		data() {
			return {
				imgPath: config.IMG_PATH,
				form: {
					id: "",
					tableName:"",//业务表名
					entTypeCode: "", //企业类型code
					entTypeName: "", //企业类型名称
					entName: "", //企业名称
					province: "", //省编码,
					city: "", //市编码
					county: "", //区编码
					street: "", //街道行政区划
					areaName: "", //区域名称 空格分隔
					idCard: "", //身份证
					legalPerson: "", //负责人
					telephone: "", //电话
					lng: "", //经度
					lat: "", //纬度
					address: "", //地址信息
					livestockCode: "", //养殖代码
					cardNo: "", //统一社会信用代码
					preventionNo: "", //防疫条件合格号
					permitNo: "", //经营许可证号
					pollutionNo: "", //请填写排污许可证号
					dungFacilities: "", //粪污处理措施code
					dungFacilitiesName: "", //粪污处理措施name
					reportFlag: "", //环评报告或登记表 1环评报告书 2环评登记表 0无
					illegalBuildingFlag: "", //是否存在违建 1是 0否
					dungDealIds: "", //粪污处理方法ids 逗号分隔
					dungDealNames: "", //粪污处理方法names 逗号分隔
					produceFlag: "", //1生产 0停产
					stopDate: "", //停产时间
					enterName: "", //录入人
					enterTel: "", //录入人电话
					farmList:[],
					farmValue:"",
					areaAddress:"",
					streetIndex: "",
					dungFacilitiesIndex: "",
					dungDealIndex: "",
					reportIndex: "",
					dungDealSelectList: [], //粪污处理方法选中
				},
				illegalBuildingFlag:"0",//是否存在违建 1是 0否
				produceFlag: "1", //1生产 0停产
				livestockCodeFlag: false,
				cardNoFlag: false,
				preventionNoFlag: false,
				permitNoFlag: false,
				pollutionNoFlag: false,
				entTypeList: [], //企业类型字典项
				farmLivestockList: [], //养殖畜种字典项
				dungFacilitiesList: [], //粪污处理措施字典项
				dungDealList: [], //粪污处理方法字典项
				
				streetList: [], //街道集合
				reportList: [{
						label: "环评报告书",
						value: "1"
					},
					{
						label: "环评登记表",
						value: "2"
					},
					{
						label: "无",
						value: "0"
					}
				],
			}
		},
		onLoad(option) {
			//const param = JSON.parse(decodeURIComponent(option.param));
			//console.log(param)
			let basicEntTemp=uni.getStorageSync('basicEntTemp');
			if(basicEntTemp){
				this.form=basicEntTemp;
				if(!this.form.livestockCode){
					this.livestockCodeFlag=true;
				}
				if(!this.form.cardNo){
					this.cardNoFlag=true;
				}
				if(!this.form.preventionNo){
					this.preventionNoFlag=true;
				}
				if(!this.form.permitNo){
					this.permitNoFlag=true;
				}
				if(!this.form.pollutionNo){
					this.pollutionNoFlag=true;
				}
				this.produceFlag=this.form.produceFlag;
				this.illegalBuildingFlag=this.form.illegalBuildingFlag;
				
			}else{
				let ent={};
				if(option.tableName && option.tableName=="bas_ent_change"){
					ent=uni.getStorageSync('entChange');
				}else{
					ent=uni.getStorageSync('ent');
				}
				this.form.id=ent.id;
				this.form.tableName=option.tableName?option.tableName:"bas_ent";
				this.form.province=ent.province;
				this.form.city=ent.city;
				this.form.county=ent.county;
				this.form.entName=ent.name;
				this.form.telephone=ent.contactsPhone;
				this.form.areaAddress=ent.address;
				this.form.produceFlag=this.produceFlag;
				if(ent.entType=="0"){
					this.form.legalPerson=ent.contacts;
					this.form.cardNo=ent.socialCode;
					this.form.entTypeCode="et02";
					this.form.entTypeName="养殖场";
					this.form.illegalBuildingFlag=this.illegalBuildingFlag;
				}else if(ent.entType=="1"){
					this.form.legalPerson=ent.name;
					this.form.idCard=ent.cardNo;
					this.form.entTypeCode="et14";
					this.form.entTypeName="散养户";
					
				}
			}
			this.getEntTypeDict();
			this.getDungFacilitiesDict();
			this.getDungDealDict();
			this.getLivestockTypeDict();
			this.getStreet();
		},
		onShow() {
			
		},
		computed: {
			endDateFun() {
				var year = new Date().getFullYear(); //年
				if (year < 1900) year = year + 1900;
				var month = new Date().getMonth() + 1; //月
				if (month < 10) month = '0' + month;
				var day = new Date().getDate(); //日
				if (day < 10) day = '0' + day;
				var hour = new Date().getHours(); //小时
				if (hour < 10) hour = '0' + hour;
				var minute = new Date().getMinutes(); //分钟
				if (minute < 10) minute = '0' + minute;
				var second = new Date().getSeconds(); //秒
				if (second < 10) second = '0' + second;
				var str = year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
				return str;
			}
		},
		methods: {
			
			clickMap() {
				let that = this;
				if (!that.form.lat) {
					uni.chooseLocation({
						success: function(res) {
							that.form.address = res.address;
							that.form.lat = res.latitude;
							that.form.lng = res.longitude;
						}
					});
				} else {
					uni.chooseLocation({
						latitude: that.form.lat,
						longitude: that.form.lng,
						success: function(res) {
							that.form.address = res.address;
							that.form.lat = res.latitude;
							that.form.lng = res.longitude;
						}
					});
				}

			},
			getEntTypeDict() {
				dictApi.findBaiscDict({
					type: 'ent_type'
				}).then(res => {
					if (res.code === 1) {
						uni.showToast({
							icon: 'none',
							title: res.message,
							mask: true,
							duration: 1500
						});
						return;
					}
					this.entTypeList = res.data;
					//默认值
					//this.form.entTypeCode = this.entTypeList[0].value;
					//this.form.entTypeName = this.entTypeList[0].label;
				})
			},
			getDungFacilitiesDict() {
				dictApi.findBaiscDict({
					type: 'dung_facilities'
				}).then(res => {
					if (res.code === 1) {
						uni.showToast({
							icon: 'none',
							title: res.message,
							mask: true,
							duration: 1500
						});
						return;
					}
					this.dungFacilitiesList = res.data;
				})
			},
			getDungDealDict() {
				dictApi.findBaiscDict({
					type: 'pollution'
				}).then(res => {
					if (res.code === 1) {
						uni.showToast({
							icon: 'none',
							title: res.message,
							mask: true,
							duration: 1500
						});
						return;
					}
					this.dungDealList = res.data;
				})
			},
			getLivestockTypeDict() {
				let that = this;
				dictApi.findBaiscDict({
					type: 'livestock_type',
					parentId: '0',
				}).then(res => {
					if (res.code === 1) {
						uni.showToast({
							icon: 'none',
							title: res.message,
							mask: true,
							duration: 1500
						});
						return;
					}
					that.farmLivestockList = [];
					res.data.forEach((item, index) => {
						let obj={};
						obj["livestockTypeCode"]=item.value;
						obj["livestockTypeName"]=item.label;
						obj["label"]=item.label;
						obj["parentId"]="0";
						let childList=[];
						item.childList.forEach((item2, index2) => {
							let child={};
							child["parentLivestockTypeCode"]=item.value;
							child["parentLivestockTypeName"]=item.label;
							child["livestockTypeCode"]=item2.value;
							child["livestockTypeName"]=item2.label;
							child["label"]=item2.label;
							child["livestockScale"]=null;
							child["outScale"]=null;
							child["lastLivestockScale"]=null;
							child["lastFemaleLivestock"]=null;
							child["lastOutScale"]=null;
							child["currentLivestockScale"]=null;
							child["currentFemaleLivestock"]=null;
							childList.push(child);
						})
						obj["childList"]=childList;
						that.farmLivestockList.push(obj);
					})
				})
			},
			getStreet() {
				let that = this;
				basicEntApi.findBasicStreetList(that.form.county).then(res => {
					if (res.code === 1) {
						uni.showToast({
							icon: 'none',
							title: res.message,
							mask: true,
							duration: 1500
						});
						return;
					}
					this.streetList = res.data;
				})
			},
			entTypeChange(e) {
				var entTypeIndex = e.target.value;
				var selected = this.entTypeList[entTypeIndex]; //获取选中的数组
				this.form.entTypeCode = selected.value; //选中的id
				this.form.entTypeName = selected.label; //选中的名称
				if(this.form.entTypeCode=="et14"){
					this.form.livestockCode="";
					this.livestockCodeFlag=false;
					
					this.form.cardNo="";
					this.cardNoFlag=false;
					
					this.form.preventionNo="";
					this.preventionNoFlag=false;
					
					this.form.permitNo="";
					this.permitNoFlag=false;
					
					this.form.pollutionNo="";
					this.pollutionNoFlag=false;
					
					this.form.reportIndex="";
					this.form.reportFlag="";
					
					this.form.illegalBuildingFlag="";
					this.illegalBuildingFlag="0";
					
					this.form.dungFacilitiesIndex="";
					this.form.dungFacilities="";
					this.form.dungFacilitiesName="";
					
					this.form.dungDealNames="";
					this.form.dungDealIds="";
					this.form.dungDealSelectList="";
					
					this.produceFlag="1"
					this.form.produceFlag="";
					this.form.stopDate="";
				}else{
					this.form.illegalBuildingFlag="0";
					this.illegalBuildingFlag="0";
					
					this.produceFlag="1"
					this.form.produceFlag="1";
				}
			},
			illegalBuildingChange(e) {
				this.form.illegalBuildingFlag = e.target.value;
			},
			produceChange(e) {
				this.form.produceFlag = e.target.value;
				if(this.form.produceFlag=="0"){
					this.form.stopDate=new Date().toISOString().slice(0, 10);
				}else{
					this.form.stopDate="";
				}
			},
			streetPickerChange(e) {
				this.form.streetIndex = e.target.value;
				var selected = this.streetList[this.form.streetIndex]; //获取选中的数组
				this.form.street = selected.code; //选中的id
				this.form.areaName = selected.remarks; //选中的名称
			},
			dungFacilitiesPickerChange(e) {
				this.form.dungFacilitiesIndex = e.target.value;
				var selected = this.dungFacilitiesList[this.form.dungFacilitiesIndex]; //获取选中的数组
				this.form.dungFacilities = selected.value; //选中的id
				this.form.dungFacilitiesName = selected.label; //选中的名称
			},
			reportPickerChange(e) {
				this.form.reportIndex = e.target.value;
				var selected = this.reportList[this.form.reportIndex]; //获取选中的数组
				this.form.reportFlag = selected.value; //选中的id
			},
			onDungDealConfirm(e){
				this.form.dungDealSelectList=e;
				const objArr = this.dungDealList.filter((op) => this.form.dungDealSelectList && this.form.dungDealSelectList.includes(op['id']))
				let idArray=[];
				let nameArray=[];
				objArr.forEach((item, index) => {
					idArray.push(item.id);
					nameArray.push(item.label);
				})
				this.form.dungDealIds = idArray.join(','); 
				this.form.dungDealNames = nameArray.join(','); 
			},
			changeDatetimePicker(res) {
				this.form.stopDate = res.f1;
			},
			showDungDeal(){
				this.$refs.dungDealRef.openPopup()
			},
			showfarmLivestock(){
				this.$refs.basicFarmSelectRef.popShow()
			},
			onBasicFarmSelectConfirm(e){
				this.form.farmList=e;
				let farmChildList=[];
				e.forEach((item, index) => {
					item.childList.forEach((item2, index2) => {
						farmChildList.push(item2.label);
					})
				})
				this.form.farmValue=farmChildList.join(',');
				
			},
			formSubmit(e) {
				uni.showLoading({
				    title: '校验中',
					mask:true,
				});
				let that = this;
				var rule = [
				    { name: "enterName", checkType: "string", checkRule: "1,20", errorMsg: "请填写录入员姓名" },
					{ name: "enterTel", checkType: "notnull", checkRule: "", errorMsg: "请填写录入员手机号" },
					{ name: "enterTel", checkType: "phoneno", checkRule: "", errorMsg: "录入员手机号填写错误" },
					{ name: "street", checkType: "notnull", checkRule: "", errorMsg: "请选择所在街道、社区、村镇" },
					{ name: "address", checkType: "string", checkRule: "1,50", errorMsg: "请填写详细地址" },
					{ name: "farmValue", checkType: "notnull", checkRule: "", errorMsg: "请选择养殖品种" },
					{ name: "idCard", checkType: "notnull", checkRule: "", errorMsg: "请填写身份证号" },
					{ name: "idCard", checkType: "idCard", checkRule: "", errorMsg: "请正确填写身份证号" },
					
				];
				
				var checkRes = graceChecker.check(that.form, rule);
				if(!checkRes){
					uni.hideLoading();
					uni.showToast({ title: graceChecker.error, icon: "none" });
					return;
				}
				
				if(that.form.entTypeCode=="et02"){
					
					if(that.cardNoFlag){
						that.form.cardNo=null;
					}else{
						if(that.form.cardNo==null || that.form.cardNo=="" || that.form.cardNo.length!=18){
							uni.hideLoading();
							uni.showToast({ title: "统一社会信用代码", icon: "none" });
							return;
						}
					}
					if(!that.livestockCodeFlag ){
						if(that.form.livestockCode==null || that.form.livestockCode=="" || that.form.livestockCode.length!=15){
							uni.hideLoading();
							uni.showToast({ title: "请填写15位的畜禽养殖代码", icon: "none" });
							return;
						}
					}else{
						that.form.livestockCode=null;
					}
					if(!that.preventionNoFlag ){
						if(that.form.preventionNo==null || that.form.preventionNo=="" || that.form.preventionNo.length!=15){
							uni.hideLoading();
							uni.showToast({ title: "请填写15位的防疫条件合格证号", icon: "none" });
							return;
						}
					}else{
						that.form.preventionNo=null;
					}
					
					if(!that.permitNoFlag){
						if(that.form.permitNo==null || that.form.permitNo=="" || that.form.permitNo.length!=25){
							uni.hideLoading();
							uni.showToast({ title: "请填写25位种畜经营许可证号", icon: "none" });
							return;
						}
						let reg =  /^[0-9a-zA-Z]*$/g;
						if (!reg.test(that.form.permitNo) ){
							uni.hideLoading();
							 uni.showToast({ title: "种畜经营许可证号只允许填写数字字母", icon: "none" });
							 return;
						 }
					}else{
						that.form.permitNo=null;
					}
					
					if(!that.pollutionNoFlag){
						if(that.form.pollutionNo==null || that.form.pollutionNo=="" || that.form.pollutionNo.length!=22){
							uni.hideLoading();
							uni.showToast({ title: "请填写22位的排污许可证号", icon: "none" });
							return;
						}
					}else{
						that.form.pollutionNo=null;
					}
					
					if(that.form.reportFlag==""){
						uni.hideLoading();
						uni.showToast({ title: "请选择是否环评", icon: "none" });
						return;
					}
					if(that.form.dungFacilities==""){
						uni.hideLoading();
						uni.showToast({ title: "请选择粪污处理措施", icon: "none" });
						return;
					}
					if(that.form.dungDealIds==""){
						uni.hideLoading();
						uni.showToast({ title: "请选择粪污处理方法", icon: "none" });
						return;
					}
				}
				uni.hideLoading();
				uni.setStorageSync("basicEntTemp",that.form);
				uni.navigateTo({
					url: '/pages/basic/basicFarmLivestockForm'
				});
			}
		},
	}
</script>

<style lang="scss" scoped>
	.title_all {
		padding-left: 5%;
	}
	.input_left {
		width: 70%;
		float: left;
	}
	.input_right {
		width: 30%;
		float: left;
	}
</style>
