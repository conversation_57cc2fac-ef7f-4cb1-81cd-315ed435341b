<template>
	<view>
		<form @submit="formSubmit">
			<view class="title_all">
				<image :src="imgPath+'img/index/img9.png'" alt="" />
				<p>行业信息</p>
			</view>
			<view v-for="(item,index) in form.farmList" :key="index">
				<view v-for="(item2,index2) in item.childList" :key="index2">
						<!-- 猪、牛、羊、鹿 -->
						<view v-if="item2.parentLivestockTypeCode=='101'||item2.parentLivestockTypeCode=='102'||item2.parentLivestockTypeCode=='103'||item2.parentLivestockTypeCode=='110'">
							<div class="content_all">
								<div class="whxx_all">
									<div class="whxx_left">
										养殖品种
									</div>
									<div class="whxx_right">
											{{item2.label}}
									</div>
								</div>
								<div class="whxx_all_hist" v-if="form.entTypeCode=='et02'">
									<div class="whxx_left_all">
										设计规模(头、羽、只)
									</div>
								</div>
								<div class="whxx_all" v-if="form.entTypeCode=='et02'">
									<div class="whxx_left">
										<span>*</span>存栏:
									</div>
									<div class="whxx_right">
										<input class="uni-input" type='number' maxlength="10" v-model="item2.livestockScale"
										 placeholder="请填写设计存栏" ></input>
									</div>
								</div>
								<div class="whxx_all" v-if="form.entTypeCode=='et02'">
									<div class="whxx_left">
										<span>*</span>年出栏:
									</div>
									<div class="whxx_right">
										<input class="uni-input" type='number' maxlength="10" v-model="item2.outScale" placeholder="请填写设计年出栏"></input>
									</div>
								</div>
								<div class="whxx_all_hist">
									<div class="whxx_left_all">
										上一年规模(头、羽、只)
									</div>
								</div>
								<div class="whxx_all" v-if="form.entTypeCode=='et02'">
									<div class="whxx_left">
										<span>*</span>总存栏:
									</div>
									<div class="whxx_right">
										<input class="uni-input" maxlength="10" type="number"  v-model="item2.lastLivestockScale"
										 placeholder="请填写上一年总存栏"></input>
									</div>
								</div>
								<div class="whxx_all" v-if="form.entTypeCode=='et02'">
									<div class="whxx_left">
										<span>*</span>{{resultLastFemaleLivestockLable(item2.label)}}:
									</div>
									<div class="whxx_right">
										<input  class="uni-input" maxlength="10" type="number"  v-model="item2.lastFemaleLivestock"
										 :placeholder="resultLastFemaleLivestockPlaceholder(item2.label)"></input>
									</div>
								</div>
								<div class="whxx_all">
									<div class="whxx_left">
										<span>*</span>出栏量:
									</div>
									<div class="whxx_right">
										<input v-if="form.entTypeCode=='et02'" class="uni-input" maxlength="10" type="number"  v-model="item2.lastOutScale"
										 placeholder="请填写上一年出栏量"></input>
										<input v-if="form.entTypeCode=='et14'" class="uni-input" maxlength="10" type="number" v-model="item2.outScale"
										 placeholder="请填写上一年出栏量"></input>
									</div>
								</div>
								<div class="whxx_all_hist">
									<div class="whxx_left_all">
										当前存栏情况(头、羽、只)
									</div>
								</div>
								<div class="whxx_all">
									<div class="whxx_left">
										<span>*</span>总存栏:
									</div>
									<div class="whxx_right">
										<input v-if="form.entTypeCode=='et02'" class="uni-input" maxlength="10" type="number"  v-model="item2.currentLivestockScale"
										 placeholder="请填写当前总存栏"></input>
										<input v-if="form.entTypeCode=='et14'" class="uni-input" maxlength="10" type="number"  v-model="item2.livestockScale"
										 placeholder="请填写当前总存栏"></input>
									</div>
								</div>
								<div class="whxx_all">
									<div class="whxx_left">
										<span>*</span>{{resultCurrentFemaleLivestockLable(item2.label)}}:
									</div>
									<div class="whxx_right">
										<input v-if="form.entTypeCode=='et02'" class="uni-input" maxlength="10" type="number"  v-model="item2.currentFemaleLivestock"
										 :placeholder="resultCurrentFemaleLivestockPlaceholder(item2.label)"></input>
										<input v-if="form.entTypeCode=='et14'" class="uni-input" maxlength="10" type="number"  v-model="item2.femaleLivestock"
										 :placeholder="resultCurrentFemaleLivestockPlaceholder(item2.label)"></input>
									</div>
								</div>
							</div>
						</view>
						<!-- 鸡、鸭、鹅、其他 -->
						<view v-if="item2.parentLivestockTypeCode=='201'||item2.parentLivestockTypeCode=='202'||item2.parentLivestockTypeCode=='203'||item2.parentLivestockTypeCode=='999'">
							<div class="content_all">
								<div class="whxx_all">
									<div class="whxx_left">
										养殖品种:
									</div>
									<div class="whxx_right">
										{{item2.label}}
									</div>
								</div>
								<div class="whxx_all_hist" v-if="form.entTypeCode=='et02'">
									<div class="whxx_left_all">
										设计规模(头、羽、只)
									</div>
								</div>
								<div class="whxx_all" v-if="form.entTypeCode=='et02'">
									<div class="whxx_left">
										<span>*</span>存栏:
									</div>
									<div class="whxx_right">
										<input class="uni-input" type='number' maxlength="10" v-model="item2.livestockScale"
										 placeholder="请填写设计存栏" ></input>
									</div>
								</div>
								<div class="whxx_all" v-if="form.entTypeCode=='et02'">
									<div class="whxx_left">
										<span>*</span>年出栏:
									</div>
									<div class="whxx_right">
										<input class="uni-input" type='number' maxlength="10" v-model="item2.outScale" placeholder="请填写设计年出栏"></input>
									</div>
								</div>
								<div class="whxx_all_hist">
									<div class="whxx_left_all">
										上一年规模(头、羽、只)
									</div>
								</div>
								<div class="whxx_all" v-if="form.entTypeCode=='et02'">
									<div class="whxx_left">
										<span>*</span>总存栏:
									</div>
									<div class="whxx_right">
										<input class="uni-input" maxlength="10" type="number" v-model="item2.lastLivestockScale"
										 placeholder="请填写上一年总存栏"></input>
									</div>
								</div>
								<div class="whxx_all">
									<div class="whxx_left">
										<span>*</span>出栏量:
									</div>
									<div class="whxx_right">
										<input v-if="form.entTypeCode=='et02'" class="uni-input" maxlength="10" type="number"  v-model="item2.lastOutScale"
										 placeholder="请填写上一年出栏量"></input>
										<input v-if="form.entTypeCode=='et14'" class="uni-input" maxlength="10" type="number" v-model="item2.outScale"
										 placeholder="请填写上一年出栏量"></input>
									</div>
								</div>
								<div class="whxx_all_hist">
									<div class="whxx_left_all">
										当前存栏情况(头、羽、只)
									</div>
								</div>
								<div class="whxx_all">
									<div class="whxx_left">
										<span>*</span>总存栏:
									</div>
									<div class="whxx_right">
										<input v-if="form.entTypeCode=='et02'" class="uni-input" maxlength="10" type="number"  v-model="item2.currentLivestockScale"
										 placeholder="请填写当前总存栏"></input>
										<input v-if="form.entTypeCode=='et14'" class="uni-input" maxlength="10" type="number"  v-model="item2.livestockScale"
										 placeholder="请填写当前总存栏"></input>
									</div>
								</div>
							</div>
						</view>
					
				</view>
			</view>
			<div style="margin-bottom: 20px;">
				<button form-type="submit" type="primary" style="width:100px;height:35px;line-height:35px;font-size:14px;color: #fff;background-color: #02994f;">保存</button>
			</div>
		</form>
	</view>
</template>

<script>
	var config = require('../../common/config.js');
	import api from '../../api/basicEnt.js';
	export default {
		components: {
			a:{
				
			}
		},
		data() {
			return {
				imgPath: config.IMG_PATH,
				form: {},
			}
		},
		onLoad(option) {
			this.form = uni.getStorageSync("basicEntTemp");
			console.log(this.form);
		},
		onShow() {

		},
		computed:{
		},
		methods: {
			formSubmit(e) {
				uni.showLoading({
				    title: '校验中',
					mask:true,
				});
				let that = this;
				let validateSuccessFlag=true;
				try{
					that.form.farmList.forEach((item, index) => {
						item.childList.forEach((item2, index2) => {
							//畜种：猪、牛、羊、鹿 
							if(item2.parentLivestockTypeCode=='101'||item2.parentLivestockTypeCode=='102'||item2.parentLivestockTypeCode=='103'||item2.parentLivestockTypeCode=='110'){
								//养殖场
								if(that.form.entTypeCode=='et02'){
									//表单校验:设计存栏、设计年出栏、上一年总存栏、上一年能繁母禽存栏
									if(!that.isNumber(item2.livestockScale) || !that.isNumber(item2.outScale)|| !that.isNumber(item2.lastLivestockScale)|| !that.isNumber(item2.lastFemaleLivestock)){
										throw new Error("break");//报错，就跳出循环
									}
									//表单校验:上一年出栏量、当前总存栏、当前能繁母禽存栏
									if(!that.isNumber(item2.lastOutScale) || !that.isNumber(item2.currentLivestockScale)|| !that.isNumber(item2.currentFemaleLivestock)){
										throw new Error("break");//报错，就跳出循环
									}
								}
								//散养户
								if(that.form.entTypeCode=='et14'){
									//表单校验:上一年出栏量、当前总存栏、当前能繁母禽存栏
									if(!that.isNumber(item2.outScale) || !that.isNumber(item2.livestockScale)|| !that.isNumber(item2.femaleLivestock)){
										throw new Error("break");//报错，就跳出循环
									}
								}
								
							}//鸡、鸭、鹅、其他
							else if(item2.parentLivestockTypeCode=='201'||item2.parentLivestockTypeCode=='202'||item2.parentLivestockTypeCode=='203'||item2.parentLivestockTypeCode=='999'){
								//养殖场
								if(that.form.entTypeCode=='et02'){
									//表单校验:设计存栏、设计年出栏、上一年总存栏
									if(!that.isNumber(item2.livestockScale) || !that.isNumber(item2.outScale)|| !that.isNumber(item2.lastLivestockScale)){
										throw new Error("break");//报错，就跳出循环
									}
									//表单校验:上一年出栏量、当前总存栏
									if(!that.isNumber(item2.lastOutScale) || !that.isNumber(item2.currentLivestockScale)){
										throw new Error("break");//报错，就跳出循环
									}
								}
								//散养户
								if(that.form.entTypeCode=='et14'){
									//表单校验:上一年出栏量、当前总存栏
									if(!that.isNumber(item2.outScale) || !that.isNumber(item2.livestockScale)){
										throw new Error("break");//报错，就跳出循环
									}
								}
								
							}
						})
					})
				}catch(e){
					console.log(e.message);
					validateSuccessFlag=false;
				}
				
				if(!validateSuccessFlag){
					uni.hideLoading();
					uni.showToast({
						icon: 'none',
						title: "请检查表单必填项",
						mask: true,
						duration: 1500
					});
					return;
				}
				that.form['farmJson']=JSON.stringify(that.form.farmList);
				api.save(that.form).then(res => {
					if (res.code === 1) {
						uni.hideLoading();
						uni.showToast({
							icon: 'none',
							title: res.message,
							mask: true,
							duration: 1500
						});
						return;
					}
					uni.hideLoading();
					uni.removeStorageSync("basicEntTemp");
					uni.showToast({
						//icon:'none',
						title: '信息提交成功',
						duration: 2000,
						success() {
							setTimeout(() => {
								uni.reLaunch({
									url: "/pages/index/index",
								})
							}, 2000);
						}
					});
				})
			},
			isNumber(checkVal){
				if(checkVal == null || checkVal.length < 1){return false;}
				checkVal = Number(checkVal);
				if(checkVal == NaN){return false;}
				return true;
			},
			resultLastFemaleLivestockLable(e){
				return "其中能繁母"+e+"存栏";
			},
			resultLastFemaleLivestockPlaceholder(e){
				return "请填写上一年能繁母"+e+"存栏";
			},
			resultCurrentFemaleLivestockLable(e){
				return "其中能繁母"+e+"存栏";
			},
			resultCurrentFemaleLivestockPlaceholder(e){
				return "请填写当前能繁母"+e+"存栏";
			}
			
		},
	}
</script>

<style lang="scss" scoped>
.title_all {
		padding-left: 5%;
}
.whxx_left_all {
	width: 100%;
	float: left;
	padding: 10px 0;
	font-size: 14px;
	color: #333333;
	font-weight: bold;
	}

</style>
