<template>
	<view class="page-container">
		<div class="cont_all">
			<div class="cont">
				<div class="content_all_new">
					<!-- 报告编号 -->
					<div class="whxx_all">
						<div class="whxx_left">
							报告编号:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.reportNumber || '-'}}</view>
						</div>
					</div>

					<!-- 样品编号 -->
					<div class="whxx_all">
						<div class="whxx_left">
							样品编号:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.sampleNumber || '-'}}</view>
						</div>
					</div>

					<!-- 受检样品 -->
					<div class="whxx_all">
						<div class="whxx_left">
							样品名称:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.sampleName || '-'}}</view>
						</div>
					</div>

					<!-- 检测结果 -->
					<div class="whxx_all">
						<div class="whxx_left">
							检测结果:
						</div>
						<div class="whxx_right">
							<view class="detail-text" :style="{'color': form.resultCode === '0' ? '#02994f' : '#ff0000'}">
								{{form.resultName || '-'}}
							</view>
						</div>
					</div>

					<!-- 样品地块 -->
					<div class="whxx_all">
						<div class="whxx_left">
							样品地块:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.samplePlot || '-'}}</view>
						</div>
					</div>

					<!-- 样品批次 -->
					<div class="whxx_all">
						<div class="whxx_left">
							样品批次:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.sampleBatch || '-'}}</view>
						</div>
					</div>

					<!-- 抽样单号 -->
					<div class="whxx_all">
						<div class="whxx_left">
							抽样单号:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.samplingNumber || '-'}}</view>
						</div>
					</div>

					<!-- 样品到样日期 -->
					<div class="whxx_all">
						<div class="whxx_left">
							样品到样日期:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.sampleDate || '-'}}</view>
						</div>
					</div>

					<!-- 受检单位 -->
					<div class="whxx_all">
						<div class="whxx_left">
							单位名称:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.entName || '-'}}</view>
						</div>
					</div>

					<!-- 联系人 -->
					<div class="whxx_all">
						<div class="whxx_left">
							联系人:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.contacts || '-'}}</view>
						</div>
					</div>

					<!-- 联系电话 -->
					<div class="whxx_all">
						<div class="whxx_left">
							联系电话:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.mobile || '-'}}</view>
						</div>
					</div>

					<!-- 受检单位性质 -->
					<div class="whxx_all">
						<div class="whxx_left">
							受检单位性质:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{entTypeDisplayText}}</view>
						</div>
					</div>

          <!-- 受检单位详细地址 -->
          <div class="whxx_all">
            <div class="whxx_left">
              单位地址:
            </div>
            <div class="whxx_right">
              <view class="detail-text">{{form.entDetail || '-'}}</view>
            </div>
          </div>

					<!-- 受检单位地址 -->
					<div class="whxx_all">
						<div class="whxx_left">
							行政区划:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.entAddress || '-'}}</view>
						</div>
					</div>



					<!-- 是否是安全县 -->
					<div class="whxx_all">
						<div class="whxx_left">
							是否是安全县:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{isSecureCountyDisplayText}}</view>
						</div>
					</div>

					<!-- 生长年限 -->
					<div class="whxx_all">
						<div class="whxx_left">
							生长年限:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.growthYears || '-'}}</view>
						</div>
					</div>

					<!-- 种植面积 -->
					<div class="whxx_all">
						<div class="whxx_left">
							种植面积（亩）:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.plantingArea || '-'}}</view>
						</div>
					</div>

					<!-- 人参产量 -->
					<div class="whxx_all">
						<div class="whxx_left">
							人参产量（斤）:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.ginsengProduction || '-'}}</view>
						</div>
					</div>

					<!-- 是否过药品安全间隔期 -->
					<div class="whxx_all">
						<div class="whxx_left">
							是否过药品安全间隔期:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{isExpireDisplayText}}</view>
						</div>
					</div>

					<!-- 抽样日期 -->
					<div class="whxx_all">
						<div class="whxx_left">
							抽样日期:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.samplingDate || '-'}}</view>
						</div>
					</div>

					<!-- 抽样人 -->
					<div class="whxx_all">
						<div class="whxx_left">
							抽样人:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.samplingPerson || '-'}}</view>
						</div>
					</div>

					<!-- 备注 -->
					<div class="whxx_all">
						<div class="whxx_left">
							备注:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.remarks || '-'}}</view>
						</div>
					</div>

					<!-- 委托单位 -->
					<div class="whxx_all">
						<div class="whxx_left">
							委托单位:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.testEntName || '-'}}</view>
						</div>
					</div>

					<!-- 样品地块 -->
					<div class="whxx_all">
						<div class="whxx_left">
							样品地块:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.samplePlot || '-'}}</view>
						</div>
					</div>
          

					<!-- 上传时间 -->
					<div class="whxx_all">
						<div class="whxx_left">
							上传时间:
						</div>
						<div class="whxx_right">
							<view class="detail-text">{{form.createDate || '-'}}</view>
						</div>
					</div>

					<!-- 报告文件 -->
					<div class="whxx_all_hist" style="border-bottom: none;margin-top:10px;padding-bottom: 5px;">
						<span class="title_tit" style="padding: 0;">报告文件:</span>
					</div>
					<div class="whxx_all_hist" style="margin:0 auto;padding-top: 0;">
						<div class="file-list-container">
							<div class="file-list" v-if="form.fileList && form.fileList.length > 0">
								<view class="file-item" v-for="(file, index) in form.fileList" :key="index" @click="previewFile(file)">
									<view class="file-icon">
										<uni-icons type="paperplane" size="20" color="#02994f"></uni-icons>
									</view>
									<view class="file-name">{{getDisplayFileName(file)}}</view>
									<view class="file-action">
										<uni-icons type="eye" size="16" color="#666"></uni-icons>
									</view>
								</view>
							</div>
							<view v-else class="no-files">
								<text>暂无报告文件</text>
							</view>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 底部操作按钮 -->
		<view class="action-btn-container">
			<button class="edit-btn" @click="editReport">编辑</button>
			<button class="delete-btn" @click="deleteReport">删除</button>
		</view>
	</view>
</template>

<script>
	import api from '@/api/inspectionReport.js';
	import dictApi from '@/api/dict.js';
	import {
		formatDate
	} from '@/common/formatDate.js';
	
	export default {
		data: function() {
			return {
				form: {
					id: "",
					testEntId: '', // 委托单位ID
					testEntName: '', // 委托单位名称
					entId: "",
					entName: "",
					entProvince: "", // 省份
					entCity: "", // 城市
					entCounty: "", // 区县
					entAddress: "", // 地址
					entDetail: "", // 详细地址
					reportNumber: "", // 报告编号
					resultCode: "", // 检测结果代码
					resultName: "", // 检测结果名称
					sampleCode: "", // 受检样品代码
					sampleName: "", // 受检样品名称
					sampleNumber: "", // 样品编号
					samplePlot: "", // 样品地块
					sampleBatch: "", // 样品批次
					samplingNumber: "", // 抽样单号
					sampleDate: "", // 样品到样日期
					contacts: "", // 联系人
					mobile: "", // 联系电话
					entType: "", // 受检单位性质代码
					entTypeName: "", // 受检单位性质名称
					isSecureCounty: "", // 是否安全县代码
					isSecureCountyName: "", // 是否安全县名称
					growthYears: "", // 生长年限
					plantingArea: "", // 种植面积
					ginsengProduction: "", // 人参产量
					isExpire: "", // 是否过药品安全间隔期代码
					isExpireName: "", // 是否过药品安全间隔期名称
					samplingDate: "", // 抽样日期
					samplingPerson: "", // 抽样人
					remarks: "", // 备注
					createDate: "", // 创建时间
					fileList: [], // 上传的文件列表
				},
				loading: false,
				entTypeList: [], // 受检单位性质字典列表
				isSecureCountyList: [], // 是否安全县字典列表
				isExpireList: [] // 是否过药品安全间隔期字典列表
			}
		},
		onLoad: function(option) {
			if (option.id) {
				this.form.id = option.id;
				// 先获取字典数据，再获取报告详情
				this.getDictData().then(() => {
					this.getReportDetail();
				});
			} else {
				uni.showToast({
					title: '参数错误',
					icon: 'none',
					duration: 1500
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		},
		computed: {
			// 受检单位性质显示文本
			entTypeDisplayText() {
				if (this.form.entType && this.entTypeList.length > 0) {
					let entType = this.entTypeList.find(item => item.value === this.form.entType);
					return entType ? entType.label : '-';
				}
				return '-';
			},
			// 是否安全县显示文本
			isSecureCountyDisplayText() {
				if (this.form.isSecureCounty && this.isSecureCountyList.length > 0) {
					let item = this.isSecureCountyList.find(item => item.value === this.form.isSecureCounty);
					return item ? item.label : '-';
				}
				return '-';
			},
			// 是否过药品安全间隔期显示文本
			isExpireDisplayText() {
				if (this.form.isExpire && this.isExpireList.length > 0) {
					let item = this.isExpireList.find(item => item.value === this.form.isExpire);
					return item ? item.label : '-';
				}
				return '-';
			}
		},
		methods: {
			// 获取字典数据
			async getDictData() {
				try {
					await Promise.all([
						this.getEntTypeDict(),
						this.getIsSecureCountyDict(),
						this.getIsExpireDict()
					]);
				} catch (error) {
					console.error('获取字典数据失败:', error);
				}
			},

			// 获取受检单位性质字典
			getEntTypeDict() {
				return new Promise((resolve, reject) => {
					dictApi.findDict({
						type: 'entType'
					}).then(res => {
						if (res.code === 1) {
							console.error('获取受检单位性质字典失败:', res.message);
							resolve();
							return;
						}
						this.entTypeList = res.data || [];
						resolve();
					}).catch(err => {
						console.error('获取受检单位性质字典失败:', err);
						reject(err);
					});
				});
			},

			// 获取是否安全县字典
			getIsSecureCountyDict() {
				return new Promise((resolve) => {
					// 创建固定的是否选项
					this.isSecureCountyList = [
						{value: '1', label: '是'},
						{value: '0', label: '否'}
					];
					resolve();
				});
			},

			// 获取是否过药品安全间隔期字典
			getIsExpireDict() {
				return new Promise((resolve) => {
					// 创建固定的是否选项
					this.isExpireList = [
						{value: '1', label: '是'},
						{value: '0', label: '否'}
					];
					resolve();
				});
			},

			// 获取报告详情
			getReportDetail() {
				let that = this;
				that.loading = true;
				uni.showLoading({
					title: '加载中...'
				});

				api.getReport(that.form.id).then(res => {
					uni.hideLoading();
					that.loading = false;
					
					if (res.code === 1) {
						uni.showToast({
							icon: 'none',
							title: res.message || '获取详情失败',
							duration: 1500
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
						return;
					}
					
					that.form = res.data;
					// 格式化日期字段为 yyyy-MM-dd 格式
					if (that.form.createDate) {
						that.form.createDate = that.formatDate(that.form.createDate);
					}
					if (that.form.sampleDate) {
						that.form.sampleDate = that.formatDate1(that.form.sampleDate);
					}
					if (that.form.samplingDate) {
						that.form.samplingDate = that.formatDate1(that.form.samplingDate);
					}
				}).catch(error => {
					uni.hideLoading();
					that.loading = false;
					console.error('获取报告详情失败:', error);
					uni.showToast({
						icon: 'none',
						title: '网络请求失败',
						duration: 1500
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				});
			},
			
			// 编辑报告
			editReport() {
				uni.navigateTo({
					url: `/inspectionReport/index/inspectionReportEnterForm?id=${this.form.id}`
				});
			},
			
			// 删除报告
			deleteReport() {
				let that = this;
				uni.showModal({
					title: '确认删除',
					content: '确定要删除这个检测报告吗？',
					success: function(res) {
						if (res.confirm) {
							that.confirmDelete();
						}
					}
				});
			},
			
			// 确认删除
			confirmDelete() {
				let that = this;
				uni.showLoading({
					title: '删除中...'
				});
				
				api.deleteReport(that.form.id).then(res => {
					uni.hideLoading();
					
					if (res.code === 1) {
						uni.showToast({
							icon: 'none',
							title: res.message || '删除失败',
							duration: 1500
						});
						return;
					}
					
					uni.showToast({
						title: '删除成功',
						duration: 1500,
						success() {
							setTimeout(() => {
								uni.navigateBack();
							}, 1500);
						}
					});
				}).catch(error => {
					uni.hideLoading();
					console.error('删除报告失败:', error);
					uni.showToast({
						icon: 'none',
						title: '删除失败',
						duration: 1500
					});
				});
			},
			
			// 预览文件
			previewFile(file) {
				if (!file.filePath) {
					uni.showToast({
						title: '文件路径不存在',
						icon: 'none'
					});
					return;
				}
				
				// 判断文件类型
				const fileExtension = this.getFileExtension(file.fileUrl);
				if (fileExtension === 'pdf') {
					// PDF文件预览
					uni.downloadFile({
						url: file.fileUrl,
						success: function(res) {
							if (res.statusCode === 200) {
								uni.openDocument({
									filePath: res.tempFilePath,
									success: function(res) {
										console.log('打开文档成功');
									},
									fail: function(err) {
										console.error('打开文档失败:', err);
										uni.showToast({
											title: '无法打开文件',
											icon: 'none'
										});
									}
								});
							}
						},
						fail: function(err) {
							console.error('下载文件失败:', err);
							uni.showToast({
								title: '下载文件失败',
								icon: 'none'
							});
						}
					});
				} else {
					// 图片预览
					uni.previewImage({
						urls: [file.fileUrl],
						current: file.fileUrl
					});
				}
			},
			
			// 获取显示的文件名（优先显示原始文件名）
			getDisplayFileName(file) {
				// 如果有原始文件名，优先使用原始文件名
				if (file.fileName) {
					return file.fileName;
				}
				// 否则从文件路径中提取文件名
				return this.getFileName(file.filePath);
			},

			// 获取文件名
			getFileName(filePath) {
				if (!filePath) return '未知文件';
				const parts = filePath.split('/');
				return parts[parts.length - 1] || '未知文件';
			},
			
			// 获取文件扩展名
			getFileExtension(filePath) {
				if (!filePath) return '';
				const parts = filePath.split('.');
				return parts[parts.length - 1].toLowerCase();
			},
			
			// 格式化日期
			formatDate(time) {
				if (!time) return '';
				let date = new Date(time);
				return formatDate(date, 'yyyy-MM-dd hh:mm:ss');
			},
      formatDate1(time) {
        if (!time) return '';
        let date = new Date(time);
        return formatDate(date, 'yyyy-MM-dd');
      }
		}
	}
</script>

<style lang="scss" scoped>
	.page-container {
		min-height: 100vh;
		padding-bottom: 90px; /* 为底部按钮预留空间 */
		box-sizing: border-box;
	}

	.cont_all {
		width: 100%;
		background: #02994F;
	}

	.content_all_new {
		width: 90%;
		margin: 0px auto 10px;
		box-shadow: 0;
		background: #fff;
		border-radius: 8px;
		padding: 30px 0;
	}

	.cont {
		width: 100%;
		background: #fff;
	}

	.whxx_all {
		display: flex;
		align-items: center;
		padding: 15px 20px;
		border-bottom: 1px solid #f0f0f0;

		&:last-child {
			border-bottom: none;
		}
	}

	.whxx_left {
		width: 100px;
		font-size: 14px;
		color: #333;
		font-weight: 500;
		flex-shrink: 0;
	}

	.whxx_right {
		flex: 1;
		margin-left: 10px;
	}

	.detail-text {
		font-size: 14px;
		color: #666;
		line-height: 1.4;
		word-break: break-all;
	}

	.whxx_all_hist {
		padding: 15px 20px;

		.title_tit {
			font-size: 14px;
			color: #333;
			font-weight: 500;
		}
	}

	.file-list-container {
		max-height: 400px; /* 设置最大高度 */
		overflow-y: auto; /* 允许垂直滚动 */
		padding: 0 20px;
	}

	.file-list {
		margin-top: 0;
	}

	.file-item {
		display: flex;
		align-items: center;
		padding: 12px 20px;
		background: #f8f9fa;
		border-radius: 6px;
		margin-bottom: 8px;

		&:last-child {
			margin-bottom: 0;
		}

		&:active {
			background: #e9ecef;
		}
	}

	.file-icon {
		margin-right: 10px;
	}

	.file-name {
		flex: 1;
		font-size: 14px;
		color: #333;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.file-action {
		margin-left: 10px;
	}

	.no-files {
		text-align: center;
		padding: 30px 20px;
		color: #999;
		font-size: 14px;
	}

	.action-btn-container {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: #fff;
		padding: 15px 20px;
		border-top: 1px solid #e5e5e5;
		z-index: 999;
		display: flex;
		gap: 15px;
	}

	.edit-btn, .delete-btn {
		flex: 1;
		height: 45px;
		border: none;
		border-radius: 5px;
		font-size: 16px;
		font-weight: bold;
	}

	.edit-btn {
		background-color: #02994f;
		color: #fff;

		&:active {
			background-color: #027a3f;
		}
	}

	.delete-btn {
		background-color: #ff4757;
		color: #fff;

		&:active {
			background-color: #ff3742;
		}
	}
</style>
