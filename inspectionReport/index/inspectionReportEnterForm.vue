<template>
	<view>
		<form @submit="formSubmit">
			<div class="cont_all">
				<div class="cont">
					<div class="content_all_new">

						<!-- 报告编号 -->
						<div class="whxx_all">
							<div class="whxx_left">
								<span>*</span>报告编号:
							</div>
							<div class="whxx_right">
								<input class="uni-input" maxlength="50" v-model="form.reportNumber" type="text" placeholder="请输入报告编号">
							</div>
						</div>

            <!-- 样品编号 -->
            <div class="whxx_all">
              <div class="whxx_left">
                <span>*</span>样品编号:
              </div>
              <div class="whxx_right">
                <input class="uni-input" maxlength="50" v-model="form.sampleNumber" type="text" placeholder="请输入样品编号">
              </div>
            </div>
            <!-- 受检样品 -->
            <div class="whxx_all">
              <div class="whxx_left">
                <span>*</span>样品名称:
              </div>
              <div class="whxx_right">
                <picker @change="changePickerSample" :value="sampleIndex" :range="sampleList" :range-key="'label'">
                  <view class="uni-input">
                    {{sampleLabel}}
                  </view>
                </picker>
              </div>
            </div>

            <!-- 检测结果 -->
            <div class="whxx_all">
              <div class="whxx_left">
                <span>*</span>检测结果:
              </div>
              <div class="whxx_right">
                <picker @change="changePickerResult" :value="resultIndex" :range="resultList" :range-key="'label'">
                  <view class="uni-input">
                    {{inspectionResultLabel}}
                  </view>
                </picker>
              </div>
            </div>

            <!-- 样品地块 -->
            <div class="whxx_all">
              <div class="whxx_left">
                <span>*</span>样品地块:
              </div>
              <div class="whxx_right">
                <input class="uni-input" maxlength="100" v-model="form.samplePlot" type="text" placeholder="请输入样品地块">
              </div>
            </div>

            <!-- 样品批次 -->
            <div class="whxx_all">
              <div class="whxx_left">
                <span>*</span>样品批次:
              </div>
              <div class="whxx_right">
                <input class="uni-input" maxlength="50" v-model="form.sampleBatch" type="text" placeholder="请输入样品批次">
              </div>
            </div>

            <!-- 抽样单号 -->
            <div class="whxx_all">
              <div class="whxx_left">
                <span>*</span>抽样单号:
              </div>
              <div class="whxx_right">
                <input class="uni-input" maxlength="50" v-model="form.samplingNumber" type="text" placeholder="请输入抽样单号">
              </div>
            </div>

            <!-- 样品到样日期 -->
            <div class="whxx_all">
              <div class="whxx_left">
                <span>*</span>样品到样日期:
              </div>
              <div class="whxx_right">
                <BiaofunDatetimePicker :defaultValue="form.sampleDate" :type="'date'" :fields="'day'" placeholder="请选择样品到样日期" @change="onSampleDateChange">
                  <view class="uni-input">
                    {{form.sampleDate || '请选择样品到样日期'}}
                  </view>
                </BiaofunDatetimePicker>
              </div>
            </div>

						<!-- 受检单位 -->
						<div class="whxx_all">
							<div class="whxx_left">
								<span>*</span>单位名称:
							</div>
							<div class="whxx_right">
								<div class="ent-input-container">
									<!-- 跳转搜索模式 -->
									<view v-if="entInputMode === 'search'" class="uni-input clickable-input" @click="goToEntSearch">
										{{entLabel}}
									</view>
									<!-- 手动输入模式 -->
									<input v-else class="uni-input" v-model="form.entName" type="text" placeholder="请输入单位名称" />
									<!-- 模式切换按钮 -->
									<view class="mode-switch-btn" @click="toggleEntInputMode">
										<text class="mode-icon">{{ entInputMode === 'search' ? '✏️' : '🔍' }}</text>
									</view>
								</div>
							</div>
						</div>

            <!-- 联系人 -->
            <div class="whxx_all">
              <div class="whxx_left">
                <span>*</span>联系人:
              </div>
              <div class="whxx_right">
                <input class="uni-input" :class="{ 'disabled-input': isFieldDisabled }" maxlength="50" v-model="form.contacts" type="text" placeholder="请输入联系人" :disabled="isFieldDisabled">
              </div>
            </div>

            <!-- 联系电话 -->
            <div class="whxx_all">
              <div class="whxx_left">
                <span>*</span>联系电话:
              </div>
              <div class="whxx_right">
                <input class="uni-input" :class="{ 'disabled-input': isFieldDisabled }" maxlength="20" v-model="form.mobile" type="text" placeholder="请输入手机号或座机号" :disabled="isFieldDisabled">
              </div>
            </div>

						<!-- 受检单位性质 -->
						<div class="whxx_all">
							<div class="whxx_left">
								<span>*</span>受检单位性质:
							</div>
							<div class="whxx_right">
								<picker @change="changePickerEntType" :value="entTypeIndex" :range="entTypeList" :range-key="'label'" :disabled="isFieldDisabled">
									<view class="uni-input" :class="{ 'disabled-input': isFieldDisabled }">
										{{entTypeLabel}}
									</view>
								</picker>
							</div>
						</div>

						<!-- 受检单位详细地址 -->
						<div class="whxx_all">
							<div class="whxx_left">
								<span>*</span>单位地址:
							</div>
							<div class="whxx_right">
								<input class="uni-input" :class="{ 'disabled-input': isFieldDisabled }" maxlength="200" v-model="form.entDetail" type="text" placeholder="请输入受检单位详细地址" :disabled="isFieldDisabled">
							</div>
						</div>

            <!-- 行政区划 -->
            <div class="whxx_all">
              <div class="whxx_left">
                <span>*</span>行政区划:
              </div>
              <div class="whxx_right">
                <!-- 手动输入模式：显示可编辑的级联选择器 -->
                <pick-regions
                  v-if="!isFieldDisabled"
                  :defaultRegion="defaultRegionValue"
                  @getRegion="onRegionChange">
                  <view class="uni-input">
                    {{form.entAddress || '请选择省市区'}}
                  </view>
                </pick-regions>
                <!-- 搜索模式：显示不可编辑的文本 -->
                <view v-else class="uni-input disabled-input">
                  {{form.entAddress || '请选择省市区'}}
                </view>
              </div>
            </div>

            <!-- 是否是安全县 -->
            <div class="whxx_all">
              <div class="whxx_left">
<!--                <span>*</span>-->是否是安全县:
              </div>
              <div class="whxx_right">
                <picker @change="changePickerIsSecureCounty" :value="isSecureCountyIndex" :range="isSecureCountyList" :range-key="'label'">
                  <view class="uni-input">
                    {{isSecureCountyLabel}}
                  </view>
                </picker>
              </div>
            </div>

            <!-- 生长年限 -->
            <div class="whxx_all">
              <div class="whxx_left">
                <span>*</span>生长年限:
              </div>
              <div class="whxx_right">
                <input class="uni-input" maxlength="10" v-model="form.growthYears" type="number" placeholder="请输入生长年限">
              </div>
            </div>
            <!-- 种植面积 -->
            <div class="whxx_all">
              <div class="whxx_left">
                <span>*</span>种植面积（亩）:
              </div>
              <div class="whxx_right">
                <input class="uni-input" maxlength="20" v-model="form.plantingArea" type="number" placeholder="请输入种植面积">
              </div>
            </div>
            <!-- 人参产量 -->
            <div class="whxx_all">
              <div class="whxx_left">
                <span>*</span>人参产量（斤）:
              </div>
              <div class="whxx_right">
                <input class="uni-input" maxlength="20" v-model="form.ginsengProduction" type="number" placeholder="请输入人参产量">
              </div>
            </div>

            <!-- 是否过药品安全间隔期 -->
            <div class="whxx_all">
              <div class="whxx_left">
<!--                <span>*</span>-->是否过药品安全间隔期:
              </div>
              <div class="whxx_right">
                <picker @change="changePickerIsExpire" :value="isExpireIndex" :range="isExpireList" :range-key="'label'">
                  <view class="uni-input">
                    {{isExpireLabel}}
                  </view>
                </picker>
              </div>
            </div>

            <!-- 抽样日期 -->
            <div class="whxx_all">
              <div class="whxx_left">
                <span>*</span>抽样日期:
              </div>
              <div class="whxx_right">
                <BiaofunDatetimePicker :defaultValue="form.samplingDate" :type="'date'" :fields="'day'" placeholder="请选择抽样日期" @change="onSamplingDateChange">
                  <view class="uni-input">
                    {{form.samplingDate || '请选择抽样日期'}}
                  </view>
                </BiaofunDatetimePicker>
              </div>
            </div>

						<!-- 抽样人 -->
						<div class="whxx_all">
							<div class="whxx_left">
								<span>*</span>抽样人:
							</div>
							<div class="whxx_right">
								<input class="uni-input" maxlength="50" v-model="form.samplingPerson" type="text" placeholder="请输入抽样人">
							</div>
						</div>

						<!-- 备注 -->
						<div class="whxx_all">
							<div class="whxx_left">
								备注:
							</div>
							<div class="whxx_right">
								<textarea class="uni-input" maxlength="500" v-model="form.remarks" placeholder="请输入备注" style="height: 80px;"></textarea>
							</div>
						</div>

						<!-- 上传报告 -->
						<div class="whxx_all_hist" style="border-bottom: none;margin-top:10px;">
							<span class="title_tit" style="padding: 0;"><span style="color: red;">*</span>上传报告(最多上传10个，每个最多5M):</span>
						</div>
						<div class="whxx_all_hist" style="margin:10px auto;">
							<div class="pboto_right" style="margin-bottom:10px;width: 100%;">
								<view style="width:100%;">
									<view class="row-file">
										<ck-upload-file v-if="afterGetData" @removeFile="onRemoveFile" @returnFileUrl="onReturnFileUrl" :initFileList="fileList" :selectNum=10 :token="upToken" :tableName="tableName"></ck-upload-file>
										<ck-upload-file v-else @removeFile="onRemoveFile" @returnFileUrl="onReturnFileUrl" :selectNum=10 :token="upToken" :tableName="tableName"></ck-upload-file>
									</view>
								</view>
							</div>
						</div>
					</div>
				</div>
			</div>
			<button form-type="submit" type="primary" style="width:100px;height:35px;line-height:35px;font-size:14px;color: #fff;background-color: #02994f;">确认提交</button>
      <div style="height: 50px;width: 20px;"></div>
		</form>
	</view>
</template>

<script>
	import api from '@/api/inspectionReport.js';
	import productApi from '@/api/product.js';
	import commonApi from '@/api/common.js';
	import entApi from '@/api/ent.js';
	import dictApi from '@/api/dict.js';
	import graceChecker from '@/common/graceui-dataChecker/graceChecker.js';
	import BiaofunDatetimePicker from "@/components/biaofun-datetime-picker/biaofun-datetime-picker";
	import ckUploadFile from '@/components/ck-uploadImg/ck-upload-file.vue';
	import pickRegions from '@/components/dynamic-regions/dynamic-regions.vue';
	export default {
		components: {BiaofunDatetimePicker,ckUploadFile,pickRegions},
		data: function() {
			return {
				editModel:false,//编辑模式
				form:{
          testEntId:'',//委托单位ID
          testEntName:'',//委托单位名称
					id:"",
					uniqueCode:"", // 唯一编码
					reportNumber:"", // 报告编号
					entId: "",
					entName: "",
					entProvince: "", // 省份
					entCity: "", // 城市
					entCounty: "", // 区县
					entAddress: "", // 地址
					entDetail: "", // 详细地址
					entType: "", // 受检单位性质
          resultCode:"", // 检测结果编码
          resultName:"", // 检测结果名称
					sampleCode:"", // 受检样品编码
					sampleName:"", // 受检样品名称
					samplePlot:"", // 样品地块
					sampleBatch:"", // 样品批次
					sampleNumber:"", // 样品编号
					samplingNumber:"", // 抽样单号
					samplingDate:"", // 抽样日期
					samplingPerson:"", // 抽样人
					sampleDate:"", // 样品到样日期
					contacts:"", // 联系人
					mobile:"", // 联系电话
					isSecureCounty:"", // 是否是安全县
					growthYears:"", // 生长年限
					plantingArea:"", // 种植面积（亩）
					isExpire:"", // 是否过药品安全间隔期
					ginsengProduction:"", // 人参产量（斤）
					remarks:"", // 备注
					fileList:[], // 上传的文件列表
				},
				fileList:[], // 文件列表用于回显
				resultList:[], // 检测结果字典列表
				resultIndex:'',
				sampleList:[], // 受检样品字典列表
				sampleIndex:'',
				entInputMode: 'search', // 受检单位输入模式：'search'(跳转搜索) 或 'manual'(手动输入)
				entTypeList:[], // 受检单位性质字典列表
				entTypeIndex:'',
				isSecureCountyList:[], // 是否安全县字典列表
				isSecureCountyIndex:'',
				isExpireList:[], // 是否过药品安全间隔期字典列表
				isExpireIndex:'',
				upToken:'',//七牛token
				tableName:"bas_inspection_report",
				afterGetData:false,
				
			}
		},
		onLoad: function(option) {
      /**
       * 判断逻辑
       * 1、判断当前ent缓存，如果不存在，提示需要先维护主体信息
       * 2、判断当前主体信息是否需要补入基础信息采集数据
       * 3、判断主体审核状态，审核中，跳转至主体查看页面
       * 4、判断主体审核状态，如果examineStatus == '90'，那么说明此主体是变更暂存，跳转至变更页面
       * 5、以上逻辑都通过后显示当前页面功能
       */
      let that = this;
      let ent = uni.getStorageSync("ent");
      if (!ent) {
        uni.showModal({
          title: '先维护主体信息',
          content: '确定前往维护主体信息?',
          mask: true,
          success: function(res) {
            if (res.confirm) {
              that.$Router.replaceAll("/pages/ent/chooseType")
            } else if (res.cancel) {
              that.$Router.replaceAll("/pages/index/index")
            }
          }
        });
      } else if (ent.basicFlag == '1' && ent.basicEnterFlag == '0') {
        uni.showModal({
          icon: "none",
          content: '未在基础数据采集系统内查询到您的信息,请前往填写详细信息',
          mask: true,
          showCancel: false,
          success: function(res) {
            that.$Router.replaceAll("/pages/basic/basicEntForm")
          }
        });
      } else if (ent.examineStatus == '0' || ent.examineStatus == '-1') {
        uni.showModal({
          icon: "none",
          content: '主体信息审核中，前往主体信息查看审核状态？',
          mask: true,
          success: function(res) {
            if (res.confirm) {
              that.$Router.replaceAll("/pages/ent/chooseType")
            } else if (res.cancel) {
              that.$Router.replaceAll("/pages/index/index")
            }
          }
        });
      } else if (ent.examineStatus == '90') { //变更暂存状态：跳转至变更页面
        uni.redirectTo({
          url: "/pages/entChange/viewCheck",
        })
        return;
      } else if (ent.frozenFlag == '1') {
        uni.showModal({
          icon: "none",
          content: '您的账号已冻结，请联系技术服务人员取消冻结',
          mask: true,
          showCancel: false,
          success: function(res) {
            that.$Router.replaceAll("/pages/index/index")
          }
        });
      } else {
        let that = this;

        // 从缓存获取ent信息并赋值给委托单位字段
        that.form.testEntId = ent.id;
        that.form.testEntName = ent.name;

        commonApi.get7nToken().then(res => {
          if(res.code == 0){
            that.upToken = res.data;
          }
        });


        // 获取检测结果字典
        that.getResultDict();

        // 获取受检样品字典
        that.getSampleDict();

        // 获取受检单位性质字典
        that.getEntTypeDict();

        // 获取是否安全县字典
        that.getIsSecureCountyDict();

        // 获取是否过药品安全间隔期字典
        that.getIsExpireDict();

        if(option.id){
          that.editModel=true;//编辑模式
          that.form.id=option.id;
          //表单赋值
          api.getReport(that.form.id).then(res => {
            if (res.code === 1) {
              uni.hideLoading();
              uni.showToast({
                icon: 'none',
                title: res.message,
                mask: true,
                duration: 1500
              });
              return;
            }
            that.form=res.data;
            if (that.form.entId) {
              this.entInputMode= 'search'
            } else {
              this.entInputMode= 'manual'
            }
            // 编辑模式下也要确保委托单位信息来自缓存
            that.form.testEntId = ent.id;
            that.form.testEntName = ent.name;
            if (that.form.fileList) {
              that.fileList = that.form.fileList
            } else {
              that.form.fileList = [];
            }
            that.afterGetData=true;
          })
        }

      }


		},

		onShow: function() {
			// 监听从企业搜索页面返回的企业信息
			let that = this;
			uni.$on('setEnt', function(ent) {
				if (ent) {
					that.form.entId = ent.id;
					that.form.entName = ent.name;
					that.form.entProvince = ent.province || '';
					that.form.entCity = ent.city || '';
					that.form.entCounty = ent.county || '';
					that.form.entAddress = ent.address || '';
					that.form.entDetail = ent.detail || (ent.province || '') + (ent.city || '') + (ent.county || '');
					// 自动回显其他相关字段
					that.form.contacts = ent.contacts || '';
					that.form.mobile = ent.contactsPhone || '';
					that.form.entType = ent.entType || '';
					// 如果有受检单位性质，设置对应的索引
					if (ent.entType && that.entTypeList.length > 0) {
						that.entTypeList.forEach((item, index) => {
							if (item.value === ent.entType) {
								that.entTypeIndex = index;
							}
						});
					}
				}
			});
		},

		computed: {
			// 字段禁用状态控制
			isFieldDisabled() {
				return this.entInputMode === 'search';
			},
			//检查结果标题
			inspectionResultLabel() {
			  let label="请选择检测结果";
			  if(this.form.resultName && this.resultList.length > 0){
				  let result=this.resultList.filter(item => item.value === this.form.resultCode)[0];
				  label=result ? result.label : "请选择检测结果";
			  }
			  return label;
			},
			//受检样品标题
			sampleLabel() {
			  let label="请选择受检样品";
			  if(this.form.sampleCode && this.sampleList.length > 0){
				  let sample=this.sampleList.filter(item => item.value === this.form.sampleCode)[0];
				  label=sample ? sample.label : "请选择受检样品";
			  }
			  return label;
			},
			//受检单位性质标题
			entTypeLabel() {
			  let label="请选择受检单位性质";
			  if(this.form.entType && this.entTypeList.length > 0){
				  let entType=this.entTypeList.filter(item => item.value === this.form.entType)[0];
				  label=entType ? entType.label : "请选择受检单位性质";
			  }
			  return label;
			},
			//是否安全县标题
			isSecureCountyLabel() {
			  let label="请选择是否是安全县";
			  if(this.form.isSecureCounty && this.isSecureCountyList.length > 0){
				  let item=this.isSecureCountyList.filter(item => item.value === this.form.isSecureCounty)[0];
				  label=item ? item.label : "请选择是否是安全县";
			  }
			  return label;
			},
			//是否过药品安全间隔期标题
			isExpireLabel() {
			  let label="请选择是否过药品安全间隔期";
			  if(this.form.isExpire && this.isExpireList.length > 0){
				  let item=this.isExpireList.filter(item => item.value === this.form.isExpire)[0];
				  label=item ? item.label : "请选择是否过药品安全间隔期";
			  }
			  return label;
			},
			//企业选择标题
			entLabel() {
			  return this.form.entName || "请点击搜索受检单位";
			},
			//行政区划默认值（用于级联选择器回显）
			defaultRegionValue() {
				// 如果没有名称但有编码
				if (this.form.entProvince && this.form.entCity && this.form.entCounty) {
					return [this.form.entProvince, this.form.entCity, this.form.entCounty];
				}
				return [];
			},
		},
		methods: {
			//跳转到企业搜索页面
			goToEntSearch() {
				uni.navigateTo({
					url: '/pages/inspection/entSearch'
				});
			},
			//切换受检单位输入模式
			toggleEntInputMode() {
				this.entInputMode = this.entInputMode === 'search' ? 'manual' : 'search';
				// 如果切换到手动输入模式，清空相关字段，允许用户手动输入
				if (this.entInputMode === 'manual') {
					this.form.entId = '';
					// 清空自动回显的字段，但保留用户可能已经输入的受检单位名称
					// this.form.entName = ''; // 保留受检单位名称
					this.form.contacts = '';
					this.form.mobile = '';
					this.form.entType = '';
					this.form.entAddress = '';
					this.form.entDetail = '';
					this.form.entProvince = '';
					this.form.entCity = '';
					this.form.entCounty = '';
					this.entTypeIndex = '';
				}
				// 如果切换到搜索模式，提示用户重新选择
				else {
					// 可以选择清空所有相关字段，强制用户重新搜索选择
					this.form.entId = '';
					this.form.entName = '';
					this.form.contacts = '';
					this.form.mobile = '';
					this.form.entType = '';
					this.form.entAddress = '';
					this.form.entDetail = '';
					this.form.entProvince = '';
					this.form.entCity = '';
					this.form.entCounty = '';
					this.entTypeIndex = '';
				}
			},
			//获取企业列表（保留方法以防其他地方调用）
			getEntList() {
				// 现在主要使用搜索功能选择企业，此方法保留以防其他地方调用
				console.log('企业选择已改为搜索模式');
			},
			//获取检测结果字典
			getResultDict() {
				let that = this;
				dictApi.findDict({
					type: 'resultCode'
				}).then(res => {
					if (res.code === 1) {
						uni.showToast({
							icon: 'none',
							title: res.message,
							mask: true,
							duration: 1500
						});
						return;
					}
					that.resultList = res.data || [];

					// 如果是编辑模式且已有inspectionResult，设置对应的索引
					if (that.editModel && that.form.inspectionResult) {
						that.resultList.forEach((item, index) => {
							if (item.value === that.form.inspectionResult) {
								that.resultIndex = index;
							}
						});
					}
				}).catch(err => {
					console.error('获取检测结果字典失败:', err);
					uni.showToast({
						icon: 'none',
						title: '获取检测结果字典失败',
						duration: 1500
					});
				});
			},
			//获取受检样品字典
			getSampleDict() {
				let that = this;
				dictApi.findDict({
					type: 'sampleCode'
				}).then(res => {
					if (res.code === 1) {
						uni.showToast({
							icon: 'none',
							title: res.message,
							mask: true,
							duration: 1500
						});
						return;
					}
					that.sampleList = res.data || [];
					// 如果是编辑模式且已有sampleCode，设置对应的索引
					if (that.editModel && that.form.sampleCode) {
						that.sampleList.forEach((item, index) => {
							if (item.value === that.form.sampleCode) {
								that.sampleIndex = index;
							}
						});
					}
				}).catch(err => {
					console.error('获取受检样品字典失败:', err);
					uni.showToast({
						icon: 'none',
						title: '获取受检样品字典失败',
						duration: 1500
					});
				});
			},
			//获取受检单位性质字典
			getEntTypeDict() {
				let that = this;
				dictApi.findDict({
					type: 'entType'
				}).then(res => {
					if (res.code === 1) {
						console.error('获取受检单位性质字典失败:', res.message);
						return;
					}
					that.entTypeList = res.data || [];
				}).catch(err => {
					console.error('获取受检单位性质字典失败:', err);
				});
			},
			//获取是否安全县字典
			getIsSecureCountyDict() {
				let that = this;
				// 创建固定的是否选项
				that.isSecureCountyList = [
					{value: '1', label: '是'},
					{value: '0', label: '否'}
				];
			},
			//获取是否过药品安全间隔期字典
			getIsExpireDict() {
				let that = this;
				// 创建固定的是否选项
				that.isExpireList = [
					{value: '1', label: '是'},
					{value: '0', label: '否'}
				];
			},

			//表单提交
			formSubmit: function(e) {
				let that = this;
				// 基础验证规则
				let rule = [
					{name: "reportNumber",checkType: "notnull",errorMsg: "请输入报告编号"},
					{name: "reportNumber",checkType: "reg",checkRule: "^[A-Z0-9]+$",errorMsg: "报告编号只能包含大写字母和数字"},
					{name: "sampleNumber",checkType: "reg",checkRule: "^[A-Z0-9]+$",errorMsg: "报告编号只能包含大写字母和数字"},
					{name: "sampleCode",checkType: "notnull",errorMsg: "请选择受检样品"},
					{name: "inspectionResult",checkType: "notnull",errorMsg: "请选择检测结果"},
					{name: "samplePlot",checkType: "notnull",errorMsg: "请输入样品地块"},
					{name: "sampleBatch",checkType: "notnull",errorMsg: "请输入样品批次"},
					{name: "samplingNumber",checkType: "notnull",errorMsg: "请输入抽样单号"},
					{name: "sampleDate",checkType: "notnull",errorMsg: "请选择样品到样日期"},
					// 根据输入模式动态添加单位名称验证规则
					...(this.entInputMode === 'search'
						? [{name: "entId", checkType: "notnull", errorMsg: "请选择单位名称"}]
						: [{name: "entName", checkType: "notnull", errorMsg: "请输入单位名称"}]),
					{name: "contacts",checkType: "notnull",errorMsg: "请输入联系人"},
					{name: "mobile",checkType: "notnull",errorMsg: "请输入联系电话"},
					{name: "mobile",checkType: "reg",checkRule: "^(1[3-9]\\d{9}|0\\d{2,3}-?\\d{7,8}|[48]00-?\\d{3}-?\\d{4})$",errorMsg: "请输入正确的手机号码或座机号码"},
					{name: "entType",checkType: "notnull",errorMsg: "请选择受检单位性质"},
					{name: "entAddress",checkType: "notnull",errorMsg: "请输入受检单位地址"},
					{name: "entDetail",checkType: "notnull",errorMsg: "请输入受检单位详细地址"},
					{name: "entProvince",checkType: "notnull",errorMsg: "请选择省份"},
					{name: "entCity",checkType: "notnull",errorMsg: "请选择城市"},
					{name: "entCounty",checkType: "notnull",errorMsg: "请选择区县"},
					/*{name: "isSecureCounty",checkType: "notnull",errorMsg: "请选择是否是安全县"},*/
					{name: "growthYears",checkType: "notnull",errorMsg: "请输入生长年限"},
					{name: "plantingArea",checkType: "notnull",errorMsg: "请输入种植面积"},
					{name: "ginsengProduction",checkType: "notnull",errorMsg: "请输入人参产量"},
					/*{name: "isExpire",checkType: "notnull",errorMsg: "请选择是否过药品安全间隔期"},*/
					{name: "samplingDate",checkType: "notnull",errorMsg: "请选择抽样日期"},
					{name: "samplingPerson",checkType: "notnull",errorMsg: "请输入抽样人"},
				];
				// 构建验证数据对象，根据输入模式包含相应字段
				let validateData = {
					reportNumber: this.form.reportNumber,
					sampleNumber: this.form.sampleNumber,
					sampleCode: this.form.sampleCode,
					inspectionResult: this.form.resultCode,
					samplePlot: this.form.samplePlot,
					sampleBatch: this.form.sampleBatch,
					samplingNumber: this.form.samplingNumber,
					sampleDate: this.form.sampleDate,
					// 根据输入模式包含相应的单位字段
					...(this.entInputMode === 'search'
						? {entId: this.form.entId}
						: {entName: this.form.entName}),
					contacts: this.form.contacts,
					mobile: this.form.mobile,
					entType: this.form.entType,
					entAddress: this.form.entAddress,
					entDetail: this.form.entDetail,
					entProvince: this.form.entProvince,
					entCity: this.form.entCity,
					entCounty: this.form.entCounty,
				/*	isSecureCounty: this.form.isSecureCounty,*/
					growthYears: this.form.growthYears,
					plantingArea: this.form.plantingArea,
					ginsengProduction: this.form.ginsengProduction,
					/*isExpire: this.form.isExpire,*/
					samplingDate: this.form.samplingDate,
					samplingPerson: this.form.samplingPerson,
				}

				let checkRes = graceChecker.check(validateData, rule);
				if (!checkRes) {
					uni.showToast({
						title: graceChecker.error,
						icon: "none",
						duration: 1500,
						success() {
							setTimeout(() => {
							}, 1500);
						}
					});
					return;
				}

				if(!this.form.fileList || this.form.fileList.length===0){
					uni.showToast({
						title: '请上传报告文件',
						icon: "none",
						duration: 1500,
						success() {
							setTimeout(() => {
							}, 1500);
						}
					});
					return;
				}
				uni.showLoading({
					title: '加载中',
					mask: true,
				});
				api.saveReport(that.form).then(res => {
					if (res.code === 1) {
						uni.hideLoading();
						uni.showToast({
							icon: 'none',
							title: res.message,
							mask: true,
							duration: 1500
						});
						return;
					}
					uni.hideLoading();
					uni.showToast({
						//icon:'none',
						title: '信息提交成功',
						duration: 2000,
						success() {
							setTimeout(() => {
								uni.navigateTo({
									url: '/inspectionReport/index/inspectionReportList'
								})
							}, 2000);
						}
					});
				})
			},
			//更换检查结果
			changePickerResult(e) {
			    this.resultIndex = e.target.value;
			    var selected = this.resultList[this.resultIndex];    //获取选中的数组
			    if (selected) {
			        this.form.resultCode = selected.value;
			        this.form.resultName = selected.label;
			    }
			},
			//更换受检样品
			changePickerSample(e) {
			    this.sampleIndex = e.target.value;
			    var selected = this.sampleList[this.sampleIndex];    //获取选中的数组
			    if (selected) {
			        this.form.sampleCode = selected.value;
			        this.form.sampleName = selected.label;
			    }
			},
			//更换受检单位性质
			changePickerEntType(e) {
			    this.entTypeIndex = e.target.value;
			    var selected = this.entTypeList[this.entTypeIndex];
			    if (selected) {
			        this.form.entType = selected.value;
			    }
			},
			//更换是否安全县
			changePickerIsSecureCounty(e) {
			    this.isSecureCountyIndex = e.target.value;
			    var selected = this.isSecureCountyList[this.isSecureCountyIndex];
			    if (selected) {
			        this.form.isSecureCounty = selected.value;
			    }
			},
			//更换是否过药品安全间隔期
			changePickerIsExpire(e) {
			    this.isExpireIndex = e.target.value;
			    var selected = this.isExpireList[this.isExpireIndex];
			    if (selected) {
			        this.form.isExpire = selected.value;
			    }
			},
			//样品到样日期变化处理
			onSampleDateChange(dateInfo) {
				console.log('样品到样日期变化:', dateInfo);
				if (dateInfo && dateInfo.f1) {
					this.form.sampleDate = dateInfo.f1;
					console.log('样品到样日期已更新:', this.form.sampleDate);
				}
			},
			//抽样日期变化处理
			onSamplingDateChange(dateInfo) {
				console.log('抽样日期变化:', dateInfo);
				if (dateInfo && dateInfo.f1) {
					this.form.samplingDate = dateInfo.f1;
					console.log('抽样日期已更新:', this.form.samplingDate);
				}
			},
			//删除文件
			onRemoveFile(index){
				this.form.fileList.splice(index,1);
			},
			//行政区划选择变化处理
			onRegionChange(regions) {
				console.log('行政区划选择变化:', regions);
				if (regions && regions.length === 3) {
					// 存储地区编码（用于数据提交）
					this.form.entProvince = regions[0].code;
					this.form.entCity = regions[1].code;
					this.form.entCounty = regions[2].code;

					// 设置完整地址（用于表单验证和数据一致性）
					this.form.entAddress = `${regions[0].name}${regions[1].name}${regions[2].name}`;

					console.log('省市区编码已更新:', this.form.entProvince, this.form.entCity, this.form.entCounty);
					console.log('完整地址已更新:', this.form.entAddress);
				}
			},
			//返回文件URL和文件信息
			onReturnFileUrl(urls, fileInfoArray){
        console.log("urls",urls);
        console.log("fileInfoArray",fileInfoArray);
				this.form.fileList = [];

				if (fileInfoArray && fileInfoArray.length > 0) {
					// 如果有文件信息数组，使用它来保存原始文件名
					fileInfoArray.forEach((fileInfo) => {
						let item = {};
						item["fileType"] = 'file';
						item["tableName"] = this.tableName;
						item["filePath"] = fileInfo.url;
						item["fileName"] = fileInfo.fileName; // 保存原始文件名
						this.form.fileList.push(item);
					});
				} else {
					// 兼容旧版本，如果没有文件信息数组，使用原来的方式
					urls.forEach((url) => {
						let item = {};
						item["fileType"] = 'file';
						item["tableName"] = this.tableName;
						item["filePath"] = url;
						this.form.fileList.push(item);
					});
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.header_all {
		width: 100%;
		height: 70px;
		background: #02994F;
		text-align: center;
		color: #fff;
	}
	.cont_all {
		width: 100%;
		background: #02994F;
	}
	.content_all_new  {
		    width: 90%;
		    margin: 0px auto 10px;
		    box-shadow: 0;
		    background: #fff;
		    overflow: auto;
	}
	.cont {
		width: 100%;

		background: #fff;
	}

	.clickable-input {
		cursor: pointer;
		position: relative;
	}

	.clickable-input::after {
		content: '';
		position: absolute;
		right: 10px;
		top: 50%;
		transform: translateY(-50%);
		width: 0;
		height: 0;
		border-left: 5px solid transparent;
		border-right: 5px solid transparent;
		border-top: 5px solid #999;
	}

	.ent-input-container {
		position: relative;
		width: 100%;
		display: flex;
		align-items: center;
	}

	.mode-switch-btn {
		position: absolute;
		right: 10px;
		top: 50%;
		transform: translateY(-50%);
		cursor: pointer;
		z-index: 2;
		padding: 5px;
		border-radius: 3px;
		background-color: rgba(0, 0, 0, 0.05);
		transition: background-color 0.2s;
	}

	.mode-switch-btn:hover {
		background-color: rgba(0, 0, 0, 0.1);
	}

	.mode-icon {
		font-size: 16px;
		display: block;
	}

	.disabled-input {
		background-color: #f5f5f5 !important;
		color: #999 !important;
		cursor: not-allowed !important;
	}

	.disabled-input::placeholder {
		color: #ccc !important;
	}
</style>
