<template>
	<view>
		<view style="width: 100%;overflow: auto;position: fixed;top: 0;background: #fff;">
			<view style="float:left;width:80%;">
				<uni-search-bar placeholder="输入报告编号" @confirm="onConfirm" @cancel="onCancel"></uni-search-bar>
			</view>
			<view style="float:left;width:20%;">
				<button type="primary" style="color: #fff; background-color: #02994f;width: 60px;float:right;height: 30px;line-height: 30px;font-size:14px;margin-top: 10px;margin-right: 10px;" @click="queryPopupShow()">筛选</button>
			</view>
		</view>
		<view class="content-all">
			<view class="news-item" v-for="(item,index) in list" :key="index" style="height: 80px">
				<view class="news-intr" @click="onView(item)" style="width: 90%;float:left;">
					<view class="flex-table" style="overflow: auto;">
						<p style="float:left;width:100%;font-weight: bold;">
							{{item.entName}}
						</p>
					</view>
					<view class="flex-table" style="overflow: auto;margin-top: 5px;">
						<p style="float:left;width:100%;font-size: 12px;color: #666;">
							报告编号：{{item.reportNumber}}
						</p>
					</view>
					<view class="flex-table" style="overflow: auto;margin-top: 5px;">
						<p style="float:left;width:100%;font-size: 12px;color: #666;">
							检测结果：<span :style="{'color': item.resultCode === '0' ? '#02994f' : '#ff0000'}">{{item.resultName}}</span>
						</p>
					</view>
					<view class="news-info" style="width: 100%;margin-top: 5px;">
						<p style="float:left;width:100%;font-size: 12px;color: #999;">
							上传时间：{{item.createDate}}
						</p>
					</view>
				</view>
				<uni-icons type="arrowright" size="17" style="float:left;color:grey;width:30px;height:30px;line-height:30px;text-align: right;" @click="onView(item)"></uni-icons>
			</view>
			<uni-load-more :status="status" :content-text="contentText" />
		</view>
		
		<!-- 底部上传报告按钮 -->
		<view class="upload-btn-container" v-show="!isFilterPopupVisible">
			<button class="upload-btn" @click="uploadReport">上传报告</button>
		</view>

		<uni-popup ref="queryPopup" type="share" class="query-popup" @change="onPopupChange">
			<inspection-report-query :resultList="resultList" @select="onSelect"></inspection-report-query>
		</uni-popup>
	</view>
</template>

<script>
	import api from '@/api/inspectionReport.js';
	import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';
	import inspectionReportQuery from '../components/inspectionReportQuery.vue'
	import {
		formatDate
	} from '@/common/formatDate.js';
  import dictApi from '@/api/dict.js';
	export default {
		components: {
			uniLoadMore,
			inspectionReportQuery,
		},
		data: function() {
			return {
				form: {
					testEntId: uni.getStorageSync("ent").id,
					reportNumber: "",
					resultCode: "",
					startDate: null,
					endDate: null,
				},
				resultList: [],
				list: [],
				total: 0, //总数
				refreshing: false, //为true表示正在刷新
				fetchPageNum: 1, //当前页数
				status: 'noMore',
				contentText: { // 加载提示
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				isFilterPopupVisible: false, // 筛选框显示状态
			}

		},
		onLoad: function(option) {
			let that = this;
			/* uni.setNavigationBarTitle({
				title: '检测报告管理'
			}); */
		},
		onShow: function() {
      this.getResultDict();
			this.onSearch()
		},
		onReachBottom() {
			if (this.list.length < this.total) {
				this.status = 'loading';
				this.getData();
			} else {
				this.status = 'noMore'; //没有数据时显示'没有更多'
			}

		},
		onPullDownRefresh() {
			console.log('下拉刷新');
			this.refreshing = true;
			this.list = [];
			this.fetchPageNum = 1;
			this.getData();
		},
		computed: {

		  },
		methods: {
      getResultDict() {
        let that = this;
        dictApi.findDict({
          type: 'resultCode'
        }).then(res => {
          if (res.code === 1) {
            uni.showToast({
              icon: 'none',
              title: res.message,
              mask: true,
              duration: 1500
            });
            return;
          }
          that.resultList = res.data || [];
        }).catch(err => {
          console.error('获取检测结果字典失败:', err);
          uni.showToast({
            icon: 'none',
            title: '获取检测结果字典失败',
            duration: 1500
          });
        });
      },
			onSearch(){
				this.list = [];
				this.fetchPageNum = 1;
				this.refreshing = true;
				this.getData();
			},
			getData: function() {
				let that = this;
				let pageNo = that.refreshing ? 1 : that.fetchPageNum;
				api.findList(pageNo, that.form).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: "请求数据失败请重试"
						})
						// 请求失败时也要停止下拉刷新
						if (that.refreshing) {
							that.refreshing = false;
							uni.stopPullDownRefresh();
						}
						return;
					}
					that.total = res.data.count;
					if (that.refreshing) {
						that.refreshing = false;
						uni.stopPullDownRefresh();
						that.list = res.data.list;
						that.fetchPageNum = res.data.next;
					} else {
						that.list = that.list.concat(res.data.list);
						that.fetchPageNum = res.data.next;
					}
				}).catch(error => {
					// 网络错误处理
					console.error('请求失败:', error);
					uni.showToast({
						title: "网络请求失败",
						icon: 'none'
					});
					if (that.refreshing) {
						that.refreshing = false;
						uni.stopPullDownRefresh();
					}
				})
			},
			onView: function(item) {
        		uni.navigateTo({
          			url: "/inspectionReport/index/inspectionReportDetail?id=" + item.id,
        		})
			},
			onConfirm(e){
				this.form.reportNumber=e.value;
				this.onSearch()
			},
			onCancel(e){
				this.form.reportNumber="";
				this.onSearch()
			},
			uploadReport() {
				uni.navigateTo({
					url: "/inspectionReport/index/inspectionReportEnterForm"
				})
			},
			queryPopupShow(){
				this.$refs.queryPopup.open();
			},
			onSelect(queryForm) {
				let that =this;
				that.form=queryForm;
				that.form.testEntId=uni.getStorageSync("ent").id;
				console.log('筛选条件:', that.form); // 调试日志
				that.onSearch();
				this.$refs.queryPopup.close()
			},
			onPopupChange(e) {
				// 监听筛选弹出框的显示/隐藏状态
				this.isFilterPopupVisible = e.show;
			},
		},
		filters: {
			formatDate(time) {
				let date = new Date(time)
				return formatDate(date, 'yyyy-MM-dd hh:mm') //年-月-日 时分
			}
		}
	}
</script>

<style lang="scss" scoped>

	.safe-body {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		width: 100%;
	}

	.index {
		flex: 1;
		width: 750rpx;
		flex-direction: column;
		/* 相当于上下居中 */
		justify-content: center;
		/* 相当于水平居中 */
		align-items: center;
	}

	.row {
		flex-direction: row;
	}

	.column {
		flex-direction: column;
	}

	.load-bottom {
		width: 100%;
		text-align: center;
	}

	.loadMore {
		font-size: 30upx;
		color: #555;
		margin-bottom: 20upx;
		line-height: 60rpx;
		margin-left: 170px;
	}

	.emptyData {
		margin-left: 175px;
	}

	.news-item {
		height: 65px;
		margin-left: 30rpx;
		margin-right: 30rpx;
		display: flex;
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		padding-top: 15px;
		padding-bottom: 15px;
		border-bottom: 2rpx #efefef solid;
	}

	.news-item .news-intr {
		flex: 2;
		padding-right: 20rpx;
		/* display: flex; */
		flex-direction: column;
		justify-content: space-between;
	}

	.news-intr .news-title {}

	.news-intr .news-info {
		display: flex;
		font-size: 12px;
		color: #999999;
	}

	.news-info .news-ago {
		text-align: left;
	}

	.news-info .news-type {
		text-align: right;
		flex: 2;
	}

	.news-item .news-image {
		flex: 1;
		max-width: 240rpx;
		max-height: 160rpx;
	}

	.news-item .news-image image {
		max-width: 100%;
		max-height: 100%;
	}

	.left {
		margin-left: 130rpx;
	}

	.triangle-up {
		margin-top: 3px;
		/*display:inline-block;*/
		width: 0;
		height: 0;
		border-left: 5px solid transparent;
		border-right: 5px solid transparent;
		border-bottom: 7px solid #fff;
		/* background: #fff; */
	}

	.triangle-down {
		margin-top: 3px;
		/*display:inline-block;*/
		width: 0;
		height: 0;
		border-left: 5px solid transparent;
		border-right: 5px solid transparent;
		border-top: 7px solid #9dd6b7;
		/* background: #9dd6b7; */
	}
	.content-all {
		width: 100%;
		margin-top: 50px;
		padding-bottom: 80px; /* 为底部按钮留出空间 */
	}

	.upload-btn-container {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: #fff;
		padding: 15px 20px;
		border-top: 1px solid #e5e5e5;
		z-index: 999;
	}

	.upload-btn {
		width: 100%;
		height: 45px;
		background-color: #02994f;
		color: #fff;
		border: none;
		border-radius: 5px;
		font-size: 16px;
		font-weight: bold;
	}

	.upload-btn:active {
		background-color: #027a3f;
	}

	/* 调整筛选弹出框位置，避免被底部按钮遮挡 */
/*	::v-deep .query-popup .uni-popup__wrapper {
		padding-bottom: 90px !important;
	}*/
</style>
