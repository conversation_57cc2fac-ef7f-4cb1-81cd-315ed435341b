<template>
  <view class="uni-popup-share">
    <view class="uni-share-content">
      <view class="uni-share-content-box">
        <view class="whxx_all">
          <view class="whxx_left">选择检测结果</view>
        </view>
        <view class="whxx_all_hist">
          <picker @change="changePickerResult" :range="resultList" :range-key="'label'">
            <view class="all">
              {{resultLabel}}
            </view>
          </picker>
        </view>
        <view class="whxx_all">
          <view class="whxx_left">选择上传时间范围</view>
        </view>
        <view class="whxx_all_hist">
          <biaofun-datetime-picker class="all" ref="startDateRef" placeholder="起始时间" fields="day" @change="changeStartDate"></biaofun-datetime-picker>
        </view>
        <view class="whxx_all_hist">
          <biaofun-datetime-picker class="all" ref="endDateRef" placeholder="结束时间" fields="day" @change="changeEndDate"></biaofun-datetime-picker>
        </view>
      </view>
    </view>
    <view class="uni-share-button-box">
      <button class="uni-share-button" type="default" size="default" style="color: #000;width: 100px;float:right;height: 40px;line-height: 40px;font-size:14px;" @click="close">重置</button>
      <button class="uni-share-button" type="primary" size="default" style="color: #fff;width: 100px;float:right;height: 40px;line-height: 40px;font-size:14px;background-color: #02994f;" @click="select">筛选</button>
    </view>
  </view>
</template>

<script>
import BiaofunDatetimePicker from "@/components/biaofun-datetime-picker/biaofun-datetime-picker";
export default {
  inject: ['popup'],
  components: {
    BiaofunDatetimePicker
  },
  props: {
    resultList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        testEntId: uni.getStorageSync("ent").id,
        reportNumber: "",
        resultCode: "",
        beginUploadDate: null,
        endUploadDate: null,
      },
      resultLabel: "全部",
    }
  },
  methods: {
    changePickerResult(e) {
      console.log("e",e);
      this.form.resultCode = this.resultList[e.detail.value].value;
      this.resultLabel = this.resultList[e.detail.value].label;
    },
    /* 时间选择 */
    changeStartDate(res) {
      this.form.beginUploadDate = res.f1;
      if(this.form.beginUploadDate && this.form.endUploadDate){
        let stDate = new Date(this.form.beginUploadDate).getTime();
        let enDate = new Date(this.form.endUploadDate).getTime();
        if(stDate>enDate){
          this.form.beginUploadDate=''
          this.$refs.startDateRef.dateStr=''
          uni.showToast({
            icon: 'none',
            title: '起始时间不能大于结束时间'
          });
        }
      }
    },
    changeEndDate(res) {
      this.form.endUploadDate = res.f1;
      if(this.form.beginUploadDate && this.form.endUploadDate){
        let stDate = new Date(this.form.beginUploadDate).getTime();
        let enDate = new Date(this.form.endUploadDate).getTime();
        if(stDate>enDate){
          this.form.endUploadDate = ''
          this.$refs.endDateRef.dateStr=''
          uni.showToast({
            icon: 'none',
            title: '结束时间不能小于起始时间'
          });
        }
      }
    },
    close() {
      this.form = {
        testEntId: uni.getStorageSync("ent").id,
        reportNumber: "",
        resultCode: "",
        beginUploadDate: null,
        endUploadDate: null,
      };
      this.resultLabel = "全部";
      this.$refs.startDateRef.clear();
      this.$refs.endDateRef.clear();
      this.$emit('select', this.form);
    },
    select() {
      this.$emit('select', this.form);
    }
  }
}
</script>

<style lang="scss" scoped>
.uni-popup-share {
  background-color: #fff;
  /*border-radius: 11px;*/
  /*margin-bottom: 41px;*/ /* 避免被底部按钮遮挡 */
}

.uni-share-content {
  display: flex;
  flex-direction: column;
}

.uni-share-content-box {
  padding: 10px 15px;
}

.uni-share-button-box {
  display: flex;
  flex-direction: row;
  padding: 10px 15px;
  border-top: 1px #f5f5f5 solid;
}

.uni-share-button {
  flex: 1;
  border-radius: 50px;
  color: #666;
  font-size: 16px;
  margin: 0 5px;
}

.whxx_all {
  width: 100%;
  height: 40px;
  line-height: 40px;
  margin-top: 10px;
}

.whxx_left {
  font-size: 14px;
  color: #333;
  font-weight: bold;
}

.whxx_all_hist {
  width: 100%;
  height: 40px;
  line-height: 40px;
  border: 1px solid #e5e5e5;
  border-radius: 5px;
  margin-top: 5px;
  padding: 0 10px;
}

.all {
  width: 100%;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #333;
}
</style>
