//------------------------开发 begin-----------------------
//后端服务url
//export const BASE_PATH = "http://192.168.181.235:8080/";

/* 'dev':'https://xnyminio.zhuisuguanjia.com/pur',
'test':'https://zb1.jlsenxiang.com/ynyp',
'prod':'https://ynypminiotwo.zhuisuguanjia.com/ynypzx', */

//耗材商城url
//export const SHOP_PATH = "https://wxtest.jikeruan.com/#/login/";
//------------------------开发 end-----------------------

//------------------------测试 begin-----------------------
//后端服务url
//export const BASE_PATH = "https://115.jlsenxiang.com/";
export const BASE_PATH = "http://127.0.0.1:8082/xmCertificate_war_exploded/";
//耗材商城url
export const SHOP_PATH = "https://wxtest.jikeruan.com/#/login/";
//------------------------测试 end-----------------------


//------------------------线上 begin-----------------------
//后端服务url
//export const BASE_PATH = "https://jlsyncphgz.zhuisuguanjia.com/";
//耗材商城url
//export const SHOP_PATH = "https://cfp.zhuisuguanjia.com/applet/#/login/";
//------------------------线上 end-----------------------

//图片网络资源url
export const IMG_PATH = "https://jlsyncphgzqn.jikeruan.com/";
//证件识别开关
export const CARD_SCAN_ENABLED = true;
//快检开关
export const INSPECTION_SITUATION_ENABLED = true;
//耗材商城开关
export const SHOP_ENABLED = true;
//阿里ocr appCode
export const OCR_CODE = "0efc5b5cbb0947d4b22510d622173129";
//技术支持
export const POWER_BY = "吉林省吉科软信息技术有限公司";
//服务电话
export const SERVICE_TEL = "************";
//承诺依据证明 是否显示快检申请
export const SHOW_FAST_REQ = true;
//祥云跳转路径对应跳转方式
export const PATH_ROUTE = {
	"/pages/ent/chooseType":"replaceAll",
	"/pages/product/product-list":"pushTab",
	"/pages/certificate/certificate":"replaceAll",
	"/pages/certificate/list":"replaceAll",
	"/pages/index/index":"replaceAll",
};