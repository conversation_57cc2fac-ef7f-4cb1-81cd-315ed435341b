/**
 * 数据脱敏工具函数
 */

/**
 * 身份证号脱敏处理
 * @param {string} idCard - 身份证号
 * @returns {string} 脱敏后的身份证号，格式：前6位 + 8个* + 后4位
 */
export function maskIdCard(idCard) {
  if (!idCard || typeof idCard !== 'string') {
    return idCard;
  }
  
  // 去除空格
  const cleanIdCard = idCard.replace(/\s/g, '');
  
  // 验证身份证号长度（15位或18位）
  if (cleanIdCard.length !== 15 && cleanIdCard.length !== 18) {
    return idCard; // 如果不是标准身份证号长度，返回原值
  }
  
  // 18位身份证脱敏：前6位 + 8个* + 后4位
  if (cleanIdCard.length === 18) {
    return cleanIdCard.substring(0, 6) + '********' + cleanIdCard.substring(14);
  }
  
  // 15位身份证脱敏：前6位 + 5个* + 后4位
  if (cleanIdCard.length === 15) {
    return cleanIdCard.substring(0, 6) + '*****' + cleanIdCard.substring(11);
  }
  
  return idCard;
}

/**
 * 手机号脱敏处理
 * @param {string} phone - 手机号
 * @returns {string} 脱敏后的手机号，格式：前3位 + 4个* + 后4位
 */
export function maskPhone(phone) {
  if (!phone || typeof phone !== 'string') {
    return phone;
  }
  
  const cleanPhone = phone.replace(/\s/g, '');
  
  if (cleanPhone.length !== 11) {
    return phone;
  }
  
  return cleanPhone.substring(0, 3) + '****' + cleanPhone.substring(7);
}

/**
 * 银行卡号脱敏处理
 * @param {string} bankCard - 银行卡号
 * @returns {string} 脱敏后的银行卡号，格式：前4位 + **** + 后4位
 */
export function maskBankCard(bankCard) {
  if (!bankCard || typeof bankCard !== 'string') {
    return bankCard;
  }
  
  const cleanCard = bankCard.replace(/\s/g, '');
  
  if (cleanCard.length < 8) {
    return bankCard;
  }
  
  const maskLength = cleanCard.length - 8;
  const maskStr = '*'.repeat(maskLength);
  
  return cleanCard.substring(0, 4) + maskStr + cleanCard.substring(cleanCard.length - 4);
}

/**
 * 姓名脱敏处理
 * @param {string} name - 姓名
 * @returns {string} 脱敏后的姓名
 */
export function maskName(name) {
  if (!name || typeof name !== 'string') {
    return name;
  }
  
  const cleanName = name.trim();
  
  if (cleanName.length <= 1) {
    return cleanName;
  }
  
  if (cleanName.length === 2) {
    return cleanName.charAt(0) + '*';
  }
  
  // 3个字符以上：保留第一个和最后一个字符，中间用*代替
  const maskLength = cleanName.length - 2;
  const maskStr = '*'.repeat(maskLength);
  
  return cleanName.charAt(0) + maskStr + cleanName.charAt(cleanName.length - 1);
}

/**
 * 邮箱脱敏处理
 * @param {string} email - 邮箱地址
 * @returns {string} 脱敏后的邮箱地址
 */
export function maskEmail(email) {
  if (!email || typeof email !== 'string') {
    return email;
  }
  
  const atIndex = email.indexOf('@');
  if (atIndex === -1) {
    return email;
  }
  
  const username = email.substring(0, atIndex);
  const domain = email.substring(atIndex);
  
  if (username.length <= 2) {
    return username.charAt(0) + '*' + domain;
  }
  
  const maskLength = username.length - 2;
  const maskStr = '*'.repeat(maskLength);
  
  return username.charAt(0) + maskStr + username.charAt(username.length - 1) + domain;
}

/**
 * 地址脱敏处理
 * @param {string} address - 地址
 * @param {number} keepLength - 保留前面多少个字符，默认6
 * @returns {string} 脱敏后的地址
 */
export function maskAddress(address, keepLength = 6) {
  if (!address || typeof address !== 'string') {
    return address;
  }
  
  const cleanAddress = address.trim();
  
  if (cleanAddress.length <= keepLength) {
    return cleanAddress;
  }
  
  return cleanAddress.substring(0, keepLength) + '****';
}

/**
 * 统一社会信用代码脱敏处理
 * @param {string} socialCode - 统一社会信用代码
 * @returns {string} 脱敏后的统一社会信用代码
 */
export function maskSocialCode(socialCode) {
  if (!socialCode || typeof socialCode !== 'string') {
    return socialCode;
  }
  
  const cleanCode = socialCode.replace(/\s/g, '');
  
  // 统一社会信用代码通常是18位
  if (cleanCode.length !== 18) {
    return socialCode;
  }
  
  // 保留前4位和后4位，中间用*代替
  return cleanCode.substring(0, 4) + '**********' + cleanCode.substring(14);
}

// 默认导出所有函数
export default {
  maskIdCard,
  maskPhone,
  maskBankCard,
  maskName,
  maskEmail,
  maskAddress,
  maskSocialCode
};
