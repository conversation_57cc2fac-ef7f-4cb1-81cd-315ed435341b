* {
    margin: 0;
    padding: 0;
    text-decoration: none;
    list-style: none;
    border: none;
}

html,
body {
    margin: 0;
    padding: 0;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
}

.bg_img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -99;
}

.logo {
    width: 100%;
    font-size: 28px;
    font-weight: bold;
    color: #fff;
    text-align: center;
    letter-spacing: 3px;
    height: 60px;
    line-height: 60px;
}

.btn-row {
    width: 70%;
    position: absolute;
    top: 55%;
    left: 15%;
}

.index_bg_img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: -99;
}

/*首页*/
._index_cont_all {
    width: 100%;
    min-height: calc(100vh - 60px - constant(safe-area-inset-bottom));
    min-height: calc(100vh - 60px - env(safe-area-inset-bottom));
}

.fr {
    float: right;
}

.fl {
    float: left;
}

.header_tit {
    width: 100%;
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    text-align: center;
    letter-spacing: 3px;
    height: 60px;
    line-height: 60px;
}

.header_list {
    width: 80%;
    margin: 0 auto;
}

.header_list ul li {
    width: 100%;
    height: 36px;
    line-height: 36px;
    color: #fff;
}

.header_list image {
    float: left;
    width: 10px;
    height: 12px;
    margin-top: 13px;
    margin-right: 10px;
}

.header_list p {
    float: left;
    font-size: 16px;
    font-weight: bold;
}

.banner {
    width: 90%;
    margin: 15px auto 0;
}

.banner_left {
    width: 49%;
    margin-right: 1%;
    height: 120px;
    background: url("https://jlsyncphgzqn.jikeruan.com/img/index/img3.png") repeat-y;
    border-radius: 10px 0 10px 0;
    background-size: 100% 100%;
    text-align: center;
    float: left;
}

.banner_tit {
    font-size: 16px;
    font-weight: bold;
    color: #fff;
}

.banner_right {
    width: 49%;
    margin-left: 1%;
    float: left;
}

.sblj {
    width: 100%;
    height: 55px;
    background: url("https://jlsyncphgzqn.jikeruan.com/img/index/img7.png");
    background-size: 100% 100%;
    margin-bottom: 10px;
    line-height: 55px;
    border-radius: 5px 0 5px 0;
}

.sblj image {
    width: 26px;
    height: 24px;
    margin: 15px 10px 0 10px;
    border-radius: 5px 0 5px 0;
}

.kjjl {
    width: 100%;
    height: 55px;
    background: url("https://jlsyncphgzqn.jikeruan.com/img/index/img8.png");
    background-size: 100% 100%;
    line-height: 55px;
}

.kjjl image {
    width: 26px;
    height: 24px;
    margin: 15px 10px 0 10px;
}

.cnyj-company {
    width: 100%;
    height: 55px;
    line-height: 55px;
    background-color: #ffffff;
}
.cnyj-company image {
    width: 26px;
    height: 24px;
    margin: 15px 10px 0 10px;
}
.cnyj-company p {

    color: #000000 !important;
}

.cpxx_all {
    width: 90%;
    margin: 0 auto;
    overflow: auto;
}

.title_all {
    width: 100%;
    height: 50px;
    line-height: 50px;

}

.title_all image {
    float: left;
    width: 8px;
    margin-top: 17px;
    margin-right: 10px;
    height: 20px;
}

.title_all p {
    float: left;
    color: #333333;
    font-size: 16px;
    font-weight: bold;
}

.cpxx_list {
    width: 100%;
}

.cpxx_list ul li {
    float: left;
    width: 49%;
    height: 70px;
    background: #fff;
    border-radius: 5px;
    box-shadow: 2px 2px 4px #ccc;
    margin-bottom: 10px;
    line-height: 70px;
}

.cpxx_list ul li image {
    float: left;
    width: 45px;
    height: 45px;
    margin: 13px 10px 0px 10px;
}

.cpxx_list ul li p {
    float: left;
    font-size: 12px;
    color: #333333;
}

/*我的*/
.my_top {
    width: 100%;
    margin: 0 auto 20px;
    height: 230px;
    background: url("https://jlsyncphgzqn.jikeruan.com/img/user_top.png") no-repeat;
    background-size: 100% 100%;
    overflow: auto;
}

.my_photo {
    width: 90px;
    height: 90px;
    border-radius: 50px;
    background: #9cdeb2;
    overflow: hidden;
    margin: 30px auto;
}

.my_phtot_img {
    width: 80px;
    height: 80px;
    margin: 5px auto;
    /* margin: 7px auto; */
    border-radius: 43px;
    /* background: #f47b42; */
}

.my_phtot_img img {
    width: 100%;
    height: 100%;
}

.my_phtot_p {
    width: 100%;
    text-align: center;
    color: #fff;
}

/* .my_phtot_p p {
    margin: 15px 0 0;
} */
.sfm {
    width: 70%;
    height: 120px;
    background: url("https://jlsyncphgzqn.jikeruan.com/img/index/img19.png") repeat-x;
    border-radius: 5px;
    margin: 10px auto 30px;
}

.sfm_left {
    width: 49%;
    float: left;
}

.sfm_cont {
    width: 100%;
    border-right: 1px solid #b9d2cb;
    height: 90px;
    margin: 15px auto;
    text-align: center;
}

.sfm_cont img {
    width: 50px;
    margin: 10px auto;
}

.sfm_cont p {
    color: #333333;
    font-size: 14px;
}

.sfm_right {
    float: left;
    width: 50%;
}

.sfm_ewm {
    width: 80px;
    height: 80px;
    margin: 20px auto;
}

.sfm_ewm img {
    width: 100%;
    height: 100%;
}

.my_list {
    width: 70%;
    margin: 0 auto;
}

.my_list ul li {
    width: 100%;
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #D0D0D0;
}

.my_list ul li img {
    float: right;
    width: 8px;
    margin-top: 20px;
}

.my_list ul li p {
    float: left;
    color: #333333;
    font-size: 14px;
}

.tcdl {
    width: 60%;
    margin: 50px auto 0;
    height: 35px;
    line-height: 35px;
    background: #35b37b;
    border-radius: 3px;
    color: #fff;
}

.tcdl input {
    width: 100%;
    height: 100%;
    background: none;
    border: none;
    color: #fff;
    font-size: 16px;
}

.whztxx_all {
    width: 80%;
    margin: 0 auto;
}

.whztxx_cont {
    width: 100%;
    height: 118px;
    border: 1px solid #f0f0f0;
    box-shadow: 2px 2px 4px #ccc;
    background: #fff;
    margin: 20px auto 0;
    border-radius: 5px;
}

.whztxx_img {
    width: 35%;
    float: left;
    text-align: center;
}

.whztxx_img image {
    width: 74px;
    margin-top: 22px;
    height: 74px;
}

.whztxx_tit {
    width: 55%;
    float: left;
    line-height: 118px;
}

.whztxx_tit p {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    float: left;
}

.whztxx_tit image {
    float: right;
    width: 8px;
    margin-top: 52px;
}

.whztxx_title {
    width: 100%;
    padding: 20px 0 0;
    color: #999999;
}

.whztxx_all {
    width: 80%;
    margin: 0 auto;
}

.whztxx_cont {
    width: 100%;
    height: 118px;
    border: 1px solid #f0f0f0;
    box-shadow: 2px 2px 4px #ccc;
    background: #fff;
    margin: 20px auto 0;
    border-radius: 5px;
}

.whztxx_img {
    width: 40%;
    float: left;
    text-align: center;
}

.whztxx_img img {
    width: 74px;
    height: 74px;
    margin-top: 22px;
}

.whztxx_tit {
    width: 55%;
    float: left;
    line-height: 118px;
}

.whztxx_tit p {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    float: left;
}

.whztxx_tit img {
    float: right;
    width: 8px;
    margin-top: 52px;
    height: 16px;
}

.whztxx_title {
    width: 100%;
    padding: 20px 0 0;
    color: #999999;
    font-size: 12px;
}

.whxx_tit {
    width: 90%;
    margin: 0 auto;
    overflow: auto;
}

.whxx_tit ul li {
    width: 50%;
    float: left;
    height: 45px;
    line-height: 45px;
}

.whxx_tit ul li p {
    font-size: 16px;
    color: #333333;
    font-weight: bold;
}

.whxx_tit ul li p span {
    color: #3dad70;
}

.content_all {
    width: 90%;
    margin: 10px auto;
    box-shadow: 2px 2px 4px #ccc;
    background: #fff;
    overflow: auto;
}

.whxx_all_hist {
    width: 96%;
    margin: 0 auto;
    overflow: auto;
    border-bottom: 1px solid #e0e0e0;
}

.whxx_all {
    width: 96%;
    margin: 0 auto;
    overflow: auto;
    border-bottom: 1px solid #e0e0e0;
    padding: 10px 0;
    display: flex;
    align-items: center;
}

.whxx_left {
    width: 45%;
    font-size: 14px;
    color: #333333;
    font-weight: bold;
}

.whxx_left span {
    color: #ff0000;
    /* padding-left: 5px; */
}

.whxx_right {
    width: 55%;
    font-size: 12px;
    color: #888888;
    word-wrap: break-word;
}

.whxx_right input {
    width: 100%;
}

.pboto_left {
    float: left;
    width: 30%;
}

.pboto_right {
    float: left;
    width: 100%;
}

.submit_btn {
    width: 70%;
    margin: 0 auto;
    background: #2da871;
    height: 45px;
    border-radius: 5px;
    overflow: auto;
}

.footer_tit {
    width: 100%;
    margin: 15px auto;
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-size: 12px;
    color: #888888;
}

.cpxx_title {
    width: 96%;
    margin: 0 auto;
    height: 40px;
    background: #02994f;
    border-radius: 3px;
    line-height: 40px;
    color: #fff;
    font-size: 14px;
}

.date_all {
    width: 25%;
    float: left;
}

.cpxx_left {
    float: left;
}

.cpxx_right {
    float: left;
}

.number_all {
    width: 25%;
    float: left;
}

.cppl_all {
    width: 35%;
    float: left;
    text-align: center;
    background: #fff;
    color: #999999;
    height: 30px;
    margin: 5px auto;
    line-height: 30px;
}

.manage_all {
    width: 15%;
    float: left;
    text-align: center;
}

.jt_all {
    width: 7px;
    height: 7px;
}

.add_all {
    width: 200px;
    height: 140px;
    position: absolute;
    top: 50%;
    margin-top: -70px;
    left: 50%;
    margin-left: -100px;
}

.add_top {
    width: 110px;
    height: 30px;
    margin: 0 auto 16px;
    text-align: center;
    color: #999999;
}

.add_top image {
    width: 25px;
    height: 25px;
    float: left;
    margin: 5px 10px 0;
}

.add_top p {
    float: left;
}

.add_bottom {
    width: 100%;
    text-align: center;
    line-height: 46px;
    color: #999999;
}

.tit_left {
    /* padding: 10px 0; */
    font-size: 14px;
    color: #333333;
    font-weight: bold;
}

.kjjl_top {
    width: 90%;
    text-align: center;
    height: 150px;
    margin: 20px auto;
    background: url("https://jlsyncphgzqn.jikeruan.com/img/kjjl_bg.png") no-repeat;
    background-size: 100% 100%;
}

.kj_num {
    width: 100%;
    height: 60px;
    line-height: 60px;
    color: #333;
    overflow: hidden;
}

.kj_num image {
    float: left;
    margin: 18px 10% 0;
    width: 32px;
    height: 33px;

}

.kj_num p {
    float: left;
    font-size: 18px;
}

.kj_number {
    width: 100%;
    height: 40px;
    line-height: 40px;
    color: #888;
    font-size: 14px;
    text-align: center;
}

.kjjl_list {
    width: 96%;
    margin: 0 auto;
    border-bottom: 1px solid #d0d0d0;
}

.title_tit {
    padding: 10px 0;
    font-size: 14px;
    color: #333333;
    font-weight: bold;
}

.page_all {
    width: 90%;
    margin: 15px auto;
    ;
}

.page_top {
    width: 100%;
    margin: 15px auto;
    font-size: 16px;
    color: #000000;
    text-align: center;
}

.page_title {
    width: 100%;
    text-indent: 24px;
    font-size: 14px;
    color: #01BE6E;
    font-weight: bold;
}

.page_title p {
    line-height: 30px;
}

.page_tit {
    width: 100%;
    text-indent: 24px;
    font-size: 14px;
    color: #000;
    line-height: 22px;
}

.page_ts {
    width: 100%;
    font-size: 14px;
    color: #01BE6E;
    line-height: 22px;
}

.dysb_list {
    width: 90%;
    margin: 20px auto;
    border-top: 1px solid #BBBBBB;
}

.dysb_all {
    width: 100%;
    border: 1px solid #BBBBBB;
    border-top: none;
    overflow: auto;
}

.dysb_left {
    width: 39%;
    float: left;
    text-align: center;
}

.dysb_right {
    width: 60%;
    float: left;
    border-left: 1px solid #BBBBBB;
}

.dysb_left image {
    width: 100px;
    height: 80px;
    margin-top: 50px;

}

.dysb_tit {
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: bold;
    color: #333;
    padding: 10px;
    border-bottom: 1px solid #BBBBBB;
}

.dysb_title {
    width: 96%;
    margin: 0 auto;
    ext-align: left;
    font-size: 14px;
    padding: 5px;
}


.select-pro {
    min-height: 100vh;
    background: #f4f4f4;
}

.select-pro .pro-main {
    /* position: fixed;
    overflow: auto;
    bottom: 60px;
    right: 0;
    top: 50px;
    left: 0; */
    background: #F4F4F4;
}

.select-pro .pro-main .pro-item {
    height: 80px;
    background: #fff;
    margin: 10px;
    border-radius: 8px;
    display: flex;
    padding: 10px;
}

.select-pro .pro-main .pro-item .img-bor {
    width: 100px;
    height: 80px;
    background: #ccc;
    border-radius: 8px;
    overflow: hidden;
}

.select-pro .pro-main .pro-item .img-bor image {
    width: 100px;
    height: 80px;
}

.select-pro .pro-main .pro-item .info {
    width: calc(100% - 130px);
    margin-left: 10px;
}

.select-pro .pro-main .pro-item .info .name {
    font-size: 16px;
    font-weight: bold;
}

.select-pro .pro-main .pro-item .info .inve {
    color: rgba(0, 0, 0, 0.4);
    font-size: 12px;
    margin-top: 10px;
}

.select-pro .pro-main .pro-item .info .inve span {
    padding: 2px 5px;
    background: #029850;
    color: #fff;
    font-size: 12px;
    border-radius: 6px;
    margin-right: 5px;
}

.select-pro .pro-main .pro-item .info .price {
    margin-top: 10px;
    color: #f60;
    margin-top: 10px;
    font-size: 12px;
}

.select-pro .pro-main .pro-item .info span {
    font-size: 14px;
}

.select-pro .pro-main .pro-item .radio {
    margin-top: 30px;
    text-align: center;
    width: 40px;
}

.select-pro .btm {
    background: #fff;
    height: 60px;
    position: fixed;
    bottom: 0px;
    left: 0px;
    right: 0px;
}

.select-pro .btm .button {
    height: 44px;
    line-height: 44px;
    color: #fff;
    text-align: center;
    margin: 8px 40px;
    background: #029850;
}

.pro-search {
    height: 30px;
    padding: 10px 20px;
    background: #fff;
}

.pro-search .input-wrap {
    height: 30px;
    background: #F7F8FA;
    border-radius: 15px;
    display: flex;
}

.pro-search .input-wrap .icon-search {
    width: 20px;
    height: 20px;
    padding: 5px 5px 5px 10px;
}

.pro-search .input-wrap .icon-search image {
    width: 100%;
}

.pro-search .input-wrap .icon-close {
    width: 20px;
    height: 20px;
    padding: 5px 10px 5px 5px;
}

.pro-search .input-wrap .icon-close image {
    width: 100%;
}

.pro-search .uni-input {
    height: 30px;
    background: transparent;
    flex: 1;
    border-radius: 15px;
    font-size: 14px;
    text-align: left;
    padding-left: 10px;

}

.fg_icon {
    width: 20px;
    height: 20px;
    float: left;
}

.fg_icon image {
    width: 20px;
    height: 20px;
}

.index-go-btn {
    width: 60px;
    height: 60px;
    position: fixed;
    right: 20px;
    bottom: calc(70px + constant(safe-area-inset-bottom));
    bottom: calc(70px + env(safe-area-inset-bottom));
    z-index: 9999;
}

.re-buy-main {
    width: 80vw;
    background: #fff;
    padding: 20px;
}

.re-buy-main .title {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
}

.re-buy-main .title span {
    float: right;
    font-size: 20px;
    font-family: Arial, Helvetica, sans-serif;
}

.re-buy-main .title image {
    width: 25px;
}

.re-buy-main .info p {
    line-height: 30px;
    font-size: 16px;
    font-weight: bold;
}

.re-buy-main image {
    width: 100%;
    display: block;
}

.ynyp-btn1 {
    margin: 15px 15px 0px 15px;
    text-align: center;
    line-height: 40px;

}

.ynyp-btn1 image {
    width: 100%;
}